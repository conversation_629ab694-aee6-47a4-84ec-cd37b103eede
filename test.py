import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, Ellipse, ConnectionPatch
import matplotlib.gridspec as gridspec
import os
from datetime import datetime

def create_architecture_diagram(save_output=True, output_dir="output", filename_prefix="architecture_diagram"):
    """
    创建机器学习架构图并可选择保存输出

    Args:
        save_output (bool): 是否保存输出文件
        output_dir (str): 输出目录
        filename_prefix (str): 文件名前缀
    """
    print("开始创建架构图...")

    # 确保输出目录存在
    if save_output and not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建输出目录: {output_dir}")

    # 创建一个新的图形
    fig = plt.figure(figsize=(18, 10))

# 设置全局字体
plt.rcParams['font.family'] = 'Arial'
plt.rcParams['font.size'] = 10

# 创建网格布局
gs = gridspec.GridSpec(3, 3)

# 特征提取部分
ax1 = plt.subplot(gs[0, 0])
ax1.text(0.5, 0.9, 'Feature Extraction', ha='center', va='center', fontsize=12, fontweight='bold')
ax1.axis('off')

# 文本流
ax2 = plt.subplot(gs[1, 0])
ax2.text(0.1, 0.8, '(Text Stream) X_t', ha='left', va='center')
ax2.text(0.1, 0.6, 'I thought that film was awful.', ha='left', va='center')
ax2.add_patch(Rectangle((0.1, 0.4), 0.25, 0.15, fill=True, facecolor='#A8CC7F', edgecolor='black', linewidth=1))
ax2.text(0.225, 0.475, 'Transformer', ha='center', va='center')
ax2.add_patch(Ellipse((0.35, 0.45), 0.05, 0.1, fill=True, facecolor='#F0F0F0', edgecolor='black', linewidth=1))
ax2.text(0.35, 0.45, 'Z_t', ha='center', va='center')
ax2.axis('off')

# 音频流
ax3 = plt.subplot(gs[2, 0])
ax3.text(0.1, 0.8, '(Audio Stream) X_a', ha='left', va='center')
ax3.add_patch(Rectangle((0.1, 0.4), 0.25, 0.15, fill=True, facecolor='#A8CC7F', edgecolor='black', linewidth=1))
ax3.text(0.225, 0.475, 'Transformer', ha='center', va='center')
ax3.add_patch(Ellipse((0.35, 0.45), 0.05, 0.1, fill=True, facecolor='#F0F0F0', edgecolor='black', linewidth=1))
ax3.text(0.35, 0.45, 'Z_a', ha='center', va='center')
ax3.axis('off')

# 表示学习部分
ax4 = plt.subplot(gs[:, 1])
ax4.text(0.5, 0.9, 'Representation Learning', ha='center', va='center', fontsize=12, fontweight='bold')
ax4.add_patch(Rectangle((0.2, 0.6), 0.2, 0.15, fill=True, facecolor='#F3D584', edgecolor='black', linewidth=1))
ax4.text(0.3, 0.675, 'Common Encoder', ha='center', va='center')
ax4.add_patch(Ellipse((0.45, 0.65), 0.08, 0.12, fill=True, facecolor='#F3D584', edgecolor='black', linewidth=1, linestyle='--'))
ax4.text(0.45, 0.65, 'C_t', ha='center', va='center')
ax4.add_patch(Ellipse((0.45, 0.5), 0.08, 0.12, fill=True, facecolor='#A8CC7F', edgecolor='black', linewidth=1, linestyle='--'))
ax4.text(0.45, 0.5, 'P_t', ha='center', va='center')
ax4.add_patch(Rectangle((0.4, 0.35), 0.2, 0.15, fill=True, facecolor='#A8CC7F', edgecolor='black', linewidth=1))
ax4.text(0.5, 0.425, 'Modality Discriminator', ha='center', va='center')
ax4.axis('off')

# 跨模态注意力融合模块
ax5 = plt.subplot(gs[:, 2])
ax5.text(0.5, 0.9, 'Cross-Modal Attention Fusion Module', ha='center', va='center', fontsize=12, fontweight='bold')
ax5.add_patch(Rectangle((0.1, 0.7), 0.25, 0.15, fill=True, facecolor='#C45E5E', edgecolor='black', linewidth=1))
ax5.text(0.225, 0.775, 'Crossmodal Attention', ha='center', va='center')
ax5.add_patch(Rectangle((0.1, 0.5), 0.25, 0.15, fill=True, facecolor='#F3CE8C', edgecolor='black', linewidth=1))
ax5.text(0.225, 0.575, 'Feed-forward Layer', ha='center', va='center')
ax5.add_patch(Rectangle((0.1, 0.3), 0.25, 0.15, fill=True, facecolor='#F3CE8C', edgecolor='black', linewidth=1))
ax5.text(0.225, 0.375, 'LayerNorm', ha='center', va='center')
ax5.add_patch(Rectangle((0.4, 0.2), 0.25, 0.15, fill=True, facecolor='#A8CC7F', edgecolor='black', linewidth=1))
ax5.text(0.525, 0.275, 'Concatenation', ha='center', va='center')
ax5.axis('off')

# 添加箭头
ax2.annotate('', xy=(0.35, 0.55), xytext=(0.35, 0.45), arrowprops=dict(arrowstyle='->'))
ax3.annotate('', xy=(0.35, 0.55), xytext=(0.35, 0.45), arrowprops=dict(arrowstyle='->'))
ax4.annotate('', xy=(0.35, 0.6), xytext=(0.45, 0.65), arrowprops=dict(arrowstyle='->'))
ax4.annotate('', xy=(0.35, 0.6), xytext=(0.45, 0.5), arrowprops=dict(arrowstyle='->'))
ax5.annotate('', xy=(0.35, 0.7), xytext=(0.1, 0.7), arrowprops=dict(arrowstyle='->'))
ax5.annotate('', xy=(0.35, 0.55), xytext=(0.1, 0.55), arrowprops=dict(arrowstyle='->'))
ax5.annotate('', xy=(0.35, 0.4), xytext=(0.1, 0.4), arrowprops=dict(arrowstyle='->'))
ax5.annotate('', xy=(0.6, 0.3), xytext=(0.4, 0.3), arrowprops=dict(arrowstyle='->'))

# 添加文本连接
ax4.text(0.3, 0.6, 'I(;θ_I)', ha='center', va='center')
ax4.text(0.3, 0.5, 'S_t(;θ_t)', ha='center', va='center')
ax4.text(0.3, 0.4, 'S_a(;θ_a)', ha='center', va='center')
ax4.text(0.3, 0.3, 'S_v(;θ_v)', ha='center', va='center')
ax5.text(0.1, 0.2, 'F_concat = [C, P_t, P_a, P_v]', ha='left', va='center')

# 添加损失函数
ax4.text(0.2, 0.2, 'L_con', ha='left', va='center', fontweight='bold')
ax4.text(0.4, 0.2, 'L_ami', ha='left', va='center', fontweight='bold')
ax4.text(0.6, 0.2, 'L_ams', ha='left', va='center', fontweight='bold')
ax4.text(0.8, 0.2, 'L_dis', ha='left', va='center', fontweight='bold')
ax5.text(0.7, 0.15, 'L_task', ha='left', va='center', fontweight='bold')

# 调整子图间距
plt.subplots_adjust(wspace=0.4, hspace=0.4)

# 显示图形
plt.show()