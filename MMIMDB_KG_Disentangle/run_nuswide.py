"""
<PERSON><PERSON>t to run the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network on NUS-WIDE dataset.
"""

import os
import argparse
import torch
import logging
import json
from datetime import datetime

from train_nuswide import train_nuswide
from utils.nuswide_kg_constructor import NUSWIDEKnowledgeGraphConstructor
from configs.nuswide_config import NUSWIDE_DATASET_CONFIG, NUSWIDE_MODEL_CONFIG, NUSWIDE_TRAINING_CONFIG

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run KG-Disentangle-Net on NUS-WIDE")

    # Mode arguments
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'test', 'build_kg'],
                        help='Mode to run the script in')

    # Data arguments
    parser.add_argument('--data_path', type=str, default=NUSWIDE_DATASET_CONFIG['data_path'],
                        help='Path to NUS-WIDE dataset')
    parser.add_argument('--kg_path', type=str, default=NUSWIDE_DATASET_CONFIG['kg_path'],
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output_nuswide',
                        help='Output directory for saving models and logs')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=NUSWIDE_DATASET_CONFIG['text_dim'],
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=NUSWIDE_DATASET_CONFIG['visual_dim'],
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=NUSWIDE_DATASET_CONFIG['kg_dim'],
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=NUSWIDE_MODEL_CONFIG['hidden_dim'],
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=NUSWIDE_DATASET_CONFIG['num_classes'],
                        help='Number of output classes')

    # Training arguments
    parser.add_argument('--batch_size', type=int, default=NUSWIDE_TRAINING_CONFIG['batch_size'],
                        help='Batch size for training')
    parser.add_argument('--num_epochs', type=int, default=NUSWIDE_TRAINING_CONFIG['num_epochs'],
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=NUSWIDE_TRAINING_CONFIG['lr'],
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=NUSWIDE_TRAINING_CONFIG['weight_decay'],
                        help='Weight decay')
    parser.add_argument('--seed', type=int, default=NUSWIDE_TRAINING_CONFIG['seed'],
                        help='Random seed')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--sample_ratio', type=float, default=1.0,
                        help='Ratio of data to sample (for low-resource experiments)')

    # Experiment arguments
    parser.add_argument('--exp_name', type=str, default=None,
                        help='Experiment name')
    parser.add_argument('--notes', type=str, default='',
                        help='Notes for the experiment')

    return parser.parse_args()

def build_kg(args):
    """Build the knowledge graph for NUS-WIDE."""
    logger.info("Building knowledge graph for NUS-WIDE...")

    # Create output directory
    os.makedirs(args.kg_path, exist_ok=True)

    # Build knowledge graph
    kg_constructor = NUSWIDEKnowledgeGraphConstructor(
        data_path=args.data_path,
        output_path=args.kg_path
    )

    kg = kg_constructor.construct()

    logger.info(f"Knowledge graph built with {len(kg['entities'])} entities and {len(kg['triples'])} triples")

    return kg

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create experiment name if not provided
    if args.exp_name is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.exp_name = f"nuswide_kg_disentangle_{timestamp}"

    # Create output directory
    args.output_dir = os.path.join(args.output_dir, args.exp_name)
    os.makedirs(args.output_dir, exist_ok=True)

    # Save arguments
    with open(os.path.join(args.output_dir, 'args.json'), 'w') as f:
        json.dump(vars(args), f, indent=2)

    # Run in the specified mode
    if args.mode == 'build_kg':
        # Set build_kg flag for train.py
        args.build_kg = True
        build_kg(args)
    elif args.mode == 'train':
        # Set build_kg flag to False by default
        args.build_kg = False

        # Build knowledge graph if needed
        if not os.path.exists(os.path.join(args.kg_path, 'nuswide_knowledge_graph.pkl')):
            logger.info("Knowledge graph not found, building it first...")
            build_kg(args)

        # Train the model
        train_nuswide(args)
    elif args.mode == 'test':
        # Set build_kg flag to False for test mode
        args.build_kg = False

        # Ensure model exists
        model_path = os.path.join(args.output_dir, 'best_model.pth')
        if not os.path.exists(model_path):
            logger.error(f"Model not found at {model_path}")
            return

        # Test the model
        args.do_train = False
        train_nuswide(args)
    else:
        logger.error(f"Unknown mode: {args.mode}")

if __name__ == '__main__':
    main()
