"""
Dataset loader for MM-IMDB dataset with knowledge graph integration.
"""

import os
import json
import torch
import numpy as np
import random
from PIL import Image
import re
from torch.utils.data import Dataset, DataLoader
from collections import OrderedDict, Counter
from sklearn.preprocessing import MultiLabelBinarizer
import pickle

class MMIMDBDataset(Dataset):
    """
    Dataset class for MM-IMDB with knowledge graph integration.
    """

    def __init__(self, data_path, kg_path, mode='train', transform=None, max_seq=40, sample_ratio=1.0):
        """
        Initialize the dataset.

        Args:
            data_path (str): Path to the MM-IMDB dataset
            kg_path (str): Path to the knowledge graph data
            mode (str): Dataset split ('train', 'val', or 'test')
            transform (callable, optional): Optional transform to be applied on images
            max_seq (int): Maximum sequence length for text
            sample_ratio (float): Ratio of data to sample (for low-resource experiments)
        """
        self.data_path = data_path
        self.kg_path = kg_path
        self.mode = mode
        self.transform = transform
        self.max_seq = max_seq
        self.sample_ratio = sample_ratio

        # Load movie data
        self.load_data()

        # Load knowledge graph
        self.load_knowledge_graph()

        # Set up preprocessor for images
        self.setup_preprocessor()

    def load_data(self):
        """Load and preprocess the dataset."""
        print(f"Loading {self.mode} data...")

        # Load movie list
        with open(os.path.join(self.data_path, 'list.txt'), 'r') as f:
            files = f.read().splitlines()

        # Load movies
        self.vocab_counts = []
        self.movies = []
        n_classes = 23  # Number of genre classes

        for file in files:
            # The file path in list.txt already includes 'dataset/', so we don't need to add it again
            json_path = os.path.join(self.data_path, file)
            if os.path.exists(json_path):
                with open(json_path, 'r') as f:
                    data = json.load(f)
                    # Extract the ID from the file path (remove 'dataset/' prefix and '.json' suffix)
                    data['imdb_id'] = os.path.basename(file).split('.')[0]

                    # Check if image exists
                    img_path = os.path.join(self.data_path, 'dataset', f"{data['imdb_id']}.jpg")
                    if os.path.exists(img_path) and 'genres' in data and 'plot' in data:
                        # Normalize text
                        data['plot'] = self.normalize_text(data['plot'])
                        if len(data['plot']) > 0:
                            self.vocab_counts.extend(data['plot'].split())
                            self.movies.append(data)

        # Get genre counts and target names
        counts = OrderedDict(Counter([g for m in self.movies for g in m['genres']]).most_common())
        self.target_names = list(counts.keys())[:n_classes]

        # Create multi-label binarizer
        le = MultiLabelBinarizer()
        self.Y = le.fit_transform([m['genres'] for m in self.movies])
        self.le = le
        self.labels = np.nonzero(le.transform([[t] for t in self.target_names]))[1]
        self.gt = self.Y[:, self.labels]

        # Split data
        index = list(range(len(self.movies)))
        random.seed(42)  # For reproducibility
        random.shuffle(index)

        train_len = int(len(self.movies) * 0.6)
        test_len = int(len(self.movies) * 0.3)

        train_data = [self.movies[index[i]] for i in range(train_len)]
        train_labels = [self.gt[index[i]] for i in range(train_len)]

        test_data = [self.movies[index[i]] for i in range(train_len, train_len + test_len)]
        test_labels = [self.gt[index[i]] for i in range(train_len, train_len + test_len)]

        val_data = [self.movies[index[i]] for i in range(train_len + test_len, len(index))]
        val_labels = [self.gt[index[i]] for i in range(train_len + test_len, len(index))]

        # Save split information
        with open(os.path.join(self.data_path, 'train_ids.txt'), 'w') as f:
            for item in train_data:
                f.write(f"{item['imdb_id']}\n")

        with open(os.path.join(self.data_path, 'test_ids.txt'), 'w') as f:
            for item in test_data:
                f.write(f"{item['imdb_id']}\n")

        with open(os.path.join(self.data_path, 'val_ids.txt'), 'w') as f:
            for item in val_data:
                f.write(f"{item['imdb_id']}\n")

        # Set data based on mode
        if self.mode == 'train':
            self.samples = train_data
            self.gengt = train_labels
        elif self.mode == 'test':
            self.samples = test_data
            self.gengt = test_labels
        else:  # val
            self.samples = val_data
            self.gengt = val_labels

        # Apply sampling if needed
        if self.sample_ratio < 1.0:
            num_samples = int(len(self.samples) * self.sample_ratio)
            indices = random.sample(range(len(self.samples)), num_samples)
            self.samples = [self.samples[i] for i in indices]
            self.gengt = [self.gengt[i] for i in indices]

        print(f"Loaded {len(self.samples)} samples for {self.mode}")

    def load_knowledge_graph(self):
        """Load knowledge graph data."""
        print("Loading knowledge graph...")

        # Load entity and relation mappings
        kg_pickle_path = os.path.join(self.kg_path, 'knowledge_graph.pkl')

        if os.path.exists(kg_pickle_path):
            with open(kg_pickle_path, 'rb') as f:
                kg_data = pickle.load(f)
                self.entity_id = kg_data['entities']
                self.relations = kg_data['relations']
                self.triples = kg_data['triples']
        else:
            # Load entity mappings
            self.entity_id = {}
            with open(os.path.join(self.kg_path, 'entity2id.txt'), 'r') as f:
                lines = f.readlines()[1:]  # Skip header
                for line in lines:
                    parts = line.strip().split('\t')
                    if len(parts) == 2:
                        self.entity_id[parts[0]] = int(parts[1])

        # Load KG embeddings if available
        kg_embed_path = os.path.join(self.kg_path, 'entity_embeddings.json')
        if os.path.exists(kg_embed_path):
            with open(kg_embed_path, 'r') as f:
                self.kg_embeddings = json.load(f)
        else:
            # Initialize random embeddings if not available
            print("KG embeddings not found, initializing random embeddings")
            self.kg_embeddings = {
                str(entity_id): np.random.randn(200).tolist()  # 200-dim embeddings
                for entity, entity_id in self.entity_id.items()
            }

    def setup_preprocessor(self):
        """Set up image preprocessor."""
        from torchvision import transforms

        self.preprocessor = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225])
        ])

    def normalize_text(self, text):
        """Normalize text data."""
        text = text.lower()
        text = re.sub(r'<br />', r' ', text).strip()
        text = re.sub(r'^https?:\/\/.*[\r\n]*', ' L ', text, flags=re.MULTILINE)
        text = re.sub(r'[\~\*\+\^`_#\[\]|]', r' ', text).strip()
        text = re.sub(r'[0-9]+', r' N ', text).strip()
        text = re.sub(r'([/\'\-\.?!\(\)",:;])', r' \1 ', text).strip()
        return text

    def __len__(self):
        """Get dataset length."""
        return len(self.samples)

    def __getitem__(self, idx):
        """Get a sample from the dataset."""
        # Get text features
        text = self.samples[idx]['plot']

        # Get image features
        img_path = os.path.join(self.data_path, 'dataset', f"{self.samples[idx]['imdb_id']}.jpg")
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        else:
            image = self.preprocessor(image)

        # Get knowledge graph features
        kg_features = []
        kg_weight = 0
        i = 0

        # Get label embeddings
        label_embeddings = np.array([
            self.kg_embeddings[str(i)] for i in range(23)
            if str(i) in self.kg_embeddings
        ])

        # Get actor embeddings
        if 'cast' in self.samples[idx]:
            for actor in self.samples[idx]['cast']:
                if actor in self.entity_id and str(self.entity_id[actor]) in self.kg_embeddings:
                    kg_features.append(self.kg_embeddings[str(self.entity_id[actor])])
                    i += 1

        # Get director embeddings
        if 'director' in self.samples[idx]:
            director = self.samples[idx]['director']
            if director in self.entity_id and str(self.entity_id[director]) in self.kg_embeddings:
                kg_features.append(self.kg_embeddings[str(self.entity_id[director])])
                i += 1

        # Combine KG features
        if len(kg_features) == 0:
            kg_features = np.zeros(200)  # Default dimension
        else:
            kg_features = np.mean(kg_features, axis=0)

        # Calculate KG weight
        kg_weight = i / (i + 6) if i > 0 else 0

        return {
            'image': image,
            'text': text,
            'labels': torch.FloatTensor(self.gengt[idx]),
            'kg_features': torch.FloatTensor(kg_features),
            'label_embeddings': torch.FloatTensor(label_embeddings),
            'kg_weight': kg_weight,
            'imdb_id': self.samples[idx]['imdb_id']
        }
