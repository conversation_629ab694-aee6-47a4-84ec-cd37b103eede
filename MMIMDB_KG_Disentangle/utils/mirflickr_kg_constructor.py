"""
Knowledge Graph Constructor for MIR-Flickr dataset.

This module constructs a knowledge graph from the MIR-Flickr dataset metadata,
following a similar approach to the MM-IMDB KG constructor but adapted for
the MIR-Flickr dataset structure.
"""

import os
import json
import numpy as np
import torch
from tqdm import tqdm
import pickle
from collections import Counter

class MIRFlickrKnowledgeGraphConstructor:
    """
    Constructs a knowledge graph from MIR-Flickr dataset metadata.
    """

    def __init__(self, data_path, output_path):
        """
        Initialize the knowledge graph constructor.

        Args:
            data_path (str): Path to the MIR-Flickr dataset
            output_path (str): Path to save the constructed knowledge graph
        """
        self.data_path = data_path
        self.output_path = output_path

        # Concept mapping (38 concepts in MIR-Flickr)
        # These are placeholder names - replace with actual concept names if available
        self.concepts = {
            'animals': 0, 'baby': 1, 'bird': 2, 'car': 3, 'clouds': 4, 
            'dog': 5, 'female': 6, 'flower': 7, 'food': 8, 'indoor': 9, 
            'lake': 10, 'male': 11, 'night': 12, 'people': 13, 'plant_life': 14, 
            'portrait': 15, 'river': 16, 'sea': 17, 'sky': 18, 'structures': 19, 
            'sunset': 20, 'transport': 21, 'tree': 22, 'water': 23, 'cityscape': 24, 
            'landscape': 25, 'night_time': 26, 'still_life': 27, 'family': 28, 
            'group': 29, 'natural': 30, 'party': 31, 'sport': 32, 'travel': 33, 
            'wedding': 34, 'beach': 35, 'mountain': 36, 'urban': 37
        }

        # Initialize entity dictionary with concepts
        self.entities = self.concepts.copy()
        self.entity_count = len(self.concepts)

        # Relation types
        self.relations = {
            'image_tag': 0,
            'image_concept': 1,
            'tag_concept': 2,
            'co_occurrence': 3  # For concept co-occurrence
        }

    def load_data(self, split_file=None):
        """
        Load data from the dataset.

        Args:
            split_file (str, optional): Path to a file containing image IDs for a specific split

        Returns:
            list: List of data items
        """
        # Define paths for MIR-Flickr dataset
        images_dir = os.path.join(self.data_path, 'images')
        tags_path = os.path.join(self.data_path, 'tags')
        annotations_path = os.path.join(self.data_path, 'annotations')

        # Load image list
        image_files = [f for f in os.listdir(images_dir) if f.endswith('.jpg')]
        
        # Load tags (if available)
        tags_dict = {}
        if os.path.exists(tags_path):
            for tag_file in os.listdir(tags_path):
                if tag_file.endswith('.txt'):
                    image_id = tag_file.split('.')[0]
                    with open(os.path.join(tags_path, tag_file), 'r', encoding='utf-8', errors='ignore') as f:
                        tags = f.read().strip().split()
                        tags_dict[image_id] = tags

        # Load annotations (38 concepts)
        annotations = {}
        
        # MIR-Flickr has 38 concept annotations
        for i in range(1, 39):
            concept_file = os.path.join(annotations_path, f'annotation_{i}.txt')
            if os.path.exists(concept_file):
                concept_name = list(self.concepts.keys())[i-1]  # Map to concept name
                
                with open(concept_file, 'r') as f:
                    lines = f.readlines()
                    for j, line in enumerate(lines):
                        image_id = f'im{j+1}'
                        if image_id not in annotations:
                            annotations[image_id] = []
                        
                        # Parse annotation (1 for positive, 0 for negative, -1 for unknown)
                        value = int(line.strip())
                        if value == 1:
                            annotations[image_id].append(i-1)  # Store concept index

        # Prepare data
        data = []
        for image_file in image_files:
            # Extract image ID
            image_id = image_file.split('.')[0]
            
            # Get tags
            tags = tags_dict.get(image_id, [])
            
            # Get concept labels
            concept_labels = annotations.get(image_id, [])
            
            # Check if image exists
            img_path = os.path.join(images_dir, image_file)
            if os.path.exists(img_path) and len(concept_labels) > 0:
                data.append({
                    'image_id': image_id,
                    'image_path': image_file,
                    'tags': tags,
                    'concepts': concept_labels
                })

        # If split file is provided, filter data
        if split_file and os.path.exists(split_file):
            with open(split_file, 'r') as f:
                split_ids = [line.strip() for line in f.readlines()]
            data = [item for item in data if item['image_id'] in split_ids]

        return data

    def build_entity_dictionary(self, data):
        """
        Build a dictionary of entities (images, tags, concepts).

        Args:
            data (list): List of data items

        Returns:
            dict: Dictionary mapping entity names to IDs
        """
        print("Building entity dictionary...")

        # Add images to entities
        for item in tqdm(data):
            # Add image to entities
            self.entities[item['image_id']] = self.entity_count
            self.entity_count += 1

            # Add tags to entities
            for tag in item['tags']:
                if tag not in self.entities:
                    self.entities[tag] = self.entity_count
                    self.entity_count += 1

        print(f"Built entity dictionary with {self.entity_count} entities")
        return self.entities

    def build_triples(self, data):
        """
        Build knowledge graph triples.

        Args:
            data (list): List of data items

        Returns:
            list: List of triples (head, relation, tail)
        """
        print("Building knowledge graph triples...")
        triples = []

        # Track relationships for creating additional connections
        tag_concepts = {}  # tag -> list of concepts
        concept_cooccurrence = {}  # concept -> list of co-occurring concepts

        for item in tqdm(data):
            image_entity_id = self.entities[item['image_id']]

            # Image-Tag relations
            for tag in item['tags']:
                if tag in self.entities:
                    tag_entity_id = self.entities[tag]
                    triples.append((image_entity_id, self.relations['image_tag'], tag_entity_id))

                    # Track tag's concepts
                    if tag_entity_id not in tag_concepts:
                        tag_concepts[tag_entity_id] = []

            # Image-Concept relations and concept co-occurrence
            item_concepts = []
            for concept_idx in item['concepts']:
                concept_name = list(self.concepts.keys())[concept_idx]
                concept_entity_id = self.entities[concept_name]
                item_concepts.append(concept_entity_id)
                triples.append((image_entity_id, self.relations['image_concept'], concept_entity_id))

                # Add concept to tags' concepts
                for tag in item['tags']:
                    if tag in self.entities:
                        tag_entity_id = self.entities[tag]
                        if concept_entity_id not in tag_concepts[tag_entity_id]:
                            tag_concepts[tag_entity_id].append(concept_entity_id)

            # Add concept co-occurrence relations
            for i, concept1 in enumerate(item_concepts):
                if concept1 not in concept_cooccurrence:
                    concept_cooccurrence[concept1] = []

                for j, concept2 in enumerate(item_concepts):
                    if i != j and concept2 not in concept_cooccurrence[concept1]:
                        concept_cooccurrence[concept1].append(concept2)

        # Add Tag-Concept relations
        for tag_id, concepts in tag_concepts.items():
            for concept_id in concepts:
                triples.append((tag_id, self.relations['tag_concept'], concept_id))

        # Add Concept Co-occurrence relations
        for concept1, co_concepts in concept_cooccurrence.items():
            for concept2 in co_concepts:
                triples.append((concept1, self.relations['co_occurrence'], concept2))

        print(f"Built {len(triples)} knowledge graph triples")
        return triples

    def save_knowledge_graph(self, entities, triples):
        """
        Save the constructed knowledge graph.

        Args:
            entities (dict): Dictionary mapping entity names to IDs
            triples (list): List of triples (head, relation, tail)
        """
        os.makedirs(self.output_path, exist_ok=True)

        # Save entity dictionary
        with open(os.path.join(self.output_path, 'mirflickr_entity2id.txt'), 'w') as f:
            f.write(f"{len(entities)}\n")
            for entity, entity_id in entities.items():
                f.write(f"{entity}\t{entity_id}\n")

        # Save relation dictionary
        with open(os.path.join(self.output_path, 'mirflickr_relation2id.txt'), 'w') as f:
            f.write(f"{len(self.relations)}\n")
            for relation, relation_id in self.relations.items():
                f.write(f"{relation}\t{relation_id}\n")

        # Save triples
        with open(os.path.join(self.output_path, 'mirflickr_triple.txt'), 'w') as f:
            f.write(f"{len(triples)}\n")
            for h, r, t in triples:
                f.write(f"{h}\t{t}\t{r}\n")

        # Save as pickle for easier loading
        with open(os.path.join(self.output_path, 'mirflickr_knowledge_graph.pkl'), 'wb') as f:
            pickle.dump({
                'entities': entities,
                'relations': self.relations,
                'triples': triples
            }, f)

        print(f"Saved knowledge graph to {self.output_path}")

    def construct(self, train_split_file=None):
        """
        Construct the knowledge graph.

        Args:
            train_split_file (str, optional): Path to a file containing image IDs for the training split

        Returns:
            dict: The constructed knowledge graph
        """
        # Load data
        data = self.load_data(train_split_file)

        # Build entity dictionary
        entities = self.build_entity_dictionary(data)

        # Build triples
        triples = self.build_triples(data)

        # Save knowledge graph
        self.save_knowledge_graph(entities, triples)

        return {
            'entities': entities,
            'relations': self.relations,
            'triples': triples
        }
