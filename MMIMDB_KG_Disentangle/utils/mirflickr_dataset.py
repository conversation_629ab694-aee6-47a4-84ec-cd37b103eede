"""
Dataset loader for MIR-Flickr dataset with knowledge graph integration.
"""

import os
import json
import torch
import numpy as np
import random
from PIL import Image
import re
from torch.utils.data import Dataset, DataLoader
from collections import OrderedDict, Counter
from sklearn.preprocessing import MultiLabelBinarizer
import pickle
import scipy.io as sio

class MIRFlickrDataset(Dataset):
    """
    Dataset class for MIR-Flickr with knowledge graph integration.
    """

    def __init__(self, data_path, kg_path, mode='train', transform=None, max_seq=40, sample_ratio=1.0):
        """
        Initialize the dataset.

        Args:
            data_path (str): Path to the MIR-Flickr dataset
            kg_path (str): Path to the knowledge graph data
            mode (str): Dataset split ('train', 'val', or 'test')
            transform (callable, optional): Optional transform to be applied on images
            max_seq (int): Maximum sequence length for text
            sample_ratio (float): Ratio of data to sample (for low-resource experiments)
        """
        self.data_path = data_path
        self.kg_path = kg_path
        self.mode = mode
        self.transform = transform
        self.max_seq = max_seq
        self.sample_ratio = sample_ratio

        # Load data
        self.load_data()

        # Load knowledge graph
        self.load_knowledge_graph()

        # Set up preprocessor for images
        self.setup_preprocessor()

    def load_data(self):
        """Load and preprocess the dataset."""
        print(f"Loading {self.mode} MIR-Flickr data...")

        # Define paths for MIR-Flickr dataset
        images_dir = os.path.join(self.data_path, 'images')
        tags_path = os.path.join(self.data_path, 'tags')
        annotations_path = os.path.join(self.data_path, 'annotations')

        # Load image list
        image_files = [f for f in os.listdir(images_dir) if f.endswith('.jpg')]
        
        # Load tags (if available)
        tags_dict = {}
        if os.path.exists(tags_path):
            for tag_file in os.listdir(tags_path):
                if tag_file.endswith('.txt'):
                    image_id = tag_file.split('.')[0]
                    with open(os.path.join(tags_path, tag_file), 'r', encoding='utf-8', errors='ignore') as f:
                        tags = f.read().strip().split()
                        tags_dict[image_id] = ' '.join(tags)

        # Load annotations (38 concepts)
        annotations = {}
        concept_names = []
        
        # MIR-Flickr has 38 concept annotations
        for i in range(1, 39):
            concept_file = os.path.join(annotations_path, f'annotation_{i}.txt')
            if os.path.exists(concept_file):
                concept_name = f'concept_{i}'  # You can map to actual concept names if available
                concept_names.append(concept_name)
                
                with open(concept_file, 'r') as f:
                    lines = f.readlines()
                    for j, line in enumerate(lines):
                        image_id = f'im{j+1}'
                        if image_id not in annotations:
                            annotations[image_id] = np.zeros(38)
                        
                        # Parse annotation (1 for positive, 0 for negative, -1 for unknown)
                        value = int(line.strip())
                        if value == 1:
                            annotations[image_id][i-1] = 1

        # Prepare data
        self.data = []
        self.vocab_counts = []
        
        for image_file in image_files:
            # Extract image ID
            image_id = image_file.split('.')[0]
            
            # Get tags
            tags = tags_dict.get(image_id, '')
            
            # Get concept labels
            label_vector = annotations.get(image_id, np.zeros(38))
            
            # Check if image exists
            img_path = os.path.join(images_dir, image_file)
            if os.path.exists(img_path) and np.sum(label_vector) > 0:
                # Normalize tags
                normalized_tags = self.normalize_text(tags)
                
                if len(normalized_tags) > 0:
                    self.vocab_counts.extend(normalized_tags.split())
                    self.data.append({
                        'image_id': image_id,
                        'image_path': image_file,
                        'tags': normalized_tags,
                        'labels': label_vector
                    })

        # Define concept names (38 concepts in MIR-Flickr)
        # These are placeholder names - replace with actual concept names if available
        self.concept_names = concept_names if concept_names else [
            'animals', 'baby', 'bird', 'car', 'clouds', 'dog', 'female', 
            'flower', 'food', 'indoor', 'lake', 'male', 'night', 'people',
            'plant_life', 'portrait', 'river', 'sea', 'sky', 'structures',
            'sunset', 'transport', 'tree', 'water', 'cityscape', 'landscape',
            'night_time', 'still_life', 'family', 'group', 'natural', 'party',
            'sport', 'travel', 'wedding', 'beach', 'mountain', 'urban'
        ][:38]  # Ensure we have exactly 38 concepts

        # Split data
        index = list(range(len(self.data)))
        random.seed(42)  # For reproducibility
        random.shuffle(index)

        train_len = int(len(self.data) * 0.6)
        test_len = int(len(self.data) * 0.3)

        train_data = [self.data[index[i]] for i in range(train_len)]
        test_data = [self.data[index[i]] for i in range(train_len, train_len + test_len)]
        val_data = [self.data[index[i]] for i in range(train_len + test_len, len(index))]

        # Save split information
        with open(os.path.join(self.data_path, 'train_ids_mirflickr.txt'), 'w') as f:
            for item in train_data:
                f.write(f"{item['image_id']}\n")

        with open(os.path.join(self.data_path, 'test_ids_mirflickr.txt'), 'w') as f:
            for item in test_data:
                f.write(f"{item['image_id']}\n")

        with open(os.path.join(self.data_path, 'val_ids_mirflickr.txt'), 'w') as f:
            for item in val_data:
                f.write(f"{item['image_id']}\n")

        # Set data based on mode
        if self.mode == 'train':
            self.samples = train_data
        elif self.mode == 'test':
            self.samples = test_data
        else:  # val
            self.samples = val_data

        # Apply sampling if needed
        if self.sample_ratio < 1.0:
            num_samples = int(len(self.samples) * self.sample_ratio)
            indices = random.sample(range(len(self.samples)), num_samples)
            self.samples = [self.samples[i] for i in indices]

        print(f"Loaded {len(self.samples)} samples for {self.mode}")

    def load_knowledge_graph(self):
        """Load knowledge graph data."""
        print("Loading knowledge graph...")

        # Load entity and relation mappings
        kg_pickle_path = os.path.join(self.kg_path, 'mirflickr_knowledge_graph.pkl')

        if os.path.exists(kg_pickle_path):
            with open(kg_pickle_path, 'rb') as f:
                kg_data = pickle.load(f)
                self.entity_id = kg_data['entities']
                self.relations = kg_data['relations']
                self.triples = kg_data['triples']
        else:
            # Load entity mappings
            self.entity_id = {}
            with open(os.path.join(self.kg_path, 'mirflickr_entity2id.txt'), 'r') as f:
                lines = f.readlines()[1:]  # Skip header
                for line in lines:
                    parts = line.strip().split('\t')
                    if len(parts) == 2:
                        self.entity_id[parts[0]] = int(parts[1])

        # Load KG embeddings if available
        kg_embed_path = os.path.join(self.kg_path, 'mirflickr_entity_embeddings.json')
        if os.path.exists(kg_embed_path):
            with open(kg_embed_path, 'r') as f:
                self.kg_embeddings = json.load(f)
        else:
            # Initialize random embeddings if not available
            print("KG embeddings not found, initializing random embeddings")
            self.kg_embeddings = {
                str(entity_id): np.random.randn(200).tolist()  # 200-dim embeddings
                for entity, entity_id in self.entity_id.items()
            }

    def setup_preprocessor(self):
        """Set up image preprocessor."""
        from torchvision import transforms

        self.preprocessor = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225])
        ])

    def normalize_text(self, text):
        """Normalize text data."""
        text = text.lower()
        text = re.sub(r'<br />', r' ', text).strip()
        text = re.sub(r'^https?:\/\/.*[\r\n]*', ' L ', text, flags=re.MULTILINE)
        text = re.sub(r'[\~\*\+\^`_#\[\]|]', r' ', text).strip()
        text = re.sub(r'[0-9]+', r' N ', text).strip()
        text = re.sub(r'([/\'\-\.?!\(\)",:;])', r' \1 ', text).strip()
        return text

    def __len__(self):
        """Get dataset length."""
        return len(self.samples)

    def __getitem__(self, idx):
        """Get a sample from the dataset."""
        # Get text features (tags)
        text = self.samples[idx]['tags']

        # Get image features
        img_path = os.path.join(self.data_path, 'images', self.samples[idx]['image_path'])
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        else:
            image = self.preprocessor(image)

        # Get knowledge graph features
        kg_features = []
        kg_weight = 0
        i = 0

        # Get label embeddings
        label_embeddings = np.array([
            self.kg_embeddings[str(i)] for i in range(38)
            if str(i) in self.kg_embeddings
        ])

        # Get tag embeddings
        tags = text.split()
        for tag in tags:
            if tag in self.entity_id and str(self.entity_id[tag]) in self.kg_embeddings:
                kg_features.append(self.kg_embeddings[str(self.entity_id[tag])])
                i += 1

        # Combine KG features
        if len(kg_features) == 0:
            kg_features = np.zeros(200)  # Default dimension
        else:
            kg_features = np.mean(kg_features, axis=0)

        # Calculate KG weight
        kg_weight = i / (i + 6) if i > 0 else 0

        return {
            'image': image,
            'text': text,
            'labels': torch.FloatTensor(self.samples[idx]['labels']),
            'kg_features': torch.FloatTensor(kg_features),
            'label_embeddings': torch.FloatTensor(label_embeddings),
            'kg_weight': kg_weight,
            'image_id': self.samples[idx]['image_id']
        }
