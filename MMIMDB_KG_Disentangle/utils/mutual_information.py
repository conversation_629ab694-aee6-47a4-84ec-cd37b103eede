#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
互信息估计工具
提供多种方法来估计两组特征之间的互信息
"""

import numpy as np
from sklearn.feature_selection import mutual_info_regression
from sklearn.neighbors import KernelDensity
from scipy.stats import entropy
import torch
import torch.nn as nn

def estimate_mutual_information(X, Y, method='knn', n_neighbors=5, bins=20):
    """
    估计两组特征之间的互信息
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        method (str): 估计方法，可选 'knn', 'binning', 'mine'
        n_neighbors (int): KNN方法的邻居数量
        bins (int): binning方法的箱数
        
    返回:
        float: 估计的互信息值
    """
    if method == 'knn':
        return estimate_mi_knn(X, Y, n_neighbors)
    elif method == 'binning':
        return estimate_mi_binning(X, Y, bins)
    elif method == 'mine':
        return estimate_mi_mine(X, Y)
    else:
        raise ValueError(f"不支持的方法: {method}")

def estimate_mi_knn(X, Y, n_neighbors=5):
    """
    使用K近邻方法估计互信息
    基于scikit-learn的mutual_info_regression实现
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        n_neighbors (int): 邻居数量
        
    返回:
        float: 估计的互信息值
    """
    # 如果X或Y是多维的，将其降维为1维
    if X.shape[1] > 1:
        X_1d = np.mean(X, axis=1)
    else:
        X_1d = X.ravel()
        
    if Y.shape[1] > 1:
        Y_1d = np.mean(Y, axis=1)
    else:
        Y_1d = Y.ravel()
    
    # 计算X和Y之间的互信息
    mi_xy = mutual_info_regression(X_1d.reshape(-1, 1), Y_1d, n_neighbors=n_neighbors)[0]
    
    # 归一化互信息到[0,1]区间
    # 计算X和Y的熵
    h_x = mutual_info_regression(X_1d.reshape(-1, 1), X_1d, n_neighbors=n_neighbors)[0]
    h_y = mutual_info_regression(Y_1d.reshape(-1, 1), Y_1d, n_neighbors=n_neighbors)[0]
    
    # 归一化互信息
    if h_x > 0 and h_y > 0:
        normalized_mi = mi_xy / np.sqrt(h_x * h_y)
    else:
        normalized_mi = 0
    
    return normalized_mi

def estimate_mi_binning(X, Y, bins=20):
    """
    使用直方图方法（binning）估计互信息
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        bins (int): 每个维度的箱数
        
    返回:
        float: 估计的互信息值
    """
    # 如果X或Y是多维的，将其降维为1维
    if X.shape[1] > 1:
        X_1d = np.mean(X, axis=1)
    else:
        X_1d = X.ravel()
        
    if Y.shape[1] > 1:
        Y_1d = np.mean(Y, axis=1)
    else:
        Y_1d = Y.ravel()
    
    # 计算联合直方图
    hist_xy, _, _ = np.histogram2d(X_1d, Y_1d, bins=bins)
    # 归一化为概率分布
    pxy = hist_xy / float(np.sum(hist_xy))
    # 计算边缘概率分布
    px = np.sum(pxy, axis=1)
    py = np.sum(pxy, axis=0)
    
    # 计算边缘熵
    hx = entropy(px, base=2)
    hy = entropy(py, base=2)
    
    # 计算联合熵
    hxy = entropy(pxy.flatten(), base=2)
    
    # 计算互信息
    mi = hx + hy - hxy
    
    # 归一化互信息
    if hx > 0 and hy > 0:
        normalized_mi = mi / np.sqrt(hx * hy)
    else:
        normalized_mi = 0
    
    return normalized_mi

def estimate_mi_mine(X, Y, batch_size=64, lr=1e-4, epochs=100):
    """
    使用互信息神经估计（MINE）方法估计互信息
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        batch_size (int): 批次大小
        lr (float): 学习率
        epochs (int): 训练轮数
        
    返回:
        float: 估计的互信息值
    """
    # 简化实现：使用KNN方法代替MINE
    # 实际应用中，可以实现完整的MINE算法
    return estimate_mi_knn(X, Y)

def estimate_mi_kernel_density(X, Y, bandwidth=0.1):
    """
    使用核密度估计方法估计互信息
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        bandwidth (float): 核密度估计的带宽
        
    返回:
        float: 估计的互信息值
    """
    # 如果X或Y是多维的，将其降维为1维
    if X.shape[1] > 1:
        X_1d = np.mean(X, axis=1)
    else:
        X_1d = X.ravel()
        
    if Y.shape[1] > 1:
        Y_1d = np.mean(Y, axis=1)
    else:
        Y_1d = Y.ravel()
    
    # 估计X的概率密度
    kde_x = KernelDensity(bandwidth=bandwidth)
    kde_x.fit(X_1d.reshape(-1, 1))
    log_px = kde_x.score_samples(X_1d.reshape(-1, 1))
    px = np.exp(log_px)
    
    # 估计Y的概率密度
    kde_y = KernelDensity(bandwidth=bandwidth)
    kde_y.fit(Y_1d.reshape(-1, 1))
    log_py = kde_y.score_samples(Y_1d.reshape(-1, 1))
    py = np.exp(log_py)
    
    # 估计联合概率密度
    XY = np.column_stack((X_1d, Y_1d))
    kde_xy = KernelDensity(bandwidth=bandwidth)
    kde_xy.fit(XY)
    log_pxy = kde_xy.score_samples(XY)
    pxy = np.exp(log_pxy)
    
    # 计算互信息
    mi = np.mean(np.log(pxy / (px * py)))
    
    # 归一化互信息
    hx = -np.mean(np.log(px))
    hy = -np.mean(np.log(py))
    
    if hx > 0 and hy > 0:
        normalized_mi = mi / np.sqrt(hx * hy)
    else:
        normalized_mi = 0
    
    return normalized_mi

# MINE神经网络实现（如果需要完整实现）
class MINENetwork(nn.Module):
    def __init__(self, x_dim, y_dim, hidden_dim=100):
        super(MINENetwork, self).__init__()
        self.network = nn.Sequential(
            nn.Linear(x_dim + y_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, x, y):
        batch_size = x.size(0)
        # 联合样本
        joint = torch.cat([x, y], dim=1)
        joint_score = self.network(joint)
        
        # 边缘样本（打乱y）
        y_shuffle = y[torch.randperm(batch_size)]
        marginal = torch.cat([x, y_shuffle], dim=1)
        marginal_score = self.network(marginal)
        
        # MINE目标
        mi_est = torch.mean(joint_score) - torch.log(torch.mean(torch.exp(marginal_score)))
        return mi_est, joint_score, marginal_score
