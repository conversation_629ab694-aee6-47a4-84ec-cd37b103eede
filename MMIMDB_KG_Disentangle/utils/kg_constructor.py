"""
Knowledge Graph Constructor for MM-IMDB dataset.

This module constructs a knowledge graph from the MM-IMDB dataset metadata,
similar to the approach in the IDKG paper but with enhancements for
cross-modal redundancy analysis.
"""

import os
import json
import numpy as np
import torch
from tqdm import tqdm
import pickle

class KnowledgeGraphConstructor:
    """
    Constructs a knowledge graph from MM-IMDB dataset metadata.
    """

    def __init__(self, data_path, output_path):
        """
        Initialize the knowledge graph constructor.

        Args:
            data_path (str): Path to the MM-IMDB dataset
            output_path (str): Path to save the constructed knowledge graph
        """
        self.data_path = data_path
        self.output_path = output_path

        # Genre mapping (similar to IDKG)
        self.genres = {
            'Drama': 0, 'Comedy': 1, 'Action': 2, 'Adventure': 3, 'Romance': 4,
            'Crime': 5, 'Horror': 6, 'Thriller': 7, 'Biography': 8, 'Animation': 9,
            'Family': 10, 'Mystery': 11, 'Fantasy': 12, 'Music': 13, 'History': 14,
            'Western': 15, 'Sci-Fi': 16, 'Musical': 17, 'Sport': 18, 'Short': 19,
            'War': 20, 'Documentary': 21, 'Film-Noir': 22
        }

        # Initialize entity dictionary with genres
        self.entities = self.genres.copy()
        self.entity_count = len(self.genres)

        # Relation types
        self.relations = {
            'movie_director': 0,
            'movie_actor': 1,
            'movie_genre': 2,
            'director_actor': 3,
            'director_genre': 4,
            'actor_genre': 5,
            'co_occurrence': 6  # New relation type for genre co-occurrence
        }

    def load_movie_data(self, split_file=None):
        """
        Load movie data from the dataset.

        Args:
            split_file (str, optional): Path to a file containing movie IDs for a specific split

        Returns:
            list: List of movie IDs
        """
        if split_file and os.path.exists(split_file):
            with open(split_file, 'r') as f:
                movie_ids = [line.strip() for line in f.readlines()]
        else:
            # Get all movie IDs from the json directory
            movie_ids = [f.split('.')[0] for f in os.listdir(os.path.join(self.data_path, 'json'))
                        if f.endswith('.json')]

        return movie_ids

    def build_entity_dictionary(self, movie_ids):
        """
        Build a dictionary of entities (movies, directors, actors, genres).

        Args:
            movie_ids (list): List of movie IDs

        Returns:
            dict: Dictionary mapping entity names to IDs
        """
        print("Building entity dictionary...")

        for movie_id in tqdm(movie_ids):
            json_path = os.path.join(self.data_path, 'json', f"{movie_id}.json")

            if not os.path.exists(json_path):
                continue

            with open(json_path, 'r') as f:
                data = json.load(f)

            # Add movie to entities
            self.entities[movie_id] = self.entity_count
            self.entity_count += 1

            # Add director to entities
            if 'director' in data and data['director'] not in self.entities:
                self.entities[data['director']] = self.entity_count
                self.entity_count += 1

            # Add actors to entities
            if 'cast' in data:
                for actor in data['cast']:
                    if actor not in self.entities:
                        self.entities[actor] = self.entity_count
                        self.entity_count += 1

        print(f"Built entity dictionary with {self.entity_count} entities")
        return self.entities

    def build_triples(self, movie_ids):
        """
        Build knowledge graph triples.

        Args:
            movie_ids (list): List of movie IDs

        Returns:
            list: List of triples (head, relation, tail)
        """
        print("Building knowledge graph triples...")
        triples = []

        # Track relationships for creating additional connections
        director_genres = {}  # director -> list of genres
        actor_genres = {}     # actor -> list of genres
        director_actors = {}  # director -> list of actors
        genre_cooccurrence = {}  # genre -> list of co-occurring genres

        for movie_id in tqdm(movie_ids):
            json_path = os.path.join(self.data_path, 'json', f"{movie_id}.json")

            if not os.path.exists(json_path):
                continue

            with open(json_path, 'r') as f:
                data = json.load(f)

            movie_entity_id = self.entities[movie_id]

            # Movie-Director relation
            if 'director' in data and data['director'] in self.entities:
                director_entity_id = self.entities[data['director']]
                triples.append((movie_entity_id, self.relations['movie_director'], director_entity_id))

                # Track director's genres
                if director_entity_id not in director_genres:
                    director_genres[director_entity_id] = []

                # Track director's actors
                if director_entity_id not in director_actors:
                    director_actors[director_entity_id] = []

            # Movie-Actor relations
            if 'cast' in data:
                for actor in data['cast']:
                    if actor in self.entities:
                        actor_entity_id = self.entities[actor]
                        triples.append((movie_entity_id, self.relations['movie_actor'], actor_entity_id))

                        # Track actor's genres
                        if actor_entity_id not in actor_genres:
                            actor_genres[actor_entity_id] = []

                        # Add actor to director's actors
                        if 'director' in data and data['director'] in self.entities:
                            director_entity_id = self.entities[data['director']]
                            if actor_entity_id not in director_actors[director_entity_id]:
                                director_actors[director_entity_id].append(actor_entity_id)

            # Movie-Genre relations and genre co-occurrence
            movie_genres = []
            for genre in data.get('genres', []):
                if genre in self.genres:
                    genre_entity_id = self.entities[genre]
                    movie_genres.append(genre_entity_id)
                    triples.append((movie_entity_id, self.relations['movie_genre'], genre_entity_id))

                    # Add genre to director's genres
                    if 'director' in data and data['director'] in self.entities:
                        director_entity_id = self.entities[data['director']]
                        if genre_entity_id not in director_genres[director_entity_id]:
                            director_genres[director_entity_id].append(genre_entity_id)

                    # Add genre to actors' genres
                    if 'cast' in data:
                        for actor in data['cast']:
                            if actor in self.entities:
                                actor_entity_id = self.entities[actor]
                                if genre_entity_id not in actor_genres[actor_entity_id]:
                                    actor_genres[actor_entity_id].append(genre_entity_id)

            # Add genre co-occurrence relations
            for i, genre1 in enumerate(movie_genres):
                if genre1 not in genre_cooccurrence:
                    genre_cooccurrence[genre1] = []

                for j, genre2 in enumerate(movie_genres):
                    if i != j and genre2 not in genre_cooccurrence[genre1]:
                        genre_cooccurrence[genre1].append(genre2)

        # Add Director-Actor relations
        for director_id, actors in director_actors.items():
            for actor_id in actors:
                triples.append((director_id, self.relations['director_actor'], actor_id))

        # Add Director-Genre relations
        for director_id, genres in director_genres.items():
            for genre_id in genres:
                triples.append((director_id, self.relations['director_genre'], genre_id))

        # Add Actor-Genre relations
        for actor_id, genres in actor_genres.items():
            for genre_id in genres:
                triples.append((actor_id, self.relations['actor_genre'], genre_id))

        # Add Genre Co-occurrence relations
        for genre1, co_genres in genre_cooccurrence.items():
            for genre2 in co_genres:
                triples.append((genre1, self.relations['co_occurrence'], genre2))

        print(f"Built {len(triples)} knowledge graph triples")
        return triples

    def save_knowledge_graph(self, entities, triples):
        """
        Save the constructed knowledge graph.

        Args:
            entities (dict): Dictionary mapping entity names to IDs
            triples (list): List of triples (head, relation, tail)
        """
        os.makedirs(self.output_path, exist_ok=True)

        # Save entity dictionary
        with open(os.path.join(self.output_path, 'entity2id.txt'), 'w') as f:
            f.write(f"{len(entities)}\n")
            for entity, entity_id in entities.items():
                f.write(f"{entity}\t{entity_id}\n")

        # Save relation dictionary
        with open(os.path.join(self.output_path, 'relation2id.txt'), 'w') as f:
            f.write(f"{len(self.relations)}\n")
            for relation, relation_id in self.relations.items():
                f.write(f"{relation}\t{relation_id}\n")

        # Save triples
        with open(os.path.join(self.output_path, 'triple.txt'), 'w') as f:
            f.write(f"{len(triples)}\n")
            for h, r, t in triples:
                f.write(f"{h}\t{t}\t{r}\n")

        # Save as pickle for easier loading
        with open(os.path.join(self.output_path, 'knowledge_graph.pkl'), 'wb') as f:
            pickle.dump({
                'entities': entities,
                'relations': self.relations,
                'triples': triples
            }, f)

        print(f"Saved knowledge graph to {self.output_path}")

    def construct(self, train_split_file=None):
        """
        Construct the knowledge graph.

        Args:
            train_split_file (str, optional): Path to a file containing movie IDs for the training split

        Returns:
            dict: The constructed knowledge graph
        """
        # Load movie data
        movie_ids = self.load_movie_data(train_split_file)

        # Build entity dictionary
        entities = self.build_entity_dictionary(movie_ids)

        # Build triples
        triples = self.build_triples(movie_ids)

        # Save knowledge graph
        self.save_knowledge_graph(entities, triples)

        return {
            'entities': entities,
            'relations': self.relations,
            'triples': triples
        }
