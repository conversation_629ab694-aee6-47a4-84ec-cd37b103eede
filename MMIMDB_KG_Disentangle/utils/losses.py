"""
Loss functions and metrics for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from sklearn.metrics import precision_recall_fscore_support, average_precision_score

class ContrastiveLoss(nn.Module):
    """
    Supervised contrastive loss for multi-modal learning.
    
    Based on the paper:
    "Supervised Contrastive Learning" - https://arxiv.org/abs/2004.11362
    """
    
    def __init__(self, temperature=0.07, base_temperature=0.07):
        """
        Initialize contrastive loss.
        
        Args:
            temperature (float): Temperature parameter
            base_temperature (float): Base temperature parameter
        """
        super(ContrastiveLoss, self).__init__()
        self.temperature = temperature
        self.base_temperature = base_temperature
    
    def forward(self, features, labels=None, mask=None):
        """
        Compute contrastive loss.
        
        Args:
            features (torch.Tensor): Feature vectors, shape [batch_size, feature_dim]
            labels (torch.Tensor): Labels, shape [batch_size]
            mask (torch.Tensor): Mask for positive pairs, shape [batch_size, batch_size]
            
        Returns:
            torch.Tensor: Loss value
        """
        device = features.device
        batch_size = features.shape[0]
        
        # Normalize features
        features = F.normalize(features, p=2, dim=1)
        
        # Compute similarity matrix
        sim_matrix = torch.matmul(features, features.T) / self.temperature
        
        # Create mask for positive pairs
        if labels is not None and mask is None:
            # Convert labels to one-hot encoding
            labels = labels.float()
            mask = torch.matmul(labels, labels.T)
            mask = mask.bool()
        elif mask is None:
            # If no labels or mask, use identity matrix (self-supervision)
            mask = torch.eye(batch_size, dtype=torch.bool, device=device)
        
        # For numerical stability
        logits_max, _ = torch.max(sim_matrix, dim=1, keepdim=True)
        sim_matrix = sim_matrix - logits_max.detach()
        
        # Compute log probabilities
        exp_sim_matrix = torch.exp(sim_matrix)
        
        # Mask out self-contrast cases
        mask_self = ~torch.eye(batch_size, dtype=torch.bool, device=device)
        mask = mask & mask_self
        
        # Compute positive and negative pairs
        pos_mask = mask.float()
        neg_mask = mask_self.float() - pos_mask
        
        # Compute positive and negative sums
        pos_sum = torch.sum(exp_sim_matrix * pos_mask, dim=1)
        neg_sum = torch.sum(exp_sim_matrix * neg_mask, dim=1)
        
        # Compute loss
        num_positives = torch.sum(pos_mask, dim=1)
        
        # Handle case where there are no positive pairs
        pos_mask_valid = num_positives > 0
        
        # Compute log probabilities
        log_probs = -torch.log(pos_sum / (pos_sum + neg_sum + 1e-8))
        
        # Compute loss only for valid samples
        loss = torch.sum(log_probs * pos_mask_valid.float()) / (torch.sum(pos_mask_valid.float()) + 1e-8)
        
        return loss

class RedundancyLoss(nn.Module):
    """
    Loss function for penalizing redundancy between modalities.
    """
    
    def __init__(self, alpha=0.1):
        """
        Initialize redundancy loss.
        
        Args:
            alpha (float): Weight for the loss
        """
        super(RedundancyLoss, self).__init__()
        self.alpha = alpha
    
    def forward(self, x, y, redundancy_score=None):
        """
        Compute redundancy loss.
        
        Args:
            x (torch.Tensor): Features from first modality
            y (torch.Tensor): Features from second modality
            redundancy_score (torch.Tensor, optional): Pre-computed redundancy score
            
        Returns:
            torch.Tensor: Loss value
        """
        if redundancy_score is not None:
            # Use pre-computed redundancy score
            return self.alpha * torch.mean(redundancy_score)
        
        # Compute cosine similarity as a measure of redundancy
        x_norm = F.normalize(x, p=2, dim=1)
        y_norm = F.normalize(y, p=2, dim=1)
        similarity = torch.sum(x_norm * y_norm, dim=1)
        
        # Penalize high similarity (redundancy)
        return self.alpha * torch.mean(similarity)

class OrthogonalityLoss(nn.Module):
    """
    Loss function for enforcing orthogonality between modality-specific and modality-invariant features.
    """
    
    def __init__(self, beta=0.1):
        """
        Initialize orthogonality loss.
        
        Args:
            beta (float): Weight for the loss
        """
        super(OrthogonalityLoss, self).__init__()
        self.beta = beta
    
    def forward(self, invariant, specific):
        """
        Compute orthogonality loss.
        
        Args:
            invariant (torch.Tensor): Modality-invariant features
            specific (torch.Tensor): Modality-specific features
            
        Returns:
            torch.Tensor: Loss value
        """
        # Normalize features
        invariant_norm = F.normalize(invariant, p=2, dim=1)
        specific_norm = F.normalize(specific, p=2, dim=1)
        
        # Compute cosine similarity
        similarity = torch.abs(torch.sum(invariant_norm * specific_norm, dim=1))
        
        # Penalize high similarity (non-orthogonality)
        return self.beta * torch.mean(similarity)

class MultiLabelFocalLoss(nn.Module):
    """
    Focal loss for multi-label classification.
    
    Based on the paper:
    "Focal Loss for Dense Object Detection" - https://arxiv.org/abs/1708.02002
    """
    
    def __init__(self, gamma=2.0, alpha=0.25):
        """
        Initialize focal loss.
        
        Args:
            gamma (float): Focusing parameter
            alpha (float): Balancing parameter
        """
        super(MultiLabelFocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
    
    def forward(self, logits, targets):
        """
        Compute focal loss.
        
        Args:
            logits (torch.Tensor): Predicted logits
            targets (torch.Tensor): Target labels
            
        Returns:
            torch.Tensor: Loss value
        """
        # Compute probabilities
        probs = torch.sigmoid(logits)
        
        # Compute focal weights
        pt = torch.where(targets == 1, probs, 1 - probs)
        alpha = torch.where(targets == 1, self.alpha, 1 - self.alpha)
        focal_weight = alpha * (1 - pt).pow(self.gamma)
        
        # Compute binary cross-entropy loss
        bce_loss = F.binary_cross_entropy_with_logits(logits, targets, reduction='none')
        
        # Apply focal weights
        loss = focal_weight * bce_loss
        
        return loss.mean()

def compute_metrics(y_true, y_pred, threshold=0.5):
    """
    Compute evaluation metrics for multi-label classification.
    
    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities
        threshold (float): Threshold for binary prediction
        
    Returns:
        dict: Dictionary of metrics
    """
    # Convert predictions to binary using threshold
    y_pred_binary = (y_pred > threshold).astype(int)
    
    # Compute precision, recall, F1 score
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average='samples'
    )
    
    # Compute mean average precision
    mAP = average_precision_score(y_true, y_pred, average='samples')
    
    # Compute per-class metrics
    per_class_precision, per_class_recall, per_class_f1, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average=None
    )
    
    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'mAP': mAP,
        'per_class_precision': per_class_precision.tolist(),
        'per_class_recall': per_class_recall.tolist(),
        'per_class_f1': per_class_f1.tolist()
    }
