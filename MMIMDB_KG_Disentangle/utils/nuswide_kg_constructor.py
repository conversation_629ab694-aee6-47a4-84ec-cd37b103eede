"""
Knowledge Graph Constructor for NUS-WIDE dataset.

This module constructs a knowledge graph from the NUS-WIDE dataset metadata,
following a similar approach to the MM-IMDB KG constructor but adapted for
the NUS-WIDE dataset structure.
"""

import os
import json
import numpy as np
import torch
from tqdm import tqdm
import pickle
from collections import Counter

class NUSWIDEKnowledgeGraphConstructor:
    """
    Constructs a knowledge graph from NUS-WIDE dataset metadata.
    """

    def __init__(self, data_path, output_path):
        """
        Initialize the knowledge graph constructor.

        Args:
            data_path (str): Path to the NUS-WIDE dataset
            output_path (str): Path to save the constructed knowledge graph
        """
        self.data_path = data_path
        self.output_path = output_path

        # Concept mapping (81 concepts in NUS-WIDE)
        self.concepts = {
            'airport': 0, 'animal': 1, 'beach': 2, 'bear': 3, 'birds': 4, 
            'boats': 5, 'book': 6, 'bridge': 7, 'buildings': 8, 'cars': 9, 
            'castle': 10, 'cat': 11, 'cityscape': 12, 'clouds': 13, 'computer': 14, 
            'coral': 15, 'cow': 16, 'dancing': 17, 'dog': 18, 'earthquake': 19, 
            'elk': 20, 'fire': 21, 'fish': 22, 'flags': 23, 'flowers': 24, 
            'food': 25, 'fox': 26, 'frost': 27, 'garden': 28, 'glacier': 29, 
            'grass': 30, 'harbor': 31, 'horses': 32, 'house': 33, 'lake': 34, 
            'leaf': 35, 'map': 36, 'military': 37, 'moon': 38, 'mountain': 39, 
            'nighttime': 40, 'ocean': 41, 'person': 42, 'plane': 43, 'plants': 44, 
            'police': 45, 'protest': 46, 'railroad': 47, 'rainbow': 48, 'reflection': 49, 
            'road': 50, 'rocks': 51, 'running': 52, 'sand': 53, 'sign': 54, 
            'sky': 55, 'snow': 56, 'soccer': 57, 'sports': 58, 'statue': 59, 
            'street': 60, 'sun': 61, 'sunset': 62, 'surf': 63, 'swimmers': 64, 
            'tattoo': 65, 'temple': 66, 'tiger': 67, 'tower': 68, 'town': 69, 
            'toy': 70, 'train': 71, 'tree': 72, 'valley': 73, 'vehicle': 74, 
            'water': 75, 'waterfall': 76, 'wedding': 77, 'whales': 78, 'window': 79, 
            'zebra': 80
        }

        # Initialize entity dictionary with concepts
        self.entities = self.concepts.copy()
        self.entity_count = len(self.concepts)

        # Relation types
        self.relations = {
            'image_tag': 0,
            'image_concept': 1,
            'tag_concept': 2,
            'co_occurrence': 3  # For concept co-occurrence
        }

    def load_data(self, split_file=None):
        """
        Load data from the dataset.

        Args:
            split_file (str, optional): Path to a file containing image IDs for a specific split

        Returns:
            list: List of data items
        """
        # Define paths for NUS-WIDE dataset
        image_list_path = os.path.join(self.data_path, 'ImageList', 'ImageList.txt')
        tags_path = os.path.join(self.data_path, 'Tags', 'AllTags81.txt')
        labels_path = os.path.join(self.data_path, 'AllLabels', 'AllLabels.txt')

        # Load image list
        with open(image_list_path, 'r') as f:
            image_list = [line.strip() for line in f.readlines()]

        # Load tags
        with open(tags_path, 'r') as f:
            tags_data = [line.strip() for line in f.readlines()]

        # Load labels (81 concepts)
        with open(labels_path, 'r') as f:
            labels_data = [line.strip().split() for line in f.readlines()]
            labels_data = [[int(label) for label in row] for row in labels_data]

        # Prepare data
        data = []
        for i, (image_path, tags, labels) in enumerate(zip(image_list, tags_data, labels_data)):
            # Extract image ID
            image_id = os.path.basename(image_path).split('.')[0]
            
            # Check if image exists
            full_image_path = os.path.join(self.data_path, 'images', image_path)
            if os.path.exists(full_image_path):
                # Get concept labels
                concept_labels = [j for j, label in enumerate(labels) if label == 1]
                
                # Get tags
                tag_list = tags.lower().split()
                
                data.append({
                    'image_id': image_id,
                    'image_path': image_path,
                    'tags': tag_list,
                    'concepts': concept_labels
                })

        # If split file is provided, filter data
        if split_file and os.path.exists(split_file):
            with open(split_file, 'r') as f:
                split_ids = [line.strip() for line in f.readlines()]
            data = [item for item in data if item['image_id'] in split_ids]

        return data

    def build_entity_dictionary(self, data):
        """
        Build a dictionary of entities (images, tags, concepts).

        Args:
            data (list): List of data items

        Returns:
            dict: Dictionary mapping entity names to IDs
        """
        print("Building entity dictionary...")

        # Add images to entities
        for item in tqdm(data):
            # Add image to entities
            self.entities[item['image_id']] = self.entity_count
            self.entity_count += 1

            # Add tags to entities
            for tag in item['tags']:
                if tag not in self.entities:
                    self.entities[tag] = self.entity_count
                    self.entity_count += 1

        print(f"Built entity dictionary with {self.entity_count} entities")
        return self.entities

    def build_triples(self, data):
        """
        Build knowledge graph triples.

        Args:
            data (list): List of data items

        Returns:
            list: List of triples (head, relation, tail)
        """
        print("Building knowledge graph triples...")
        triples = []

        # Track relationships for creating additional connections
        tag_concepts = {}  # tag -> list of concepts
        concept_cooccurrence = {}  # concept -> list of co-occurring concepts

        for item in tqdm(data):
            image_entity_id = self.entities[item['image_id']]

            # Image-Tag relations
            for tag in item['tags']:
                if tag in self.entities:
                    tag_entity_id = self.entities[tag]
                    triples.append((image_entity_id, self.relations['image_tag'], tag_entity_id))

                    # Track tag's concepts
                    if tag_entity_id not in tag_concepts:
                        tag_concepts[tag_entity_id] = []

            # Image-Concept relations and concept co-occurrence
            item_concepts = []
            for concept_idx in item['concepts']:
                concept_name = list(self.concepts.keys())[concept_idx]
                concept_entity_id = self.entities[concept_name]
                item_concepts.append(concept_entity_id)
                triples.append((image_entity_id, self.relations['image_concept'], concept_entity_id))

                # Add concept to tags' concepts
                for tag in item['tags']:
                    if tag in self.entities:
                        tag_entity_id = self.entities[tag]
                        if concept_entity_id not in tag_concepts[tag_entity_id]:
                            tag_concepts[tag_entity_id].append(concept_entity_id)

            # Add concept co-occurrence relations
            for i, concept1 in enumerate(item_concepts):
                if concept1 not in concept_cooccurrence:
                    concept_cooccurrence[concept1] = []

                for j, concept2 in enumerate(item_concepts):
                    if i != j and concept2 not in concept_cooccurrence[concept1]:
                        concept_cooccurrence[concept1].append(concept2)

        # Add Tag-Concept relations
        for tag_id, concepts in tag_concepts.items():
            for concept_id in concepts:
                triples.append((tag_id, self.relations['tag_concept'], concept_id))

        # Add Concept Co-occurrence relations
        for concept1, co_concepts in concept_cooccurrence.items():
            for concept2 in co_concepts:
                triples.append((concept1, self.relations['co_occurrence'], concept2))

        print(f"Built {len(triples)} knowledge graph triples")
        return triples

    def save_knowledge_graph(self, entities, triples):
        """
        Save the constructed knowledge graph.

        Args:
            entities (dict): Dictionary mapping entity names to IDs
            triples (list): List of triples (head, relation, tail)
        """
        os.makedirs(self.output_path, exist_ok=True)

        # Save entity dictionary
        with open(os.path.join(self.output_path, 'nuswide_entity2id.txt'), 'w') as f:
            f.write(f"{len(entities)}\n")
            for entity, entity_id in entities.items():
                f.write(f"{entity}\t{entity_id}\n")

        # Save relation dictionary
        with open(os.path.join(self.output_path, 'nuswide_relation2id.txt'), 'w') as f:
            f.write(f"{len(self.relations)}\n")
            for relation, relation_id in self.relations.items():
                f.write(f"{relation}\t{relation_id}\n")

        # Save triples
        with open(os.path.join(self.output_path, 'nuswide_triple.txt'), 'w') as f:
            f.write(f"{len(triples)}\n")
            for h, r, t in triples:
                f.write(f"{h}\t{t}\t{r}\n")

        # Save as pickle for easier loading
        with open(os.path.join(self.output_path, 'nuswide_knowledge_graph.pkl'), 'wb') as f:
            pickle.dump({
                'entities': entities,
                'relations': self.relations,
                'triples': triples
            }, f)

        print(f"Saved knowledge graph to {self.output_path}")

    def construct(self, train_split_file=None):
        """
        Construct the knowledge graph.

        Args:
            train_split_file (str, optional): Path to a file containing image IDs for the training split

        Returns:
            dict: The constructed knowledge graph
        """
        # Load data
        data = self.load_data(train_split_file)

        # Build entity dictionary
        entities = self.build_entity_dictionary(data)

        # Build triples
        triples = self.build_triples(data)

        # Save knowledge graph
        self.save_knowledge_graph(entities, triples)

        return {
            'entities': entities,
            'relations': self.relations,
            'triples': triples
        }
