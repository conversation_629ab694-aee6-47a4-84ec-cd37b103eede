"""
Knowledge Graph Embedding Generator.

This module generates embeddings for entities in the knowledge graph
using TransE, TransH, or RotatE models.
"""

import os
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
import pickle

class KGDataset(Dataset):
    """Dataset for knowledge graph triples."""
    
    def __init__(self, triples, num_entities, num_relations):
        """
        Initialize the dataset.
        
        Args:
            triples (list): List of triples (head, relation, tail)
            num_entities (int): Number of entities
            num_relations (int): Number of relations
        """
        self.triples = triples
        self.num_entities = num_entities
        self.num_relations = num_relations
    
    def __len__(self):
        """Get dataset length."""
        return len(self.triples)
    
    def __getitem__(self, idx):
        """Get a sample from the dataset."""
        head, relation, tail = self.triples[idx]
        return {
            'head': torch.tensor(head, dtype=torch.long),
            'relation': torch.tensor(relation, dtype=torch.long),
            'tail': torch.tensor(tail, dtype=torch.long)
        }

class TransE(nn.Module):
    """
    TransE model for knowledge graph embeddings.
    
    Based on the paper:
    "Translating Embeddings for Modeling Multi-relational Data" - https://papers.nips.cc/paper/2013/hash/1cecc7a77928ca8133fa24680a88d2f9-Abstract.html
    """
    
    def __init__(self, num_entities, num_relations, embedding_dim=200, margin=1.0):
        """
        Initialize the TransE model.
        
        Args:
            num_entities (int): Number of entities
            num_relations (int): Number of relations
            embedding_dim (int): Dimension of embeddings
            margin (float): Margin for loss function
        """
        super(TransE, self).__init__()
        
        self.num_entities = num_entities
        self.num_relations = num_relations
        self.embedding_dim = embedding_dim
        self.margin = margin
        
        # Entity embeddings
        self.entity_embeddings = nn.Embedding(num_entities, embedding_dim)
        nn.init.xavier_uniform_(self.entity_embeddings.weight.data)
        
        # Relation embeddings
        self.relation_embeddings = nn.Embedding(num_relations, embedding_dim)
        nn.init.xavier_uniform_(self.relation_embeddings.weight.data)
        
        # Normalize embeddings
        self.entity_embeddings.weight.data = F.normalize(self.entity_embeddings.weight.data, p=2, dim=1)
        self.relation_embeddings.weight.data = F.normalize(self.relation_embeddings.weight.data, p=2, dim=1)
    
    def forward(self, head, relation, tail):
        """
        Forward pass of the TransE model.
        
        Args:
            head (torch.Tensor): Head entity indices
            relation (torch.Tensor): Relation indices
            tail (torch.Tensor): Tail entity indices
            
        Returns:
            torch.Tensor: Score for each triple
        """
        # Get embeddings
        head_embeddings = self.entity_embeddings(head)
        relation_embeddings = self.relation_embeddings(relation)
        tail_embeddings = self.entity_embeddings(tail)
        
        # Compute score: ||h + r - t||
        score = torch.norm(head_embeddings + relation_embeddings - tail_embeddings, p=2, dim=1)
        
        return score
    
    def get_embeddings(self):
        """
        Get entity and relation embeddings.
        
        Returns:
            dict: Dictionary of embeddings
        """
        return {
            'entity_embeddings': self.entity_embeddings.weight.detach().cpu().numpy(),
            'relation_embeddings': self.relation_embeddings.weight.detach().cpu().numpy()
        }

class KGEmbeddingGenerator:
    """Generator for knowledge graph embeddings."""
    
    def __init__(self, kg_path, output_path, embedding_dim=200, model_type='TransE'):
        """
        Initialize the embedding generator.
        
        Args:
            kg_path (str): Path to the knowledge graph data
            output_path (str): Path to save the embeddings
            embedding_dim (int): Dimension of embeddings
            model_type (str): Type of embedding model ('TransE', 'TransH', or 'RotatE')
        """
        self.kg_path = kg_path
        self.output_path = output_path
        self.embedding_dim = embedding_dim
        self.model_type = model_type
        
        # Load knowledge graph
        self.load_knowledge_graph()
    
    def load_knowledge_graph(self):
        """Load knowledge graph data."""
        print("Loading knowledge graph...")
        
        kg_pickle_path = os.path.join(self.kg_path, 'knowledge_graph.pkl')
        
        if os.path.exists(kg_pickle_path):
            with open(kg_pickle_path, 'rb') as f:
                kg_data = pickle.load(f)
                self.entities = kg_data['entities']
                self.relations = kg_data['relations']
                self.triples = kg_data['triples']
        else:
            # Load entity mappings
            self.entities = {}
            with open(os.path.join(self.kg_path, 'entity2id.txt'), 'r') as f:
                lines = f.readlines()[1:]  # Skip header
                for line in lines:
                    parts = line.strip().split('\t')
                    if len(parts) == 2:
                        self.entities[parts[0]] = int(parts[1])
            
            # Load relation mappings
            self.relations = {}
            with open(os.path.join(self.kg_path, 'relation2id.txt'), 'r') as f:
                lines = f.readlines()[1:]  # Skip header
                for line in lines:
                    parts = line.strip().split('\t')
                    if len(parts) == 2:
                        self.relations[parts[0]] = int(parts[1])
            
            # Load triples
            self.triples = []
            with open(os.path.join(self.kg_path, 'triple.txt'), 'r') as f:
                lines = f.readlines()[1:]  # Skip header
                for line in lines:
                    parts = line.strip().split('\t')
                    if len(parts) == 3:
                        head = int(parts[0])
                        tail = int(parts[1])
                        relation = int(parts[2])
                        self.triples.append((head, relation, tail))
        
        self.num_entities = len(self.entities)
        self.num_relations = len(self.relations)
        
        print(f"Loaded knowledge graph with {self.num_entities} entities, "
              f"{self.num_relations} relations, and {len(self.triples)} triples")
    
    def create_model(self):
        """Create embedding model."""
        if self.model_type == 'TransE':
            return TransE(
                num_entities=self.num_entities,
                num_relations=self.num_relations,
                embedding_dim=self.embedding_dim
            )
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
    
    def train(self, batch_size=128, num_epochs=100, lr=0.001, device='cuda'):
        """
        Train the embedding model.
        
        Args:
            batch_size (int): Batch size
            num_epochs (int): Number of training epochs
            lr (float): Learning rate
            device (str): Device to use ('cuda' or 'cpu')
            
        Returns:
            dict: Dictionary of embeddings
        """
        print(f"Training {self.model_type} model...")
        
        # Set device
        device = torch.device(device if torch.cuda.is_available() else 'cpu')
        
        # Create dataset and dataloader
        dataset = KGDataset(self.triples, self.num_entities, self.num_relations)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # Create model
        model = self.create_model().to(device)
        
        # Create optimizer
        optimizer = optim.Adam(model.parameters(), lr=lr)
        
        # Training loop
        for epoch in range(num_epochs):
            model.train()
            total_loss = 0.0
            
            for batch in tqdm(dataloader, desc=f"Epoch {epoch+1}/{num_epochs}"):
                # Move batch to device
                head = batch['head'].to(device)
                relation = batch['relation'].to(device)
                tail = batch['tail'].to(device)
                
                # Forward pass
                score = model(head, relation, tail)
                
                # Generate negative samples
                neg_head = torch.randint(0, self.num_entities, head.shape).to(device)
                neg_tail = torch.randint(0, self.num_entities, tail.shape).to(device)
                
                # Forward pass for negative samples
                neg_head_score = model(neg_head, relation, tail)
                neg_tail_score = model(head, relation, neg_tail)
                
                # Compute loss
                pos_loss = torch.mean(score)
                neg_loss = torch.mean(torch.min(neg_head_score, neg_tail_score))
                loss = torch.relu(pos_loss - neg_loss + model.margin)
                
                # Backward pass and optimize
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # Normalize embeddings
                with torch.no_grad():
                    model.entity_embeddings.weight.data = F.normalize(
                        model.entity_embeddings.weight.data, p=2, dim=1
                    )
                    model.relation_embeddings.weight.data = F.normalize(
                        model.relation_embeddings.weight.data, p=2, dim=1
                    )
                
                # Update statistics
                total_loss += loss.item()
            
            # Print epoch statistics
            print(f"Epoch {epoch+1}/{num_epochs}, Loss: {total_loss/len(dataloader):.4f}")
        
        # Get embeddings
        embeddings = model.get_embeddings()
        
        return embeddings
    
    def generate_embeddings(self, batch_size=128, num_epochs=100, lr=0.001, device='cuda'):
        """
        Generate embeddings for the knowledge graph.
        
        Args:
            batch_size (int): Batch size
            num_epochs (int): Number of training epochs
            lr (float): Learning rate
            device (str): Device to use ('cuda' or 'cpu')
            
        Returns:
            dict: Dictionary of embeddings
        """
        # Train model to get embeddings
        embeddings = self.train(batch_size, num_epochs, lr, device)
        
        # Create output directory
        os.makedirs(self.output_path, exist_ok=True)
        
        # Save embeddings
        entity_embeddings = embeddings['entity_embeddings']
        relation_embeddings = embeddings['relation_embeddings']
        
        # Save as JSON for easy loading
        entity_embeddings_dict = {
            str(i): entity_embeddings[i].tolist()
            for i in range(self.num_entities)
        }
        
        relation_embeddings_dict = {
            str(i): relation_embeddings[i].tolist()
            for i in range(self.num_relations)
        }
        
        with open(os.path.join(self.output_path, 'entity_embeddings.json'), 'w') as f:
            json.dump(entity_embeddings_dict, f)
        
        with open(os.path.join(self.output_path, 'relation_embeddings.json'), 'w') as f:
            json.dump(relation_embeddings_dict, f)
        
        # Save as numpy arrays
        np.save(os.path.join(self.output_path, 'entity_embeddings.npy'), entity_embeddings)
        np.save(os.path.join(self.output_path, 'relation_embeddings.npy'), relation_embeddings)
        
        print(f"Saved embeddings to {self.output_path}")
        
        return embeddings

if __name__ == '__main__':
    import argparse
    import torch.nn.functional as F
    
    # Parse arguments
    parser = argparse.ArgumentParser(description="Generate knowledge graph embeddings")
    parser.add_argument('--kg_path', type=str, required=True,
                        help='Path to the knowledge graph data')
    parser.add_argument('--output_path', type=str, required=True,
                        help='Path to save the embeddings')
    parser.add_argument('--embedding_dim', type=int, default=200,
                        help='Dimension of embeddings')
    parser.add_argument('--model_type', type=str, default='TransE',
                        choices=['TransE', 'TransH', 'RotatE'],
                        help='Type of embedding model')
    parser.add_argument('--batch_size', type=int, default=128,
                        help='Batch size')
    parser.add_argument('--num_epochs', type=int, default=100,
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    
    args = parser.parse_args()
    
    # Generate embeddings
    generator = KGEmbeddingGenerator(
        kg_path=args.kg_path,
        output_path=args.output_path,
        embedding_dim=args.embedding_dim,
        model_type=args.model_type
    )
    
    generator.generate_embeddings(
        batch_size=args.batch_size,
        num_epochs=args.num_epochs,
        lr=args.lr,
        device=args.device
    )
