"""
Dataset loader for NUS-WIDE dataset with knowledge graph integration.
"""

import os
import json
import torch
import numpy as np
import random
from PIL import Image
import re
from torch.utils.data import Dataset, DataLoader
from collections import OrderedDict, Counter
from sklearn.preprocessing import MultiLabelBinarizer
import pickle
import scipy.io as sio

class NUSWIDEDataset(Dataset):
    """
    Dataset class for NUS-WIDE with knowledge graph integration.
    """

    def __init__(self, data_path, kg_path, mode='train', transform=None, max_seq=40, sample_ratio=1.0):
        """
        Initialize the dataset.

        Args:
            data_path (str): Path to the NUS-WIDE dataset
            kg_path (str): Path to the knowledge graph data
            mode (str): Dataset split ('train', 'val', or 'test')
            transform (callable, optional): Optional transform to be applied on images
            max_seq (int): Maximum sequence length for text
            sample_ratio (float): Ratio of data to sample (for low-resource experiments)
        """
        self.data_path = data_path
        self.kg_path = kg_path
        self.mode = mode
        self.transform = transform
        self.max_seq = max_seq
        self.sample_ratio = sample_ratio

        # Load data
        self.load_data()

        # Load knowledge graph
        self.load_knowledge_graph()

        # Set up preprocessor for images
        self.setup_preprocessor()

    def load_data(self):
        """Load and preprocess the dataset."""
        print(f"Loading {self.mode} NUS-WIDE data...")

        # Define paths for NUS-WIDE dataset
        image_list_path = os.path.join(self.data_path, 'ImageList', 'ImageList.txt')
        tags_path = os.path.join(self.data_path, 'Tags', 'AllTags81.txt')
        labels_path = os.path.join(self.data_path, 'AllLabels', 'AllLabels.txt')

        # Load image list
        with open(image_list_path, 'r') as f:
            image_list = [line.strip() for line in f.readlines()]

        # Load tags
        with open(tags_path, 'r') as f:
            tags_data = [line.strip() for line in f.readlines()]

        # Load labels (81 concepts)
        with open(labels_path, 'r') as f:
            labels_data = [line.strip().split() for line in f.readlines()]
            labels_data = [[int(label) for label in row] for row in labels_data]

        # Prepare data
        self.data = []
        self.vocab_counts = []
        n_classes = 81  # Number of concept classes in NUS-WIDE

        for i, (image_path, tags, labels) in enumerate(zip(image_list, tags_data, labels_data)):
            # Extract image ID
            image_id = os.path.basename(image_path).split('.')[0]
            
            # Check if image exists
            full_image_path = os.path.join(self.data_path, 'images', image_path)
            if os.path.exists(full_image_path):
                # Normalize tags
                normalized_tags = self.normalize_text(tags)
                
                # Get concept labels
                concept_labels = [j for j, label in enumerate(labels) if label == 1]
                
                if len(normalized_tags) > 0 and len(concept_labels) > 0:
                    self.vocab_counts.extend(normalized_tags.split())
                    self.data.append({
                        'image_id': image_id,
                        'image_path': image_path,
                        'tags': normalized_tags,
                        'concepts': concept_labels
                    })

        # Define concept names (81 concepts in NUS-WIDE)
        self.concept_names = [
            'airport', 'animal', 'beach', 'bear', 'birds', 'boats', 'book', 'bridge', 
            'buildings', 'cars', 'castle', 'cat', 'cityscape', 'clouds', 'computer', 
            'coral', 'cow', 'dancing', 'dog', 'earthquake', 'elk', 'fire', 'fish', 
            'flags', 'flowers', 'food', 'fox', 'frost', 'garden', 'glacier', 'grass', 
            'harbor', 'horses', 'house', 'lake', 'leaf', 'map', 'military', 'moon', 
            'mountain', 'nighttime', 'ocean', 'person', 'plane', 'plants', 'police', 
            'protest', 'railroad', 'rainbow', 'reflection', 'road', 'rocks', 'running', 
            'sand', 'sign', 'sky', 'snow', 'soccer', 'sports', 'statue', 'street', 
            'sun', 'sunset', 'surf', 'swimmers', 'tattoo', 'temple', 'tiger', 'tower', 
            'town', 'toy', 'train', 'tree', 'valley', 'vehicle', 'water', 'waterfall', 
            'wedding', 'whales', 'window', 'zebra'
        ]

        # Create multi-label binarizer
        le = MultiLabelBinarizer()
        self.Y = np.zeros((len(self.data), n_classes))
        for i, item in enumerate(self.data):
            for concept in item['concepts']:
                self.Y[i, concept] = 1
        
        self.le = le
        self.labels = np.arange(n_classes)
        self.gt = self.Y

        # Split data
        index = list(range(len(self.data)))
        random.seed(42)  # For reproducibility
        random.shuffle(index)

        train_len = int(len(self.data) * 0.6)
        test_len = int(len(self.data) * 0.3)

        train_data = [self.data[index[i]] for i in range(train_len)]
        train_labels = [self.gt[index[i]] for i in range(train_len)]

        test_data = [self.data[index[i]] for i in range(train_len, train_len + test_len)]
        test_labels = [self.gt[index[i]] for i in range(train_len, train_len + test_len)]

        val_data = [self.data[index[i]] for i in range(train_len + test_len, len(index))]
        val_labels = [self.gt[index[i]] for i in range(train_len + test_len, len(index))]

        # Save split information
        with open(os.path.join(self.data_path, 'train_ids_nuswide.txt'), 'w') as f:
            for item in train_data:
                f.write(f"{item['image_id']}\n")

        with open(os.path.join(self.data_path, 'test_ids_nuswide.txt'), 'w') as f:
            for item in test_data:
                f.write(f"{item['image_id']}\n")

        with open(os.path.join(self.data_path, 'val_ids_nuswide.txt'), 'w') as f:
            for item in val_data:
                f.write(f"{item['image_id']}\n")

        # Set data based on mode
        if self.mode == 'train':
            self.samples = train_data
            self.gengt = train_labels
        elif self.mode == 'test':
            self.samples = test_data
            self.gengt = test_labels
        else:  # val
            self.samples = val_data
            self.gengt = val_labels

        # Apply sampling if needed
        if self.sample_ratio < 1.0:
            num_samples = int(len(self.samples) * self.sample_ratio)
            indices = random.sample(range(len(self.samples)), num_samples)
            self.samples = [self.samples[i] for i in indices]
            self.gengt = [self.gengt[i] for i in indices]

        print(f"Loaded {len(self.samples)} samples for {self.mode}")

    def load_knowledge_graph(self):
        """Load knowledge graph data."""
        print("Loading knowledge graph...")

        # Load entity and relation mappings
        kg_pickle_path = os.path.join(self.kg_path, 'nuswide_knowledge_graph.pkl')

        if os.path.exists(kg_pickle_path):
            with open(kg_pickle_path, 'rb') as f:
                kg_data = pickle.load(f)
                self.entity_id = kg_data['entities']
                self.relations = kg_data['relations']
                self.triples = kg_data['triples']
        else:
            # Load entity mappings
            self.entity_id = {}
            with open(os.path.join(self.kg_path, 'nuswide_entity2id.txt'), 'r') as f:
                lines = f.readlines()[1:]  # Skip header
                for line in lines:
                    parts = line.strip().split('\t')
                    if len(parts) == 2:
                        self.entity_id[parts[0]] = int(parts[1])

        # Load KG embeddings if available
        kg_embed_path = os.path.join(self.kg_path, 'nuswide_entity_embeddings.json')
        if os.path.exists(kg_embed_path):
            with open(kg_embed_path, 'r') as f:
                self.kg_embeddings = json.load(f)
        else:
            # Initialize random embeddings if not available
            print("KG embeddings not found, initializing random embeddings")
            self.kg_embeddings = {
                str(entity_id): np.random.randn(200).tolist()  # 200-dim embeddings
                for entity, entity_id in self.entity_id.items()
            }

    def setup_preprocessor(self):
        """Set up image preprocessor."""
        from torchvision import transforms

        self.preprocessor = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225])
        ])

    def normalize_text(self, text):
        """Normalize text data."""
        text = text.lower()
        text = re.sub(r'<br />', r' ', text).strip()
        text = re.sub(r'^https?:\/\/.*[\r\n]*', ' L ', text, flags=re.MULTILINE)
        text = re.sub(r'[\~\*\+\^`_#\[\]|]', r' ', text).strip()
        text = re.sub(r'[0-9]+', r' N ', text).strip()
        text = re.sub(r'([/\'\-\.?!\(\)",:;])', r' \1 ', text).strip()
        return text

    def __len__(self):
        """Get dataset length."""
        return len(self.samples)

    def __getitem__(self, idx):
        """Get a sample from the dataset."""
        # Get text features (tags)
        text = self.samples[idx]['tags']

        # Get image features
        img_path = os.path.join(self.data_path, 'images', self.samples[idx]['image_path'])
        image = Image.open(img_path).convert('RGB')
        if self.transform:
            image = self.transform(image)
        else:
            image = self.preprocessor(image)

        # Get knowledge graph features
        kg_features = []
        kg_weight = 0
        i = 0

        # Get label embeddings
        label_embeddings = np.array([
            self.kg_embeddings[str(i)] for i in range(81)
            if str(i) in self.kg_embeddings
        ])

        # Get tag embeddings
        tags = text.split()
        for tag in tags:
            if tag in self.entity_id and str(self.entity_id[tag]) in self.kg_embeddings:
                kg_features.append(self.kg_embeddings[str(self.entity_id[tag])])
                i += 1

        # Combine KG features
        if len(kg_features) == 0:
            kg_features = np.zeros(200)  # Default dimension
        else:
            kg_features = np.mean(kg_features, axis=0)

        # Calculate KG weight
        kg_weight = i / (i + 6) if i > 0 else 0

        return {
            'image': image,
            'text': text,
            'labels': torch.FloatTensor(self.gengt[idx]),
            'kg_features': torch.FloatTensor(kg_features),
            'label_embeddings': torch.FloatTensor(label_embeddings),
            'kg_weight': kg_weight,
            'image_id': self.samples[idx]['image_id']
        }
