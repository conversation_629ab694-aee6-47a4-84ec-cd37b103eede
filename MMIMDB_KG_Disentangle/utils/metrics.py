#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
评估指标工具
提供计算互信息、正交性等指标的函数
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cross_decomposition import CCA
from scipy.stats import entropy
import torch

def estimate_mutual_information(X, Y, bins=20):
    """
    使用直方图方法（binning）估计互信息
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        bins (int): 每个维度的箱数
        
    返回:
        float: 估计的互信息值
    """
    # 如果X或Y是多维的，将其降维为1维
    if X.shape[1] > 1:
        X_1d = np.mean(X, axis=1)
    else:
        X_1d = X.ravel()
        
    if Y.shape[1] > 1:
        Y_1d = np.mean(Y, axis=1)
    else:
        Y_1d = Y.ravel()
    
    # 计算联合直方图
    hist_xy, _, _ = np.histogram2d(X_1d, Y_1d, bins=bins)
    # 归一化为概率分布
    pxy = hist_xy / float(np.sum(hist_xy))
    # 计算边缘概率分布
    px = np.sum(pxy, axis=1)
    py = np.sum(pxy, axis=0)
    
    # 计算边缘熵
    hx = entropy(px, base=2)
    hy = entropy(py, base=2)
    
    # 计算联合熵
    hxy = entropy(pxy.flatten(), base=2)
    
    # 计算互信息
    mi = hx + hy - hxy
    
    # 归一化互信息
    if hx > 0 and hy > 0:
        normalized_mi = mi / np.sqrt(hx * hy)
    else:
        normalized_mi = 0
    
    return normalized_mi

def compute_orthogonality(X, Y):
    """
    计算两组特征之间的正交性
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        
    返回:
        float: 正交性指标，值越小表示越正交
    """
    # 计算余弦相似度
    cosine_sim = np.mean([
        cosine_similarity(X[i:i+1], Y[i:i+1])[0][0]
        for i in range(len(X))
    ])
    
    # 计算正交性（1 - 余弦相似度的绝对值）
    orthogonality = 1 - np.abs(cosine_sim)
    
    return orthogonality

def compute_feature_norm(X):
    """
    计算特征的L2范数
    
    参数:
        X (numpy.ndarray): 特征，形状为 (n_samples, n_features)
        
    返回:
        float: 平均L2范数
    """
    return np.mean(np.linalg.norm(X, axis=1))

def compute_feature_ratio(X, Y):
    """
    计算两组特征的范数比率
    
    参数:
        X (numpy.ndarray): 第一组特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二组特征，形状为 (n_samples, n_features_y)
        
    返回:
        float: X相对于Y的范数比率
    """
    X_norm = compute_feature_norm(X)
    Y_norm = compute_feature_norm(Y)
    
    if Y_norm > 0:
        return X_norm / Y_norm
    else:
        return 0

def compute_modality_separation(X, Y):
    """
    计算两个模态特征之间的分离度
    
    参数:
        X (numpy.ndarray): 第一个模态特征，形状为 (n_samples, n_features_x)
        Y (numpy.ndarray): 第二个模态特征，形状为 (n_samples, n_features_y)
        
    返回:
        dict: 包含不同分离度指标的字典
    """
    # 计算余弦相似度
    cosine_sim = np.mean([
        cosine_similarity(X[i:i+1], Y[i:i+1])[0][0]
        for i in range(len(X))
    ])
    
    # 使用CCA计算模态间的相关性
    n_components = min(10, X.shape[1], Y.shape[1])
    cca = CCA(n_components=n_components)
    cca.fit(X, Y)
    X_c, Y_c = cca.transform(X, Y)
    cca_corr = np.mean([np.corrcoef(X_c[:, i], Y_c[:, i])[0, 1] for i in range(n_components)])
    
    return {
        'cosine_similarity': cosine_sim,
        'cca_correlation': cca_corr,
        'separation_score': 1 - np.abs(cosine_sim)
    }

def calculate_metrics(features):
    """
    计算各种解缠指标
    
    参数:
        features (dict): 包含不同特征表示的字典
        
    返回:
        dict: 包含各种指标的字典
    """
    metrics = {}
    
    # 1. 互信息指标
    metrics['MI(MIR, Text-EMSR)'] = estimate_mutual_information(features['mir'], features['text_emsr'])
    metrics['MI(MIR, Visual-EMSR)'] = estimate_mutual_information(features['mir'], features['visual_emsr'])
    metrics['MI(MIR, Text-IMSR)'] = estimate_mutual_information(features['mir'], features['text_imsr'])
    metrics['MI(MIR, Visual-IMSR)'] = estimate_mutual_information(features['mir'], features['visual_imsr'])
    metrics['MI(Text-EMSR, Visual-EMSR)'] = estimate_mutual_information(features['text_emsr'], features['visual_emsr'])
    metrics['MI(KG, MIR)'] = estimate_mutual_information(features['kg'], features['mir'])
    metrics['MI(KG, Text-EMSR)'] = estimate_mutual_information(features['kg'], features['text_emsr'])
    metrics['MI(KG, Visual-EMSR)'] = estimate_mutual_information(features['kg'], features['visual_emsr'])
    
    # 2. 正交性指标
    metrics['Ortho(MIR, Text-EMSR)'] = compute_orthogonality(features['mir'], features['text_emsr'])
    metrics['Ortho(MIR, Visual-EMSR)'] = compute_orthogonality(features['mir'], features['visual_emsr'])
    metrics['Ortho(MIR, Text-IMSR)'] = compute_orthogonality(features['mir'], features['text_imsr'])
    metrics['Ortho(MIR, Visual-IMSR)'] = compute_orthogonality(features['mir'], features['visual_imsr'])
    metrics['Ortho(Text-EMSR, Text-IMSR)'] = compute_orthogonality(features['text_emsr'], features['text_imsr'])
    metrics['Ortho(Visual-EMSR, Visual-IMSR)'] = compute_orthogonality(features['visual_emsr'], features['visual_imsr'])
    
    # 3. IMSR信息量指标
    metrics['Text-IMSR Norm'] = compute_feature_norm(features['text_imsr'])
    metrics['Visual-IMSR Norm'] = compute_feature_norm(features['visual_imsr'])
    
    # 计算IMSR相对于总特征的比例
    text_total = features['text_emsr'] + features['text_imsr'] + features['mir']
    visual_total = features['visual_emsr'] + features['visual_imsr'] + features['mir']
    
    metrics['Text-IMSR Ratio'] = compute_feature_ratio(features['text_imsr'], text_total)
    metrics['Visual-IMSR Ratio'] = compute_feature_ratio(features['visual_imsr'], visual_total)
    
    # 4. 模态分离指标
    modality_separation = compute_modality_separation(features['text_emsr'], features['visual_emsr'])
    metrics['EMSR Cosine Similarity'] = modality_separation['cosine_similarity']
    metrics['EMSR CCA Correlation'] = modality_separation['cca_correlation']
    metrics['EMSR Separation Score'] = modality_separation['separation_score']
    
    # 5. 知识图谱贡献指标
    metrics['KG-MIR Similarity'] = 1 - compute_orthogonality(features['kg'], features['mir'])
    metrics['KG-Text-EMSR Similarity'] = 1 - compute_orthogonality(features['kg'], features['text_emsr'])
    metrics['KG-Visual-EMSR Similarity'] = 1 - compute_orthogonality(features['kg'], features['visual_emsr'])
    metrics['KG Information Content'] = compute_feature_norm(features['kg'])
    
    return metrics
