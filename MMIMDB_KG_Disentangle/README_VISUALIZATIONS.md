# 可视化工具使用指南

本文档介绍如何使用可视化工具来分析模型性能、特征空间和跨模态冗余。

## 概述

我们提供了以下可视化工具：

1. **特征空间可视化**：可视化文本和视觉特征空间，以及跨模态冗余
2. **结果可视化**：可视化分类指标和解缠指标
3. **冗余分析**：分析跨模态冗余的分布和影响
4. **训练动态分析**：分析训练过程中冗余检测的影响
5. **消融实验可视化**：可视化消融实验结果

## 快速开始

使用以下命令运行所有可视化：

```bash
./run_visualizations.sh --model_path <path_to_model> [--output_dir <output_dir>] [--device <cuda|cpu>] [--interactive] [--num_samples <num_samples>] [--save_features]
```

例如：

```bash
./run_visualizations.sh --model_path ./output/kg_disentangle_v1/best_model.pth --output_dir ./output/visualizations --device cuda --interactive --num_samples 1000 --save_features
```

## 参数说明

- `--model_path`：模型路径（必需）
- `--output_dir`：输出目录（默认：./output/visualizations）
- `--device`：设备（默认：cuda）
- `--interactive`：生成交互式可视化（可选）
- `--num_samples`：可视化的样本数量（默认：1000）
- `--save_features`：保存提取的特征（可选）

## 单独运行各个可视化工具

### 1. 特征空间可视化

```bash
python visualize_features.py --model_path <path_to_model> --output_dir <output_dir> --device <device> [--interactive] [--num_samples <num_samples>] [--save_features]
```

这将生成以下可视化：

- 2D特征空间（t-SNE、UMAP、PCA）
- 3D特征空间（t-SNE、UMAP、PCA）
- 交互式3D特征空间（如果使用--interactive）

### 2. 结果可视化

```bash
python visualize_results.py --results_dir <results_dir> --output_dir <output_dir> [--interactive] [--compare_dirs <compare_dir1> <compare_dir2> ...] [--compare_names <name1> <name2> ...]
```

这将生成以下可视化：

- 分类指标雷达图和条形图
- 解缠指标雷达图和条形图
- 不同模型的指标比较（如果提供了比较目录）
- 交互式可视化（如果使用--interactive）

### 3. 冗余分析

```bash
python redundancy_analysis.py --model_path <path_to_model> --output_dir <output_dir> --device <device>
```

这将生成以下可视化：

- 冗余分数分布
- 特征空间中的冗余
- 按类别的冗余
- 特征相关性
- 冗余对特征精炼的影响

### 4. 训练动态分析

```bash
python training_dynamics_analysis.py --log_dir <log_dir> --output_dir <output_dir> [--compare_logs <log1> <log2> ...] [--compare_names <name1> <name2> ...]
```

这将生成以下可视化：

- 训练曲线（损失、F1分数）
- 损失组件（分类损失、冗余损失）
- 收敛速度比较（如果提供了比较日志）

## 可视化结果说明

### 特征空间可视化

特征空间可视化显示了文本和视觉特征在降维后的分布，颜色表示冗余分数。这些可视化可以帮助您理解：

- 跨模态冗余在特征空间中的分布
- 冗余检测对特征空间的影响
- 原始特征和精炼特征之间的差异

### 结果可视化

结果可视化显示了模型的分类指标和解缠指标。这些可视化可以帮助您理解：

- 模型在分类任务上的性能
- 模型在特征解缠方面的性能
- 不同模型配置之间的性能差异

### 冗余分析

冗余分析显示了跨模态冗余的详细信息。这些可视化可以帮助您理解：

- 冗余分数的分布
- 冗余与特征之间的关系
- 冗余对不同类别的影响
- 冗余检测的效果

### 训练动态分析

训练动态分析显示了训练过程中的指标变化。这些可视化可以帮助您理解：

- 训练过程中损失和F1分数的变化
- 冗余损失和分类损失的关系
- 冗余检测对训练收敛的影响
- 不同模型配置的训练动态差异

## 交互式可视化

如果使用`--interactive`参数，将生成交互式可视化，可以在浏览器中查看。这些可视化允许您：

- 旋转和缩放3D特征空间
- 悬停查看数据点的详细信息
- 筛选和突出显示特定数据点
- 调整可视化参数

交互式可视化保存为HTML文件，可以在任何现代浏览器中打开。

## 注意事项

1. 生成3D可视化和交互式可视化可能需要较长时间，特别是对于大型数据集
2. 交互式可视化需要安装plotly库：`pip install plotly`
3. 保存特征可能占用大量磁盘空间，特别是对于大型数据集
4. 对于大型数据集，建议减少样本数量（使用`--num_samples`参数）
