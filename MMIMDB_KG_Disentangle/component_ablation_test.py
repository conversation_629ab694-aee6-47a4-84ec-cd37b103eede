"""
Component Ablation Test for Multimodal Feature Disentanglement Model.
This script performs ablation tests on different components of the model:
- No Knowledge Graph (no_kg)
- No Redundancy Detection (no_redundancy)
- No Graph Reasoning (no_graph_reasoning)
- No Adaptive Fusion (no_adaptive_fusion)
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import argparse
from torch.utils.data import DataLoader
import logging
from tabulate import tabulate
import pandas as pd
import sklearn.metrics

# Import project modules
from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
from enhanced_evaluate import compute_disentanglement_metrics

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Component Ablation Test")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/component_ablation',
                        help='Output directory for saving results')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Experiment arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')

    return parser.parse_args()

def compute_metrics(y_true, y_pred):
    """
    Compute standard metrics for multi-label classification.

    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities

    Returns:
        dict: Dictionary of metrics
    """
    # Convert predictions to binary using 0.5 threshold
    y_pred_binary = (y_pred > 0.5).astype(int)

    # Compute metrics
    f1_micro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='micro')
    f1_macro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='macro')
    f1_samples = sklearn.metrics.f1_score(y_true, y_pred_binary, average='samples')
    precision = sklearn.metrics.precision_score(y_true, y_pred_binary, average='micro')
    recall = sklearn.metrics.recall_score(y_true, y_pred_binary, average='micro')

    # Compute mAP
    ap_scores = []
    for i in range(y_true.shape[1]):
        if np.sum(y_true[:, i]) > 0:  # Only compute AP if there are positive samples
            ap = sklearn.metrics.average_precision_score(y_true[:, i], y_pred[:, i])
            ap_scores.append(ap)
    mAP = np.mean(ap_scores) if ap_scores else 0.0

    return {
        'f1_micro': f1_micro,
        'f1_macro': f1_macro,
        'f1': f1_samples,  # This is the sample-averaged F1 score
        'precision': precision,
        'recall': recall,
        'mAP': mAP
    }

class ComponentAblationModel(KGDisentangleNet):
    """
    Model for component ablation tests.
    This model extends the KGDisentangleNet to allow disabling specific components.
    """

    def __init__(self, text_dim, visual_dim, kg_dim, hidden_dim, num_classes,
                 disable_kg=False, disable_redundancy=False,
                 disable_graph_reasoning=False, disable_adaptive_fusion=False):
        """
        Initialize the model.

        Args:
            text_dim (int): Dimension of text features
            visual_dim (int): Dimension of visual features
            kg_dim (int): Dimension of knowledge graph embeddings
            hidden_dim (int): Dimension of hidden layers
            num_classes (int): Number of output classes
            disable_kg (bool): Whether to disable knowledge graph
            disable_redundancy (bool): Whether to disable redundancy detection
            disable_graph_reasoning (bool): Whether to disable graph reasoning
            disable_adaptive_fusion (bool): Whether to disable adaptive fusion
        """
        super().__init__(text_dim, visual_dim, kg_dim, hidden_dim, num_classes)

        self.disable_kg = disable_kg
        self.disable_redundancy = disable_redundancy
        self.disable_graph_reasoning = disable_graph_reasoning
        self.disable_adaptive_fusion = disable_adaptive_fusion
        self.kg_dim = kg_dim  # Store kg_dim for creating zero tensors

        # Create a simple fusion layer for when adaptive fusion is disabled
        if disable_adaptive_fusion:
            self.simple_fusion = torch.nn.Linear(hidden_dim * 2, hidden_dim)

    def forward(self, text_features, visual_features, kg_features=None, label_embeddings=None):
        """
        Forward pass with component ablation.

        Args:
            text_features (torch.Tensor): Text features
            visual_features (torch.Tensor): Visual features
            kg_features (torch.Tensor, optional): Knowledge graph features
            label_embeddings (torch.Tensor, optional): Label embeddings

        Returns:
            dict: Dictionary of outputs
        """
        # Encode text and visual features
        text_encoded = self.text_encoder(text_features)
        visual_encoded = self.visual_encoder(visual_features)

        # Initialize outputs dictionary
        outputs = {
            'text_encoded': text_encoded,
            'visual_encoded': visual_encoded
        }

        # Disable knowledge graph if specified
        if self.disable_kg or kg_features is None:
            # Create zero tensors with correct dimensions
            kg_encoded = torch.zeros_like(text_encoded)  # [batch_size, hidden_dim]
            # For kg_features_3d, we need to match the original kg_features dimension
            # Original kg_features is [batch_size, kg_dim]
            # So kg_features_3d should be [batch_size, 1, kg_dim]
            batch_size = text_encoded.size(0)
            kg_features_3d = torch.zeros(batch_size, 1, self.kg_dim).to(text_encoded.device)
        else:
            kg_encoded = self.kg_encoder(kg_features)
            kg_features_3d = kg_features.unsqueeze(1)  # Add sequence dimension

        outputs['kg_encoded'] = kg_encoded

        # Add sequence dimension for 3D processing
        text_encoded_3d = text_encoded.unsqueeze(1)  # [batch_size, 1, hidden_dim]
        visual_encoded_3d = visual_encoded.unsqueeze(1)  # [batch_size, 1, hidden_dim]

        # Disable redundancy detection if specified
        if self.disable_redundancy:
            redundancy_score = torch.zeros(text_encoded.size(0), 1).to(text_encoded.device)
            attn_weights = None
        else:
            # Compute redundancy score using 3D tensors
            redundancy_score, attn_weights = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
            # Remove the extra dimension from redundancy_score if needed
            if len(redundancy_score.shape) == 3:
                redundancy_score = redundancy_score.squeeze(1)

        outputs['redundancy_score'] = redundancy_score
        outputs['attention_weights'] = attn_weights

        # Disable graph reasoning if specified
        if self.disable_graph_reasoning:
            # Skip graph reasoning
            text_refined = text_encoded
            visual_refined = visual_encoded
        else:
            # Apply graph reasoning using 3D tensors
            text_refined_3d = self.graph_reasoner(text_encoded_3d, kg_features_3d)
            visual_refined_3d = self.graph_reasoner(visual_encoded_3d, kg_features_3d)

            # Remove sequence dimension
            text_refined = text_refined_3d.squeeze(1)
            visual_refined = visual_refined_3d.squeeze(1)

        outputs['text_refined'] = text_refined
        outputs['visual_refined'] = visual_refined

        # Disable adaptive fusion if specified
        if self.disable_adaptive_fusion:
            # Simple concatenation and linear projection
            fused = torch.cat([text_refined, visual_refined], dim=1)
            fused = self.simple_fusion(fused)
        else:
            # Apply adaptive fusion
            fused = self.adaptive_fusion(
                text_refined,
                visual_refined,
                kg_encoded,
                redundancy_score
            )

        outputs['fused'] = fused

        # Apply classifier
        logits = self.enhanced_classifier(fused)
        outputs['logits'] = logits

        return outputs

def evaluate_model_with_ablation(model, test_loader, args, ablation_config):
    """
    Evaluate model with component ablation.

    Args:
        model (torch.nn.Module): Model to evaluate
        test_loader (torch.utils.data.DataLoader): Test data loader
        args (argparse.Namespace): Command line arguments
        ablation_config (dict): Ablation configuration

    Returns:
        dict: Evaluation metrics
    """
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')

    # Create ablation model
    ablation_model = ComponentAblationModel(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes,
        disable_kg=ablation_config.get('disable_kg', False),
        disable_redundancy=ablation_config.get('disable_redundancy', False),
        disable_graph_reasoning=ablation_config.get('disable_graph_reasoning', False),
        disable_adaptive_fusion=ablation_config.get('disable_adaptive_fusion', False)
    ).to(device)

    # Load model weights
    ablation_model.load_state_dict(model.state_dict(), strict=False)
    ablation_model.eval()

    # Evaluation
    test_preds = []
    test_labels = []
    all_text_encoded = []
    all_visual_encoded = []
    all_text_refined = []
    all_visual_refined = []
    all_redundancy_scores = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc=f"Testing ({ablation_config['name']})"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device) if not ablation_config.get('disable_kg', False) else None
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image

            # Forward pass
            outputs = ablation_model(text_features, image_features, kg_features, label_embeddings)

            # Collect predictions and labels
            test_preds.append(torch.sigmoid(outputs['logits']).cpu().numpy())
            test_labels.append(labels.cpu().numpy())

            # Collect features for disentanglement metrics
            all_text_encoded.append(outputs['text_encoded'].cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].cpu().numpy())

            # Collect refined features if available
            if 'text_refined' in outputs:
                all_text_refined.append(outputs['text_refined'].cpu().numpy())
            if 'visual_refined' in outputs:
                all_visual_refined.append(outputs['visual_refined'].cpu().numpy())

    # Stack all predictions and labels
    test_preds = np.vstack(test_preds)
    test_labels = np.vstack(test_labels)

    # Stack all features
    all_text_encoded = np.vstack(all_text_encoded)
    all_visual_encoded = np.vstack(all_visual_encoded)
    all_redundancy_scores = np.vstack(all_redundancy_scores)

    # Compute standard metrics
    standard_metrics = compute_metrics(test_labels, test_preds)

    # Compute disentanglement metrics
    disentanglement_metrics = compute_disentanglement_metrics(
        all_text_encoded, all_visual_encoded, all_redundancy_scores
    )

    # Combine all metrics
    all_metrics = {**standard_metrics, **disentanglement_metrics}

    return all_metrics

def visualize_comparison(results, output_dir):
    """
    Visualize comparison of different ablation configurations.

    Args:
        results (dict): Dictionary of results for each ablation configuration
        output_dir (str): Output directory
    """
    os.makedirs(output_dir, exist_ok=True)

    # Extract classification metrics
    classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP']

    # Create dataframe for classification metrics
    classification_data = []
    for config_name, metrics in results.items():
        for metric in classification_metrics:
            if metric in metrics:
                classification_data.append({
                    'Configuration': config_name,
                    'Metric': metric,
                    'Value': metrics[metric]
                })

    classification_df = pd.DataFrame(classification_data)

    # Create bar chart for classification metrics
    plt.figure(figsize=(15, 10))
    sns.barplot(x='Metric', y='Value', hue='Configuration', data=classification_df)

    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Classification Metrics Comparison', fontsize=18, pad=20)

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)

    # Add legend
    plt.legend(title='Configuration', fontsize=12, title_fontsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'classification_metrics_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # Extract disentanglement metrics
    disentanglement_metrics = [
        'modality_disentanglement_score',
        'cross_modal_redundancy',
        'feature_independence',
        'modality_specificity',
        'text_specificity',
        'visual_specificity',
        'shared_information_preservation',
        'mutual_information',
        'text_to_image_transfer',
        'image_to_text_transfer',
        'average_orthogonality',
        'modality_separation',
        'shared_information',
        'text_specific_information',
        'visual_specific_information'
    ]

    # Filter metrics that exist in results
    available_metrics = []
    for metric in disentanglement_metrics:
        if any(metric in metrics for metrics in results.values()):
            available_metrics.append(metric)

    # Create dataframe for disentanglement metrics
    disentanglement_data = []
    for config_name, metrics in results.items():
        for metric in available_metrics:
            if metric in metrics:
                disentanglement_data.append({
                    'Configuration': config_name,
                    'Metric': metric,
                    'Value': metrics[metric]
                })

    disentanglement_df = pd.DataFrame(disentanglement_data)

    # Create bar chart for disentanglement metrics
    plt.figure(figsize=(18, 12))
    sns.barplot(x='Metric', y='Value', hue='Configuration', data=disentanglement_df)

    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Disentanglement Metrics Comparison', fontsize=18, pad=20)

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)

    # Add legend
    plt.legend(title='Configuration', fontsize=12, title_fontsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'disentanglement_metrics_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # Create comparison table
    table_data = []
    headers = ['Metric'] + list(results.keys())

    # Classification metrics
    table_data.append(["=== Classification Metrics ==="])
    for metric in classification_metrics:
        row = [metric]
        for config_name in results.keys():
            if metric in results[config_name]:
                row.append(f"{results[config_name][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)

    # Disentanglement metrics
    table_data.append([""])
    table_data.append(["=== Disentanglement Metrics ==="])
    for metric in available_metrics:
        row = [metric]
        for config_name in results.keys():
            if metric in results[config_name]:
                row.append(f"{results[config_name][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)

    # Print comparison table
    logger.info("\nMetrics Comparison:")
    logger.info("\n" + tabulate(table_data, headers=headers, tablefmt='grid'))

    # Save comparison table
    with open(os.path.join(output_dir, 'metrics_comparison.txt'), 'w') as f:
        f.write(tabulate(table_data, headers=headers, tablefmt='grid'))

    # Save comparison table as CSV
    with open(os.path.join(output_dir, 'metrics_comparison.csv'), 'w') as f:
        f.write(','.join(['Metric'] + list(results.keys())) + '\n')
        for row in table_data:
            if len(row) == 1:  # Section header
                f.write(f"{row[0]}\n")
            else:
                f.write(','.join([str(cell) for cell in row]) + '\n')

    # Create LaTeX table
    latex_table = "\\begin{table}[htbp]\n\\centering\n\\caption{Component Ablation Study Results}\n\\label{tab:component_ablation}\n"
    latex_table += "\\begin{tabular}{l" + "c" * len(results.keys()) + "}\n\\toprule\n"

    # Add headers
    latex_table += "Metric & " + " & ".join(results.keys()) + " \\\\\n\\midrule\n"

    # Add classification metrics
    latex_table += "\\multicolumn{" + str(len(results.keys()) + 1) + "}{l}{\\textbf{Classification Metrics}} \\\\\n"
    for i, row in enumerate(table_data):
        if i == 0 or len(row) == 1:  # Skip section headers
            continue
        if row[0] == "":  # Reached disentanglement metrics
            break
        latex_table += row[0] + " & " + " & ".join(row[1:]) + " \\\\\n"

    # Add disentanglement metrics
    latex_table += "\\midrule\n\\multicolumn{" + str(len(results.keys()) + 1) + "}{l}{\\textbf{Disentanglement Metrics}} \\\\\n"
    disentanglement_started = False
    for row in table_data:
        if len(row) == 1 and row[0] == "=== Disentanglement Metrics ===":
            disentanglement_started = True
            continue
        if disentanglement_started and len(row) > 1:
            latex_table += row[0] + " & " + " & ".join(row[1:]) + " \\\\\n"

    latex_table += "\\bottomrule\n\\end{tabular}\n\\end{table}"

    # Save LaTeX table
    with open(os.path.join(output_dir, 'metrics_comparison.tex'), 'w') as f:
        f.write(latex_table)

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Create test dataset
    logger.info("Creating test dataset...")
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    # Create test data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Load full model
    logger.info(f"Loading model from {args.model_path}...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)

    logger.info("Model created, loading state dict...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    logger.info("Model loaded successfully.")
    model.eval()

    # Define ablation configurations
    ablation_configs = [
        {
            'name': 'Full Model',
            'disable_kg': False,
            'disable_redundancy': False,
            'disable_graph_reasoning': False,
            'disable_adaptive_fusion': False
        },
        {
            'name': 'No KG',
            'disable_kg': True,
            'disable_redundancy': False,
            'disable_graph_reasoning': False,
            'disable_adaptive_fusion': False
        },
        {
            'name': 'No Redundancy',
            'disable_kg': False,
            'disable_redundancy': True,
            'disable_graph_reasoning': False,
            'disable_adaptive_fusion': False
        },
        {
            'name': 'No Graph Reasoning',
            'disable_kg': False,
            'disable_redundancy': False,
            'disable_graph_reasoning': True,
            'disable_adaptive_fusion': False
        },
        {
            'name': 'No Adaptive Fusion',
            'disable_kg': False,
            'disable_redundancy': False,
            'disable_graph_reasoning': False,
            'disable_adaptive_fusion': True
        }
    ]

    # Evaluate each ablation configuration
    results = {}

    for config in ablation_configs:
        logger.info(f"\nEvaluating {config['name']}...")
        metrics = evaluate_model_with_ablation(model, test_loader, args, config)
        results[config['name']] = metrics

        # Log metrics
        logger.info(f"\nResults for {config['name']}:")
        logger.info(f"F1 (Samples): {metrics['f1']:.4f}")
        logger.info(f"F1-Micro: {metrics['f1_micro']:.4f}")
        logger.info(f"F1-Macro: {metrics['f1_macro']:.4f}")
        logger.info(f"Precision: {metrics['precision']:.4f}")
        logger.info(f"Recall: {metrics['recall']:.4f}")
        logger.info(f"mAP: {metrics['mAP']:.4f}")

        logger.info("\n=== Disentanglement Metrics ===")
        logger.info(f"Modality Disentanglement Score: {metrics['modality_disentanglement_score']:.4f}")
        logger.info(f"Cross-Modal Redundancy: {metrics['cross_modal_redundancy']:.4f}")
        logger.info(f"Feature Independence: {metrics['feature_independence']:.4f}")

        # Modality Specificity
        if 'modality_specificity' in metrics:
            logger.info(f"\nModality Specificity: {metrics['modality_specificity']:.4f}")
            logger.info(f"  - Text Specificity: {metrics['text_specificity']:.4f}")
            logger.info(f"  - Visual Specificity: {metrics['visual_specificity']:.4f}")

        # Information Metrics
        logger.info(f"\nInformation Metrics:")
        logger.info(f"Shared Information Preservation: {metrics['shared_information_preservation']:.4f}")
        logger.info(f"Mutual Information: {metrics['mutual_information']:.4f}")

        # Cross-Modal Transfer
        if 'text_to_image_transfer' in metrics:
            logger.info(f"\nCross-Modal Transfer:")
            logger.info(f"Text → Image Transfer: {metrics['text_to_image_transfer']:.4f}")
            logger.info(f"Image → Text Transfer: {metrics['image_to_text_transfer']:.4f}")

        # Orthogonality and Separation
        if 'average_orthogonality' in metrics:
            logger.info(f"\nOrthogonality and Separation:")
            logger.info(f"Average Orthogonality: {metrics['average_orthogonality']:.4f}")
            logger.info(f"Modality Separation: {metrics['modality_separation']:.4f}")

        # Shared and Specific Information
        if 'shared_information' in metrics:
            logger.info(f"\nShared and Specific Information:")
            logger.info(f"Shared Information: {metrics['shared_information']:.4f}")
            logger.info(f"Text-Specific Information: {metrics['text_specific_information']:.4f}")
            logger.info(f"Visual-Specific Information: {metrics['visual_specific_information']:.4f}")

        # Save metrics to JSON
        config_output_dir = os.path.join(args.output_dir, config['name'].lower().replace(' ', '_'))
        os.makedirs(config_output_dir, exist_ok=True)

        # Convert NumPy values to Python native types for JSON serialization
        json_safe_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, np.ndarray):
                json_safe_metrics[key] = value.tolist()
            elif isinstance(value, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                                   np.uint8, np.uint16, np.uint32, np.uint64)):
                json_safe_metrics[key] = int(value)
            elif isinstance(value, (np.float_, np.float16, np.float32, np.float64)):
                json_safe_metrics[key] = float(value)
            elif value is np.nan:
                json_safe_metrics[key] = None
            else:
                json_safe_metrics[key] = value

        with open(os.path.join(config_output_dir, 'test_results.json'), 'w') as f:
            json.dump(json_safe_metrics, f, indent=2)

    # Visualize comparison
    logger.info("\nVisualizing comparison...")
    visualize_comparison(results, os.path.join(args.output_dir, 'comparison'))

    # Save all results to a single JSON file
    all_results = {}
    for config_name, metrics in results.items():
        # Convert NumPy values to Python native types for JSON serialization
        json_safe_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, np.ndarray):
                json_safe_metrics[key] = value.tolist()
            elif isinstance(value, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                                   np.uint8, np.uint16, np.uint32, np.uint64)):
                json_safe_metrics[key] = int(value)
            elif isinstance(value, (np.float_, np.float16, np.float32, np.float64)):
                json_safe_metrics[key] = float(value)
            elif value is np.nan:
                json_safe_metrics[key] = None
            else:
                json_safe_metrics[key] = value

        all_results[config_name] = json_safe_metrics

    with open(os.path.join(args.output_dir, 'all_results.json'), 'w') as f:
        json.dump(all_results, f, indent=2)

    logger.info(f"\nDone! Results saved to {args.output_dir}")

if __name__ == '__main__':
    main()
