"""
<PERSON><PERSON><PERSON> to generate modality-specific and class-specific visualizations similar to the provided example.
This script creates t-SNE visualizations showing:
1. Shared representation with class-based coloring
2. Modality-specific representation with modality-based coloring
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import torch
from torch.utils.data import DataLoader
from sklearn.manifold import TSNE
import argparse
from tqdm import tqdm
import seaborn as sns
from matplotlib.lines import Line2D

# Import project modules
from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet

# Set up beautiful visualization style
plt.style.use('seaborn-v0_8-whitegrid')
custom_params = {
    'axes.spines.right': False,
    'axes.spines.top': False,
    'axes.grid': True,
    'grid.linestyle': '--',
    'grid.alpha': 0.7,
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif'],
    'figure.figsize': (20, 8),
    'figure.dpi': 150,
}
plt.rcParams.update(custom_params)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Generate modality and class visualizations")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/visualizations/modality_class',
                        help='Output directory for saving visualizations')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Visualization arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--num_samples', type=int, default=500,
                        help='Number of samples to visualize')
    parser.add_argument('--perplexity', type=int, default=30,
                        help='Perplexity for t-SNE')
    parser.add_argument('--n_iter', type=int, default=1000,
                        help='Number of iterations for t-SNE')
    parser.add_argument('--num_classes_to_show', type=int, default=5,
                        help='Number of classes to show in visualization')

    return parser.parse_args()

def collect_features_and_labels(model, data_loader, device, max_samples=500):
    """Collect features and labels from the model."""
    model.eval()

    # Lists to store features and labels
    text_features = []
    visual_features = []
    shared_features = []
    text_specific_features = []
    visual_specific_features = []
    labels = []

    sample_count = 0

    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Collecting features"):
            if sample_count >= max_samples:
                break

            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            batch_labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors
            text_input = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_input = image.view(batch_size, -1)
            else:
                image_input = image

            # Forward pass
            outputs = model(text_input, image_input, kg_features, label_embeddings)

            # Collect features
            text_features.append(outputs['text_encoded'].cpu().numpy())
            visual_features.append(outputs['visual_encoded'].cpu().numpy())

            # Get shared and modality-specific features if available
            if 'shared_features' in outputs:
                shared_features.append(outputs['shared_features'].cpu().numpy())
            if 'text_specific' in outputs:
                text_specific_features.append(outputs['text_specific'].cpu().numpy())
            if 'visual_specific' in outputs:
                visual_specific_features.append(outputs['visual_specific'].cpu().numpy())

            # Collect labels
            labels.append(batch_labels.cpu().numpy())

            sample_count += len(text)

    # Concatenate all features and labels
    features = {
        'text_encoded': np.vstack(text_features)[:max_samples],
        'visual_encoded': np.vstack(visual_features)[:max_samples],
        'labels': np.vstack(labels)[:max_samples]
    }

    if shared_features:
        features['shared_features'] = np.vstack(shared_features)[:max_samples]
    if text_specific_features:
        features['text_specific'] = np.vstack(text_specific_features)[:max_samples]
    if visual_specific_features:
        features['visual_specific'] = np.vstack(visual_specific_features)[:max_samples]

    return features

def create_modality_class_visualization(features, output_dir, perplexity=30, n_iter=1000, num_classes=5):
    """Create modality and class visualizations."""
    os.makedirs(output_dir, exist_ok=True)

    # Get labels
    labels = features['labels']

    # Get the most frequent classes
    class_counts = np.sum(labels, axis=0)
    top_classes = np.argsort(class_counts)[-num_classes:]

    # Create a mask for samples with the top classes
    mask = np.zeros(labels.shape[0], dtype=bool)
    for c in top_classes:
        mask = np.logical_or(mask, labels[:, c] > 0)

    # Filter features and labels
    filtered_text = features['text_encoded'][mask]
    filtered_visual = features['visual_encoded'][mask]
    filtered_labels = labels[mask]

    # Combine text and visual features
    combined_features = np.concatenate([filtered_text, filtered_visual], axis=0)

    # Create modality labels (0 for text, 1 for visual)
    modality_labels = np.concatenate([
        np.zeros(filtered_text.shape[0]),
        np.ones(filtered_visual.shape[0])
    ])

    # Create class labels for visualization
    class_labels = np.zeros((filtered_labels.shape[0], num_classes))
    for i, c in enumerate(top_classes):
        class_labels[:, i] = filtered_labels[:, c] > 0

    # Duplicate class labels for visual features
    class_labels = np.concatenate([class_labels, class_labels], axis=0)

    # Apply t-SNE
    print("Applying t-SNE...")
    tsne = TSNE(n_components=2, perplexity=perplexity, n_iter=n_iter, random_state=42)
    combined_tsne = tsne.fit_transform(combined_features)

    # Split back into text and visual
    text_tsne = combined_tsne[:filtered_text.shape[0]]
    visual_tsne = combined_tsne[filtered_text.shape[0]:]

    # Create a figure with two subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

    # Plot 1: Shared Representation (t-SNE) - colored by class
    class_colors = ['purple', 'blue', 'green', 'yellow', 'red']
    markers = ['o', 'x']  # o for text, x for visual

    # Plot each class
    for i, c in enumerate(range(num_classes)):
        # Text features for this class
        mask_text_class = class_labels[:filtered_text.shape[0], i] == 1
        ax1.scatter(
            text_tsne[mask_text_class, 0],
            text_tsne[mask_text_class, 1],
            color=class_colors[i],
            marker=markers[0],
            alpha=0.7,
            s=50
        )

        # Visual features for this class
        mask_visual_class = class_labels[filtered_text.shape[0]:, i] == 1
        ax1.scatter(
            visual_tsne[mask_visual_class, 0],
            visual_tsne[mask_visual_class, 1],
            color=class_colors[i],
            marker=markers[1],
            alpha=0.7,
            s=50
        )

    # Add legend for classes and modalities
    class_legend_elements = [
        Line2D([0], [0], marker='o', color='w', markerfacecolor=class_colors[i], label=f'Class {i}', markersize=8)
        for i in range(num_classes)
    ]
    modality_legend_elements = [
        Line2D([0], [0], marker='o', color='black', label='Text', markersize=8),
        Line2D([0], [0], marker='x', color='black', label='Image', markersize=8)
    ]

    ax1.legend(handles=class_legend_elements, title="Class", loc='upper right')
    ax1.add_artist(ax1.legend(handles=modality_legend_elements, title="Modality", loc='upper left'))

    ax1.set_title('Shared Representation (t-SNE)', fontsize=14)
    ax1.set_xlabel('t-SNE Dimension 1', fontsize=12)
    ax1.set_ylabel('t-SNE Dimension 2', fontsize=12)
    ax1.grid(True)

    # Plot 2: Modality-Specific Representation (t-SNE) - colored by modality
    modality_colors = ['salmon', 'purple']  # salmon for text, purple for visual

    # Text features
    ax2.scatter(
        text_tsne[:, 0],
        text_tsne[:, 1],
        color=modality_colors[0],
        marker=markers[0],
        alpha=0.7,
        s=50
    )

    # Visual features
    ax2.scatter(
        visual_tsne[:, 0],
        visual_tsne[:, 1],
        color=modality_colors[1],
        marker=markers[1],
        alpha=0.7,
        s=50
    )

    # Add legend for modalities
    modality_legend_elements = [
        Line2D([0], [0], marker='o', color='w', markerfacecolor=modality_colors[0], label='Text', markersize=8),
        Line2D([0], [0], marker='x', color='w', markerfacecolor=modality_colors[1], label='Image', markersize=8)
    ]

    # Add legend for classes
    class_legend_elements = [
        Line2D([0], [0], marker='o', color='black', label=f'Class {i}', markersize=8)
        for i in range(num_classes)
    ]

    ax2.legend(handles=modality_legend_elements, title="Modality", loc='upper right')
    ax2.add_artist(ax2.legend(handles=class_legend_elements, title="Class", loc='upper left'))

    ax2.set_title('Modality-Specific Representation (t-SNE)', fontsize=14)
    ax2.set_xlabel('t-SNE Dimension 1', fontsize=12)
    ax2.set_ylabel('t-SNE Dimension 2', fontsize=12)
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'modality_class_visualization.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Visualization saved to {os.path.join(output_dir, 'modality_class_visualization.png')}")

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create dataset
    print("Creating dataset...")
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    # Create data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Create model
    print("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)

    # Load model weights
    print(f"Loading model from {args.model_path}...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model.eval()

    # Collect features and labels
    print("Collecting features and labels...")
    features = collect_features_and_labels(
        model,
        test_loader,
        device,
        max_samples=args.num_samples
    )

    # Create modality and class visualizations
    print("Creating modality and class visualizations...")
    create_modality_class_visualization(
        features,
        args.output_dir,
        perplexity=args.perplexity,
        n_iter=args.n_iter,
        num_classes=args.num_classes_to_show
    )

    print("Done!")

if __name__ == '__main__':
    main()
