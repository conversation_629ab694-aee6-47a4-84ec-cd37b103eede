#!/bin/bash

# Script to run MIR-Flickr experiment in the background

# Create log directory
mkdir -p logs

# Set experiment name with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
EXP_NAME="mirflickr_experiment_${TIMESTAMP}"

# Set paths
DATA_PATH="/home/<USER>/workplace/dwb/data/mirflickr"
KG_PATH="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data"
OUTPUT_DIR="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output_mirflickr/${EXP_NAME}"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# First, prepare the MIR-Flickr dataset if needed
if [ ! -d "${DATA_PATH}/images" ]; then
    echo "Preparing MIR-Flickr dataset..."
    python prepare_mirflickr.py --output_dir ${DATA_PATH}
    
    # If download fails, create mock data
    if [ ! -d "${DATA_PATH}/images" ]; then
        echo "Download failed, creating mock data..."
        python prepare_mirflickr.py --output_dir ${DATA_PATH} --mock --num_mock_samples 1000
    fi
fi

# Build knowledge graph
echo "Building knowledge graph for MIR-Flickr..."
nohup python run_mirflickr.py \
    --mode build_kg \
    --data_path ${DATA_PATH} \
    --kg_path ${KG_PATH} \
    --output_dir ${OUTPUT_DIR} \
    > logs/mirflickr_build_kg_${TIMESTAMP}.log 2>&1 &

# Wait for knowledge graph to be built
echo "Waiting for knowledge graph to be built..."
sleep 60

# Run training
echo "Starting training on MIR-Flickr..."
nohup python run_mirflickr.py \
    --mode train \
    --data_path ${DATA_PATH} \
    --kg_path ${KG_PATH} \
    --output_dir ${OUTPUT_DIR} \
    --batch_size 32 \
    --num_epochs 30 \
    --lr 1e-4 \
    --weight_decay 1e-5 \
    --seed 42 \
    --device cuda \
    --exp_name ${EXP_NAME} \
    --notes "MIR-Flickr experiment with KG-Disentangle-Net" \
    > logs/mirflickr_train_${TIMESTAMP}.log 2>&1 &

echo "Experiment started in background with name: ${EXP_NAME}"
echo "Check logs in logs/mirflickr_train_${TIMESTAMP}.log"
