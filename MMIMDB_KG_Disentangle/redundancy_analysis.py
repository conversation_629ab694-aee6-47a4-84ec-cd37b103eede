"""
<PERSON><PERSON>t for analyzing and visualizing cross-modal redundancy in the model.
This script focuses on:
1. Visualizing redundancy during training vs. testing
2. Analyzing how redundancy detection affects training dynamics
3. Comparing redundancy patterns across different model components
4. Creating beautiful and informative visualizations
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec
from mpl_toolkits.mplot3d import Axes3D
from matplotlib.ticker import MaxNLocator
import pandas as pd
from torch.utils.data import DataLoader

from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
import argparse

# Set up beautiful visualization style
plt.style.use('seaborn-v0_8-whitegrid')
custom_params = {
    'axes.spines.right': False,
    'axes.spines.top': False,
    'axes.grid': True,
    'grid.linestyle': '--',
    'grid.alpha': 0.7,
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif'],
    'figure.figsize': (12, 8),
    'figure.dpi': 150,
}
plt.rcParams.update(custom_params)

# Custom color palettes
main_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
redundancy_cmap = LinearSegmentedColormap.from_list('redundancy', ['#f0f9e8', '#7bccc4', '#43a2ca', '#0868ac'])
feature_cmap = LinearSegmentedColormap.from_list('features', ['#feebe2', '#fbb4b9', '#f768a1', '#c51b8a', '#7a0177'])

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Redundancy analysis for KG-Disentangle-Net")
    
    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/visualizations',
                        help='Output directory for saving visualizations')
    
    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')
    
    # Evaluation arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    
    return parser.parse_args()

def collect_features_and_redundancy(model, data_loader, device):
    """Collect features and redundancy scores from the model."""
    model.eval()
    all_text_encoded = []
    all_visual_encoded = []
    all_text_refined = []
    all_visual_refined = []
    all_redundancy_scores = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Collecting features"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None
            
            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features
            
            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image
            
            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            
            # Collect features and redundancy scores
            all_text_encoded.append(outputs['text_encoded'].cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].cpu().numpy())
            all_text_refined.append(outputs['text_refined'].cpu().numpy())
            all_visual_refined.append(outputs['visual_refined'].cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].cpu().numpy())
            all_labels.append(labels.cpu().numpy())
    
    # Concatenate all features and redundancy scores
    return {
        'text_encoded': np.vstack(all_text_encoded),
        'visual_encoded': np.vstack(all_visual_encoded),
        'text_refined': np.vstack(all_text_refined),
        'visual_refined': np.vstack(all_visual_refined),
        'redundancy_scores': np.vstack(all_redundancy_scores),
        'labels': np.vstack(all_labels)
    }

def visualize_redundancy_distribution(features, output_dir):
    """Visualize the distribution of redundancy scores."""
    redundancy_scores = features['redundancy_scores'].flatten()
    
    plt.figure(figsize=(12, 8))
    
    # Create a beautiful histogram with KDE
    sns.histplot(redundancy_scores, kde=True, bins=50, color=main_colors[0], 
                 alpha=0.7, edgecolor='none', line_kws={'linewidth': 2})
    
    plt.title('Distribution of Cross-Modal Redundancy Scores', fontsize=18, pad=20)
    plt.xlabel('Redundancy Score', fontsize=14, labelpad=10)
    plt.ylabel('Frequency', fontsize=14, labelpad=10)
    plt.grid(True, alpha=0.3)
    
    # Add statistical information
    mean_redundancy = np.mean(redundancy_scores)
    median_redundancy = np.median(redundancy_scores)
    std_redundancy = np.std(redundancy_scores)
    
    stats_text = f"Mean: {mean_redundancy:.4f}\nMedian: {median_redundancy:.4f}\nStd Dev: {std_redundancy:.4f}"
    plt.annotate(stats_text, xy=(0.75, 0.75), xycoords='axes fraction', 
                 bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.8),
                 fontsize=12)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'redundancy_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    return mean_redundancy, median_redundancy, std_redundancy

def visualize_feature_space_with_redundancy(features, output_dir, method='tsne'):
    """Visualize the feature space with redundancy highlighted."""
    # Combine text and visual features
    text_encoded = features['text_encoded']
    visual_encoded = features['visual_encoded']
    redundancy_scores = features['redundancy_scores'].flatten()
    labels = features['labels']
    
    # Reduce dimensionality for visualization
    if method == 'tsne':
        reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        text_2d = reducer.fit_transform(text_encoded)
        reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        visual_2d = reducer.fit_transform(visual_encoded)
    elif method == 'umap':
        reducer = umap.UMAP(n_components=2, random_state=42)
        text_2d = reducer.fit_transform(text_encoded)
        reducer = umap.UMAP(n_components=2, random_state=42)
        visual_2d = reducer.fit_transform(visual_encoded)
    else:  # PCA
        reducer = PCA(n_components=2, random_state=42)
        text_2d = reducer.fit_transform(text_encoded)
        reducer = PCA(n_components=2, random_state=42)
        visual_2d = reducer.fit_transform(visual_encoded)
    
    # Create a beautiful scatter plot
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Text features colored by redundancy
    scatter1 = ax1.scatter(text_2d[:, 0], text_2d[:, 1], c=redundancy_scores, 
                          cmap=redundancy_cmap, alpha=0.8, s=50, edgecolor='none')
    ax1.set_title('Text Feature Space Colored by Redundancy', fontsize=18, pad=20)
    ax1.set_xlabel(f'{method.upper()} Component 1', fontsize=14, labelpad=10)
    ax1.set_ylabel(f'{method.upper()} Component 2', fontsize=14, labelpad=10)
    ax1.grid(True, alpha=0.3)
    
    # Visual features colored by redundancy
    scatter2 = ax2.scatter(visual_2d[:, 0], visual_2d[:, 1], c=redundancy_scores, 
                          cmap=redundancy_cmap, alpha=0.8, s=50, edgecolor='none')
    ax2.set_title('Visual Feature Space Colored by Redundancy', fontsize=18, pad=20)
    ax2.set_xlabel(f'{method.upper()} Component 1', fontsize=14, labelpad=10)
    ax2.set_ylabel(f'{method.upper()} Component 2', fontsize=14, labelpad=10)
    ax2.grid(True, alpha=0.3)
    
    # Add colorbar
    cbar = fig.colorbar(scatter1, ax=[ax1, ax2], orientation='horizontal', pad=0.05)
    cbar.set_label('Redundancy Score', fontsize=14, labelpad=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'feature_space_redundancy_{method}.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create 3D visualization
    fig = plt.figure(figsize=(15, 12))
    
    # Create 3D subplots
    ax1 = fig.add_subplot(121, projection='3d')
    ax2 = fig.add_subplot(122, projection='3d')
    
    # Reduce to 3D
    if method == 'tsne':
        reducer = TSNE(n_components=3, random_state=42, perplexity=30)
        text_3d = reducer.fit_transform(text_encoded)
        reducer = TSNE(n_components=3, random_state=42, perplexity=30)
        visual_3d = reducer.fit_transform(visual_encoded)
    elif method == 'umap':
        reducer = umap.UMAP(n_components=3, random_state=42)
        text_3d = reducer.fit_transform(text_encoded)
        reducer = umap.UMAP(n_components=3, random_state=42)
        visual_3d = reducer.fit_transform(visual_encoded)
    else:  # PCA
        reducer = PCA(n_components=3, random_state=42)
        text_3d = reducer.fit_transform(text_encoded)
        reducer = PCA(n_components=3, random_state=42)
        visual_3d = reducer.fit_transform(visual_encoded)
    
    # Text features in 3D
    scatter1 = ax1.scatter(text_3d[:, 0], text_3d[:, 1], text_3d[:, 2], 
                          c=redundancy_scores, cmap=redundancy_cmap, alpha=0.8, s=50)
    ax1.set_title('3D Text Feature Space', fontsize=18, pad=20)
    ax1.set_xlabel(f'Component 1', fontsize=12)
    ax1.set_ylabel(f'Component 2', fontsize=12)
    ax1.set_zlabel(f'Component 3', fontsize=12)
    
    # Visual features in 3D
    scatter2 = ax2.scatter(visual_3d[:, 0], visual_3d[:, 1], visual_3d[:, 2], 
                          c=redundancy_scores, cmap=redundancy_cmap, alpha=0.8, s=50)
    ax2.set_title('3D Visual Feature Space', fontsize=18, pad=20)
    ax2.set_xlabel(f'Component 1', fontsize=12)
    ax2.set_ylabel(f'Component 2', fontsize=12)
    ax2.set_zlabel(f'Component 3', fontsize=12)
    
    # Add colorbar
    cbar = fig.colorbar(scatter1, ax=[ax1, ax2], orientation='horizontal', pad=0.05, shrink=0.8)
    cbar.set_label('Redundancy Score', fontsize=14, labelpad=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'feature_space_redundancy_3d_{method}.png'), dpi=300, bbox_inches='tight')
    plt.close()

def visualize_redundancy_by_class(features, output_dir):
    """Visualize redundancy scores by class."""
    redundancy_scores = features['redundancy_scores'].flatten()
    labels = features['labels']
    
    # Calculate average redundancy score for each class
    num_classes = labels.shape[1]
    class_redundancy = []
    
    for i in range(num_classes):
        # Get samples that belong to this class
        class_samples = labels[:, i] == 1
        if np.sum(class_samples) > 0:
            # Calculate average redundancy for this class
            avg_redundancy = np.mean(redundancy_scores[class_samples])
            class_redundancy.append((i, avg_redundancy))
    
    # Sort by redundancy score
    class_redundancy.sort(key=lambda x: x[1], reverse=True)
    
    # Create a beautiful bar chart
    plt.figure(figsize=(14, 10))
    
    class_indices = [x[0] for x in class_redundancy]
    redundancy_values = [x[1] for x in class_redundancy]
    
    # Get class names (replace with actual class names if available)
    class_names = [f"Class {i}" for i in class_indices]
    
    # Create horizontal bar chart
    bars = plt.barh(class_names, redundancy_values, color=main_colors[0], alpha=0.8, 
                   edgecolor='none', height=0.6)
    
    # Add value labels
    for i, bar in enumerate(bars):
        plt.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                f'{redundancy_values[i]:.4f}', va='center', fontsize=10)
    
    plt.title('Average Cross-Modal Redundancy by Class', fontsize=18, pad=20)
    plt.xlabel('Average Redundancy Score', fontsize=14, labelpad=10)
    plt.ylabel('Class', fontsize=14, labelpad=10)
    plt.grid(True, alpha=0.3)
    plt.xlim(0, max(redundancy_values) * 1.1)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'redundancy_by_class.png'), dpi=300, bbox_inches='tight')
    plt.close()

def visualize_feature_correlation(features, output_dir):
    """Visualize correlation between text and visual features."""
    text_encoded = features['text_encoded']
    visual_encoded = features['visual_encoded']
    redundancy_scores = features['redundancy_scores'].flatten()
    
    # Calculate correlation between text and visual features
    # Use a subset of features if dimensionality is too high
    max_dim = 100
    text_subset = text_encoded[:, :min(text_encoded.shape[1], max_dim)]
    visual_subset = visual_encoded[:, :min(visual_encoded.shape[1], max_dim)]
    
    # Calculate correlation matrix
    correlation_matrix = np.corrcoef(text_subset.T, visual_subset.T)
    
    # Extract the cross-correlation block
    text_dim = text_subset.shape[1]
    visual_dim = visual_subset.shape[1]
    cross_corr = correlation_matrix[:text_dim, text_dim:text_dim+visual_dim]
    
    # Create a beautiful heatmap
    plt.figure(figsize=(16, 12))
    
    sns.heatmap(cross_corr, cmap='coolwarm', center=0, 
               xticklabels=10, yticklabels=10, 
               cbar_kws={'label': 'Correlation Coefficient'})
    
    plt.title('Cross-Modal Feature Correlation Matrix', fontsize=18, pad=20)
    plt.xlabel('Visual Features', fontsize=14, labelpad=10)
    plt.ylabel('Text Features', fontsize=14, labelpad=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'feature_correlation.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Calculate average absolute correlation
    avg_abs_corr = np.mean(np.abs(cross_corr))
    
    # Create a histogram of correlation values
    plt.figure(figsize=(12, 8))
    
    sns.histplot(cross_corr.flatten(), kde=True, bins=50, color=main_colors[1], 
                alpha=0.7, edgecolor='none', line_kws={'linewidth': 2})
    
    plt.title('Distribution of Cross-Modal Feature Correlations', fontsize=18, pad=20)
    plt.xlabel('Correlation Coefficient', fontsize=14, labelpad=10)
    plt.ylabel('Frequency', fontsize=14, labelpad=10)
    plt.grid(True, alpha=0.3)
    
    # Add statistical information
    stats_text = f"Mean Absolute Correlation: {avg_abs_corr:.4f}"
    plt.annotate(stats_text, xy=(0.05, 0.95), xycoords='axes fraction', 
                 bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.8),
                 fontsize=12)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'correlation_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    return avg_abs_corr

def visualize_redundancy_effect(features, output_dir):
    """Visualize the effect of redundancy on feature refinement."""
    text_encoded = features['text_encoded']
    visual_encoded = features['visual_encoded']
    text_refined = features['text_refined']
    visual_refined = features['visual_refined']
    redundancy_scores = features['redundancy_scores'].flatten()
    
    # Calculate feature changes
    text_change = np.linalg.norm(text_refined - text_encoded, axis=1)
    visual_change = np.linalg.norm(visual_refined - visual_encoded, axis=1)
    
    # Create a scatter plot of redundancy vs. feature change
    plt.figure(figsize=(14, 10))
    
    plt.scatter(redundancy_scores, text_change, alpha=0.6, label='Text Features', 
               color=main_colors[0], s=50, edgecolor='none')
    plt.scatter(redundancy_scores, visual_change, alpha=0.6, label='Visual Features', 
               color=main_colors[1], s=50, edgecolor='none')
    
    # Add trend lines
    z1 = np.polyfit(redundancy_scores, text_change, 1)
    p1 = np.poly1d(z1)
    plt.plot(np.sort(redundancy_scores), p1(np.sort(redundancy_scores)), 
            color=main_colors[0], linestyle='--', linewidth=2)
    
    z2 = np.polyfit(redundancy_scores, visual_change, 1)
    p2 = np.poly1d(z2)
    plt.plot(np.sort(redundancy_scores), p2(np.sort(redundancy_scores)), 
            color=main_colors[1], linestyle='--', linewidth=2)
    
    plt.title('Effect of Redundancy on Feature Refinement', fontsize=18, pad=20)
    plt.xlabel('Redundancy Score', fontsize=14, labelpad=10)
    plt.ylabel('Feature Change Magnitude', fontsize=14, labelpad=10)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    
    # Add correlation information
    text_corr = np.corrcoef(redundancy_scores, text_change)[0, 1]
    visual_corr = np.corrcoef(redundancy_scores, visual_change)[0, 1]
    
    stats_text = f"Text Correlation: {text_corr:.4f}\nVisual Correlation: {visual_corr:.4f}"
    plt.annotate(stats_text, xy=(0.05, 0.95), xycoords='axes fraction', 
                 bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.8),
                 fontsize=12)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'redundancy_effect.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    return text_corr, visual_corr

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create datasets
    print("Creating datasets...")
    train_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='train'
    )
    
    val_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='val'
    )
    
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    print("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)
    
    # Load model weights
    print(f"Loading model from {args.model_path}...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    
    # Collect features and redundancy scores
    print("Collecting features and redundancy scores...")
    train_features = collect_features_and_redundancy(model, train_loader, device)
    val_features = collect_features_and_redundancy(model, val_loader, device)
    test_features = collect_features_and_redundancy(model, test_loader, device)
    
    # Create visualization directories
    train_dir = os.path.join(args.output_dir, 'train')
    val_dir = os.path.join(args.output_dir, 'val')
    test_dir = os.path.join(args.output_dir, 'test')
    
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    os.makedirs(test_dir, exist_ok=True)
    
    # Generate visualizations
    print("Generating visualizations...")
    
    # Redundancy distribution
    print("Visualizing redundancy distribution...")
    train_mean, train_median, train_std = visualize_redundancy_distribution(train_features, train_dir)
    val_mean, val_median, val_std = visualize_redundancy_distribution(val_features, val_dir)
    test_mean, test_median, test_std = visualize_redundancy_distribution(test_features, test_dir)
    
    # Feature space with redundancy
    print("Visualizing feature space with redundancy...")
    visualize_feature_space_with_redundancy(train_features, train_dir, method='tsne')
    visualize_feature_space_with_redundancy(val_features, val_dir, method='tsne')
    visualize_feature_space_with_redundancy(test_features, test_dir, method='tsne')
    
    visualize_feature_space_with_redundancy(train_features, train_dir, method='umap')
    visualize_feature_space_with_redundancy(val_features, val_dir, method='umap')
    visualize_feature_space_with_redundancy(test_features, test_dir, method='umap')
    
    # Redundancy by class
    print("Visualizing redundancy by class...")
    visualize_redundancy_by_class(train_features, train_dir)
    visualize_redundancy_by_class(val_features, val_dir)
    visualize_redundancy_by_class(test_features, test_dir)
    
    # Feature correlation
    print("Visualizing feature correlation...")
    train_corr = visualize_feature_correlation(train_features, train_dir)
    val_corr = visualize_feature_correlation(val_features, val_dir)
    test_corr = visualize_feature_correlation(test_features, test_dir)
    
    # Redundancy effect
    print("Visualizing redundancy effect...")
    train_text_corr, train_visual_corr = visualize_redundancy_effect(train_features, train_dir)
    val_text_corr, val_visual_corr = visualize_redundancy_effect(val_features, val_dir)
    test_text_corr, test_visual_corr = visualize_redundancy_effect(test_features, test_dir)
    
    # Create summary report
    summary = {
        'redundancy_statistics': {
            'train': {'mean': train_mean, 'median': train_median, 'std': train_std},
            'val': {'mean': val_mean, 'median': val_median, 'std': val_std},
            'test': {'mean': test_mean, 'median': test_median, 'std': test_std}
        },
        'feature_correlation': {
            'train': train_corr,
            'val': val_corr,
            'test': test_corr
        },
        'redundancy_effect': {
            'train': {'text_corr': train_text_corr, 'visual_corr': train_visual_corr},
            'val': {'text_corr': val_text_corr, 'visual_corr': val_visual_corr},
            'test': {'text_corr': test_text_corr, 'visual_corr': test_visual_corr}
        }
    }
    
    # Save summary report
    with open(os.path.join(args.output_dir, 'redundancy_analysis_summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Visualizations saved to {args.output_dir}")
    print(f"Summary report saved to {os.path.join(args.output_dir, 'redundancy_analysis_summary.json')}")

if __name__ == '__main__':
    main()
