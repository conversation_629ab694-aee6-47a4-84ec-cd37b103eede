#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MM-IMDB数据集加载器
"""

import os
import json
import numpy as np
import torch
from torch.utils.data import Dataset
import h5py

class MMIMDBDataset(Dataset):
    """
    MM-IMDB数据集
    """
    def __init__(self, data_dir, split='train'):
        """
        初始化MM-IMDB数据集
        
        参数:
            data_dir (str): 数据集目录
            split (str): 数据集划分，可选 'train', 'val', 'test'
        """
        self.data_dir = data_dir
        self.split = split
        
        # 加载数据集划分
        split_file = os.path.join(data_dir, f'{split}_split.json')
        if os.path.exists(split_file):
            with open(split_file, 'r') as f:
                self.split_ids = json.load(f)
        else:
            # 如果没有划分文件，使用模拟数据
            print(f"警告: 找不到划分文件 {split_file}，使用模拟数据")
            self.split_ids = [f'sample_{i}' for i in range(100)]
        
        # 加载特征文件
        text_feature_file = os.path.join(data_dir, 'text_features.h5')
        visual_feature_file = os.path.join(data_dir, 'visual_features.h5')
        label_file = os.path.join(data_dir, 'labels.json')
        
        # 检查特征文件是否存在
        if os.path.exists(text_feature_file) and os.path.exists(visual_feature_file) and os.path.exists(label_file):
            # 加载实际特征
            self.text_features = h5py.File(text_feature_file, 'r')
            self.visual_features = h5py.File(visual_feature_file, 'r')
            with open(label_file, 'r') as f:
                self.labels = json.load(f)
            
            # 获取类别列表
            self.classes = sorted(list(set([c for sample in self.labels.values() for c in sample])))
            self.num_classes = len(self.classes)
            self.class_to_idx = {c: i for i, c in enumerate(self.classes)}
            
            # 过滤划分中不存在的样本
            self.split_ids = [id for id in self.split_ids if id in self.labels]
        else:
            # 如果特征文件不存在，使用模拟数据
            print(f"警告: 找不到特征文件，使用模拟数据")
            self.text_features = None
            self.visual_features = None
            self.labels = None
            self.classes = [f'class_{i}' for i in range(23)]  # MM-IMDB有23个类别
            self.num_classes = len(self.classes)
            self.class_to_idx = {c: i for i, c in enumerate(self.classes)}
    
    def __len__(self):
        """
        返回数据集大小
        """
        return len(self.split_ids)
    
    def __getitem__(self, idx):
        """
        获取数据集中的一个样本
        
        参数:
            idx (int): 样本索引
            
        返回:
            dict: 包含特征和标签的字典
        """
        sample_id = self.split_ids[idx]
        
        if self.text_features is not None and self.visual_features is not None and self.labels is not None:
            # 加载实际特征和标签
            text_feature = self.text_features[sample_id][:]
            visual_feature = self.visual_features[sample_id][:]
            label = self.labels[sample_id]
            
            # 转换标签为one-hot向量
            label_vec = np.zeros(self.num_classes)
            for c in label:
                if c in self.class_to_idx:
                    label_vec[self.class_to_idx[c]] = 1
        else:
            # 生成模拟特征和标签
            text_feature = np.random.randn(300)  # 假设文本特征维度为300
            visual_feature = np.random.randn(4096)  # 假设视觉特征维度为4096
            label_vec = np.random.randint(0, 2, size=self.num_classes)  # 随机生成多标签
        
        return {
            'id': sample_id,
            'text_features': torch.FloatTensor(text_feature),
            'visual_features': torch.FloatTensor(visual_feature),
            'labels': torch.FloatTensor(label_vec)
        }
