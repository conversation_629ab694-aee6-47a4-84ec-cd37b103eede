# 消融实验（Ablation Study）

本文档介绍如何运行消融实验，以评估模型中各个组件的贡献。

## 概述

消融实验通过禁用模型的特定组件，然后评估模型性能的变化，来确定每个组件的重要性。我们的消融实验测试以下组件的影响：

1. **知识图谱（KG）组件**：评估知识图谱信息对模型性能的贡献
2. **冗余检测模块**：评估冗余检测对模型性能的贡献
3. **图推理模块**：评估基于图的推理对模型性能的贡献
4. **自适应融合模块**：评估自适应融合对模型性能的贡献

## 运行消融实验

### 使用脚本运行

最简单的方法是使用提供的脚本：

```bash
./run_ablation.sh --exp_name <experiment_name> [--output_dir <output_dir>] [--device <cuda|cpu>]
```

例如：

```bash
./run_ablation.sh --exp_name kg_disentangle_v1 --output_dir ./output/ablation_results --device cuda
```

### 直接使用Python运行

您也可以直接使用Python运行消融实验：

```bash
python ablation_study.py --model_path <path_to_model> --output_dir <output_dir> --device <device>
```

例如：

```bash
python ablation_study.py --model_path ./output/kg_disentangle_v1/best_model.pth --output_dir ./output/ablation_results --device cuda
```

## 输出

消融实验将为每个配置创建一个目录，并在其中保存指标。此外，它还会创建一个汇总文件，其中包含所有实验的关键指标。

输出目录结构：

```
output_dir/
├── full_model/
│   └── metrics.json
├── no_kg/
│   └── metrics.json
├── no_redundancy/
│   └── metrics.json
├── no_graph_reasoning/
│   └── metrics.json
├── no_adaptive_fusion/
│   └── metrics.json
└── summary.json
```

## 指标

我们记录以下指标：

### 分类指标

- **F1 (Samples)**：样本级F1分数
- **F1-Micro**：微平均F1分数
- **F1-Macro**：宏平均F1分数
- **Hamming Accuracy**：海明准确率
- **Precision**：精确率
- **Recall**：召回率
- **mAP**：平均精度均值

### 解缠指标

- **Modality Disentanglement Score**：模态解缠分数（越高越好）
- **Cross-Modal Redundancy**：跨模态冗余（越低越好）
- **Shared Information Preservation**：共享信息保留（越高越好）

## 解释结果

通过比较不同配置的性能，您可以确定每个组件对模型的贡献：

1. **full_model vs. no_kg**：知识图谱的贡献
2. **full_model vs. no_redundancy**：冗余检测的贡献
3. **full_model vs. no_graph_reasoning**：图推理的贡献
4. **full_model vs. no_adaptive_fusion**：自适应融合的贡献

性能下降越大，说明该组件对模型越重要。
