#!/bin/bash

# 运行特征提取和解缠指标计算脚本

# 设置环境变量
export PYTHONPATH=/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle:$PYTHONPATH

# 设置参数
MODEL_PATH="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/kg_disentangle_v1/best_model.pth"
DATA_DIR="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mmimdb"
OUTPUT_DIR="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/disentanglement_analysis"
BATCH_SIZE=32
GPU_ID=0
SAMPLE_SIZE=100

# 创建输出目录
mkdir -p $OUTPUT_DIR

# 运行特征提取脚本
echo "开始提取特征并计算解缠指标..."
python /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/extract_features.py \
    --model_path $MODEL_PATH \
    --data_dir $DATA_DIR \
    --output_dir $OUTPUT_DIR \
    --batch_size $BATCH_SIZE \
    --gpu $GPU_ID \
    --sample_size $SAMPLE_SIZE

# 检查运行结果
if [ $? -eq 0 ]; then
    echo "分析完成，结果保存在: $OUTPUT_DIR"
    echo "生成的报告文件:"
    ls -l $OUTPUT_DIR/disentanglement_analysis_report.md
    echo "生成的CSV文件:"
    ls -l $OUTPUT_DIR/*.csv
    echo "生成的可视化图像:"
    ls -l $OUTPUT_DIR/*.png
else
    echo "分析失败，请检查错误信息"
fi
