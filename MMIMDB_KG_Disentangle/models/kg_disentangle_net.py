"""
Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.

This model addresses cross-modal semantic entanglement by:
1. Detecting redundancy between modality-invariant and modality-specific representations
2. Using knowledge graph to guide the disentanglement process
3. Implementing adaptive fusion with redundancy suppression
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from models.base_model import BaseModel

class MultiHeadAttention(nn.Module):
    """Multi-head attention module for cross-modal interaction."""

    def __init__(self, dim, num_heads=8):
        super(MultiHeadAttention, self).__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.head_dim = dim // num_heads
        assert self.head_dim * num_heads == dim, "dim must be divisible by num_heads"

        self.q_proj = nn.Linear(dim, dim)
        self.k_proj = nn.Linear(dim, dim)
        self.v_proj = nn.Linear(dim, dim)
        self.out_proj = nn.Linear(dim, dim)

    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)

        # Project and reshape
        q = self.q_proj(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # Compute attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.head_dim)

        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        # Apply softmax and compute weighted sum
        attn_weights = F.softmax(scores, dim=-1)
        attn_output = torch.matmul(attn_weights, v)

        # Reshape and project back
        attn_output = attn_output.transpose(1, 2).contiguous().view(batch_size, -1, self.dim)
        output = self.out_proj(attn_output)

        return output, attn_weights

class RedundancyDetectionModule(nn.Module):
    """Module for detecting semantic redundancy between modalities."""

    def __init__(self, dim):
        super(RedundancyDetectionModule, self).__init__()
        self.attention = MultiHeadAttention(dim)
        self.redundancy_estimator = nn.Sequential(
            nn.Linear(dim * 2, dim),
            nn.ReLU(),
            nn.Linear(dim, 1),
            nn.Sigmoid()
        )

    def forward(self, x, y):
        # Cross-attention between modalities
        attended_x, attn_weights = self.attention(x, y, y)

        # Compute redundancy score
        concat_features = torch.cat([x, attended_x], dim=-1)
        redundancy_score = self.redundancy_estimator(concat_features)

        return redundancy_score, attn_weights

class GraphReasoningModule(nn.Module):
    """Module for graph-based reasoning using knowledge graph embeddings."""

    def __init__(self, dim, kg_dim):
        super(GraphReasoningModule, self).__init__()
        self.kg_projection = nn.Linear(kg_dim, dim)
        self.graph_attention = MultiHeadAttention(dim)
        self.update_gate = nn.Sequential(
            nn.Linear(dim * 2, dim),
            nn.Sigmoid()
        )

    def forward(self, features, kg_features):
        # Project KG features to the same dimension
        kg_projected = self.kg_projection(kg_features)

        # Apply graph attention
        attended_features, _ = self.graph_attention(features, kg_projected, kg_projected)

        # Gated update - avoid in-place operations
        update_gate = self.update_gate(torch.cat([features, attended_features], dim=-1))
        # Create a new tensor instead of modifying in-place
        updated_features = update_gate * attended_features + (1 - update_gate) * features

        return updated_features

class AdaptiveFusionModule(nn.Module):
    """Module for adaptive fusion with redundancy suppression."""

    def __init__(self, dim):
        super(AdaptiveFusionModule, self).__init__()
        self.fusion_gate = nn.Sequential(
            nn.Linear(dim * 3, dim * 3),
            nn.Sigmoid()
        )
        self.fusion_projection = nn.Linear(dim * 3, dim)

    def forward(self, text_features, visual_features, kg_features, redundancy_scores):
        # Concatenate all features
        concat_features = torch.cat([text_features, visual_features, kg_features], dim=-1)

        # Compute fusion gates (influenced by redundancy scores)
        gates = self.fusion_gate(concat_features)

        # Apply redundancy suppression
        batch_size = text_features.size(0)
        dim = text_features.size(1)

        # Reshape gates and apply redundancy suppression
        gates = gates.view(batch_size, 3, dim)

        # Make sure redundancy_scores has shape [batch_size, 1]
        if len(redundancy_scores.shape) == 1:
            redundancy_scores = redundancy_scores.unsqueeze(-1)
        elif len(redundancy_scores.shape) == 3:
            # If it's [batch_size, 1, 1], reshape to [batch_size, 1]
            redundancy_scores = redundancy_scores.squeeze(1)

        # Suppress redundant information - avoid in-place operations
        # Create a new tensor for the gates
        new_gates = gates.clone()
        new_gates[:, 0] = gates[:, 0] * (1 - redundancy_scores)  # Text
        new_gates[:, 1] = gates[:, 1] * (1 - redundancy_scores)  # Visual
        gates = new_gates

        # Reshape back
        gates = gates.view(batch_size, 3 * dim)

        # Apply gates and project
        gated_features = gates * concat_features
        fused = self.fusion_projection(gated_features)

        return fused

class KGDisentangleNet(BaseModel):
    """
    Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.
    """

    def __init__(self, text_dim=300, visual_dim=4096, kg_dim=200, hidden_dim=512, num_classes=23):
        super(KGDisentangleNet, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=kg_dim,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )

        # Redundancy detection module
        self.redundancy_detector = RedundancyDetectionModule(hidden_dim)

        # Graph reasoning module
        self.graph_reasoner = GraphReasoningModule(hidden_dim, kg_dim)

        # Adaptive fusion module
        self.adaptive_fusion = AdaptiveFusionModule(hidden_dim)

        # Enhanced classifier with label-aware attention
        self.label_attention = nn.Linear(num_classes, hidden_dim)
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

        # Contrastive learning temperature
        self.temperature = 0.07

    def forward(self, images, texts, kg_features, label_embeddings=None, kg_weights=None, return_features=False):
        """
        Forward pass of the model.

        Args:
            images: Image features
            texts: Text features
            kg_features: Knowledge graph features
            label_embeddings: Label embeddings for label-aware attention
            kg_weights: Knowledge graph weights
            return_features: Whether to return features for disentanglement metrics

        Returns:
            tuple: (logits, disentanglement_loss) or (logits, disentanglement_loss, features)
        """
        # Encode features
        text_encoded = self.text_encoder(texts)
        visual_encoded = self.visual_encoder(images)
        kg_encoded = self.kg_encoder(kg_features)

        # Step 1: Detect redundancy between modalities
        # Add sequence dimension (batch_size, hidden_dim) -> (batch_size, 1, hidden_dim)
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        redundancy_score, attn_weights = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
        # Remove the extra dimension from redundancy_score
        redundancy_score = redundancy_score.squeeze(1)

        # Step 2: Apply graph reasoning to guide disentanglement
        # Add sequence dimension for graph reasoning
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        kg_features_3d = kg_features.unsqueeze(1)

        text_refined_3d = self.graph_reasoner(text_encoded_3d, kg_features_3d)
        visual_refined_3d = self.graph_reasoner(visual_encoded_3d, kg_features_3d)

        # Remove sequence dimension
        text_refined = text_refined_3d.squeeze(1)
        visual_refined = visual_refined_3d.squeeze(1)

        # Compute modality-invariant and modality-specific features
        # Modality-invariant features are the common parts
        text_invariant = text_refined * redundancy_score
        visual_invariant = visual_refined * redundancy_score

        # Modality-specific features are the unique parts
        text_specific = text_refined * (1 - redundancy_score)
        visual_specific = visual_refined * (1 - redundancy_score)

        # Step 3: Adaptive fusion with redundancy suppression
        fused = self.adaptive_fusion(
            text_refined,
            visual_refined,
            kg_encoded,
            redundancy_score
        )

        # Classification with label attention if available
        if label_embeddings is not None:
            # Skip label attention for now due to shape mismatch
            # We'll implement this properly in a future update
            logits = self.enhanced_classifier(fused)
        else:
            logits = self.enhanced_classifier(fused)

        # Calculate disentanglement loss
        # Contrastive loss to maximize similarity between modality-invariant features
        invariant_sim = F.cosine_similarity(text_invariant, visual_invariant, dim=1).mean()

        # Orthogonality loss to minimize similarity between modality-specific features
        specific_sim = F.cosine_similarity(text_specific, visual_specific, dim=1).mean()

        # Cross-modal orthogonality loss
        text_specific_visual_invariant_sim = F.cosine_similarity(text_specific, visual_invariant, dim=1).mean()
        visual_specific_text_invariant_sim = F.cosine_similarity(visual_specific, text_invariant, dim=1).mean()

        # Redundancy penalty
        redundancy_penalty = redundancy_score.mean()

        # Total disentanglement loss
        disentanglement_loss = (
            - 0.5 * invariant_sim  # Maximize similarity between invariant features
            + 0.3 * specific_sim   # Minimize similarity between specific features
            + 0.1 * text_specific_visual_invariant_sim  # Minimize cross-modal similarity
            + 0.1 * visual_specific_text_invariant_sim  # Minimize cross-modal similarity
            + 0.1 * redundancy_penalty  # Control redundancy level
        )

        if return_features:
            features = {
                'text_invariant': text_invariant,
                'visual_invariant': visual_invariant,
                'text_specific': text_specific,
                'visual_specific': visual_specific
            }
            return logits, disentanglement_loss, features

        return logits, disentanglement_loss
