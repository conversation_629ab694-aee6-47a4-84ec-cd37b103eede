#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
知识图谱增强的层级跨模态语义解缠网络模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class KGHierDisNet(nn.Module):
    """
    知识图谱增强的层级跨模态语义解缠网络
    """
    def __init__(self, text_dim=300, visual_dim=4096, hidden_dim=512, kg_dim=200, num_classes=23, dropout=0.5):
        super(KGHierDisNet, self).__init__()
        self.text_dim = text_dim
        self.visual_dim = visual_dim
        self.hidden_dim = hidden_dim
        self.kg_dim = kg_dim
        self.num_classes = num_classes
        
        # 文本特征提取器
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.<PERSON>LU(),
            nn.Dropout(dropout)
        )
        
        # 视觉特征提取器
        self.visual_encoder = nn.Sequential(
            nn.Linear(visual_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 知识图谱特征提取器
        self.kg_encoder = nn.Sequential(
            nn.Linear(kg_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 语义相关性分析模块
        self.semantic_relevance = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
        
        # 模态不变表示提取器
        self.mir_extractor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 有效模态特定表示提取器
        self.emsr_extractor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 无效模态特定表示提取器
        self.imsr_extractor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 融合模块
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 4, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, num_classes),
            nn.Sigmoid()
        )
        
    def forward(self, text_features, visual_features, return_features=False):
        """
        前向传播
        
        参数:
            text_features (torch.Tensor): 文本特征，形状为 (batch_size, text_dim)
            visual_features (torch.Tensor): 视觉特征，形状为 (batch_size, visual_dim)
            return_features (bool): 是否返回中间特征表示
            
        返回:
            torch.Tensor: 预测结果，形状为 (batch_size, num_classes)
            dict (可选): 包含中间特征表示的字典
        """
        batch_size = text_features.size(0)
        
        # 特征编码
        text_encoded = self.text_encoder(text_features)
        visual_encoded = self.visual_encoder(visual_features)
        
        # 模拟知识图谱特征（在实际模型中，这应该是从知识图谱中提取的）
        # 这里我们简单地使用文本和视觉特征的组合来模拟
        kg_features = torch.cat([text_encoded[:, :self.kg_dim//2], visual_encoded[:, :self.kg_dim//2]], dim=1)
        kg_encoded = self.kg_encoder(kg_features)
        
        # 计算语义相关性
        text_visual_concat = torch.cat([text_encoded, visual_encoded], dim=1)
        semantic_relevance = self.semantic_relevance(text_visual_concat)
        
        # 提取模态不变表示 (MIR)
        mir = self.mir_extractor(text_visual_concat) * semantic_relevance
        
        # 提取有效模态特定表示 (EMSR)
        text_emsr = self.emsr_extractor(torch.cat([text_encoded, mir], dim=1)) * (1 - semantic_relevance)
        visual_emsr = self.emsr_extractor(torch.cat([visual_encoded, mir], dim=1)) * (1 - semantic_relevance)
        
        # 提取无效模态特定表示 (IMSR)
        text_imsr = self.imsr_extractor(text_encoded) * semantic_relevance * 0.1  # 无效表示应该很小
        visual_imsr = self.imsr_extractor(visual_encoded) * semantic_relevance * 0.1
        
        # 融合表示
        fused = self.fusion(torch.cat([mir, text_emsr, visual_emsr, kg_encoded], dim=1))
        
        # 分类
        logits = self.classifier(fused)
        
        if return_features:
            features = {
                'mir': mir.cpu().numpy(),
                'text_emsr': text_emsr.cpu().numpy(),
                'visual_emsr': visual_emsr.cpu().numpy(),
                'text_imsr': text_imsr.cpu().numpy(),
                'visual_imsr': visual_imsr.cpu().numpy(),
                'kg': kg_encoded.cpu().numpy(),
                'fused': fused.cpu().numpy()
            }
            return features
        
        return logits
