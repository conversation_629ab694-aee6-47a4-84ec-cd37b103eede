"""
Base model architecture for cross-modal semantic disentanglement with knowledge graph integration.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class BaseModel(nn.Module):
    """
    Base model class for multimodal multi-label classification with knowledge graph integration.
    """
    def __init__(self, text_dim=300, visual_dim=4096, kg_dim=200, hidden_dim=512, num_classes=23):
        """
        Initialize the base model.

        Args:
            text_dim (int): Dimension of text features
            visual_dim (int): Dimension of visual features
            kg_dim (int): Dimension of knowledge graph embeddings
            hidden_dim (int): Dimension of hidden layers
            num_classes (int): Number of output classes
        """
        super(BaseModel, self).__init__()

        self.text_dim = text_dim
        self.visual_dim = visual_dim
        self.kg_dim = kg_dim
        self.hidden_dim = hidden_dim
        self.num_classes = num_classes

        # Feature encoders
        self.text_encoder = nn.Sequential(
            nn.Linear(text_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),  # Use LayerNorm instead of BatchNorm
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # For image features from a 224x224 RGB image
        # Input shape: [batch_size, 3, 224, 224]
        # We'll flatten and then reduce dimension
        self.visual_encoder = nn.Sequential(
            nn.Flatten(),  # Flatten to [batch_size, 3*224*224]
            nn.Linear(3*224*224, 4096),  # First reduce the dimension
            nn.ReLU(),
            nn.LayerNorm(4096),  # Use LayerNorm instead of BatchNorm
            nn.Linear(4096, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        self.kg_encoder = nn.Sequential(
            nn.Linear(kg_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),  # Use LayerNorm instead of BatchNorm
            nn.ReLU(),
            nn.Dropout(0.3)
        )

        # Classifier
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, text_features, visual_features, kg_features):
        """
        Forward pass of the base model.

        Args:
            text_features (torch.Tensor): Text features
            visual_features (torch.Tensor): Visual features
            kg_features (torch.Tensor): Knowledge graph features

        Returns:
            dict: Dictionary containing model outputs
        """
        # Encode features
        text_encoded = self.text_encoder(text_features)
        visual_encoded = self.visual_encoder(visual_features)
        kg_encoded = self.kg_encoder(kg_features)

        # Simple fusion (to be enhanced in derived models)
        fused = text_encoded + visual_encoded + kg_encoded

        # Classification
        logits = self.classifier(fused)
        probs = torch.sigmoid(logits)

        return {
            'logits': logits,
            'probs': probs,
            'text_encoded': text_encoded,
            'visual_encoded': visual_encoded,
            'kg_encoded': kg_encoded,
            'fused': fused
        }
