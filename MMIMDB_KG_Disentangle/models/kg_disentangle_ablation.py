"""
Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network - Ablation Variants.

This file contains ablation variants of the KGDisentangleNet model for experimental evaluation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from models.base_model import BaseModel
from models.kg_disentangle_net import MultiHeadAttention, RedundancyDetectionModule, GraphReasoningModule, AdaptiveFusionModule

class CombinedRedundancyGraphModule(nn.Module):
    """
    Combined module that integrates redundancy detection and graph reasoning.
    This is used for ablation experiments to test the effectiveness of combining these components.
    """
    
    def __init__(self, dim, kg_dim):
        super(CombinedRedundancyGraphModule, self).__init__()
        # Attention mechanism for cross-modal interaction
        self.attention = MultiHeadAttention(dim)
        
        # KG projection
        self.kg_projection = nn.Linear(kg_dim, dim)
        
        # Combined reasoning network
        self.reasoning_network = nn.Sequential(
            nn.Linear(dim * 3, dim),
            nn.<PERSON><PERSON>(),
            nn.Linear(dim, dim)
        )
        
        # Redundancy estimator
        self.redundancy_estimator = nn.Sequential(
            nn.Linear(dim * 2, dim),
            nn.Re<PERSON>(),
            nn.Linear(dim, 1),
            nn.Sigmoid()
        )
        
        # Update gate
        self.update_gate = nn.Sequential(
            nn.Linear(dim * 2, dim),
            nn.Sigmoid()
        )
    
    def forward(self, features, kg_features):
        # Project KG features
        kg_projected = self.kg_projection(kg_features)
        
        # Cross-attention between features and KG
        attended_kg, _ = self.attention(features, kg_projected, kg_projected)
        
        # Cross-attention between modalities (if batch contains multiple modalities)
        # For simplicity, we'll use self-attention here
        self_attended, attn_weights = self.attention(features, features, features)
        
        # Combine all information
        combined = torch.cat([features, attended_kg, self_attended], dim=-1)
        reasoned_features = self.reasoning_network(combined)
        
        # Compute redundancy score
        concat_features = torch.cat([features, self_attended], dim=-1)
        redundancy_score = self.redundancy_estimator(concat_features)
        
        # Gated update
        update_gate = self.update_gate(torch.cat([features, reasoned_features], dim=-1))
        refined_features = update_gate * reasoned_features + (1 - update_gate) * features
        
        return refined_features, redundancy_score, attn_weights

class KGDisentangleNetAblation(BaseModel):
    """
    Ablation variant of KGDisentangleNet with combined redundancy detection and graph reasoning.
    """
    
    def __init__(self, text_dim=300, visual_dim=4096, kg_dim=200, hidden_dim=512, num_classes=23):
        super(KGDisentangleNetAblation, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=kg_dim,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )
        
        # Combined redundancy detection and graph reasoning module
        self.combined_module = CombinedRedundancyGraphModule(hidden_dim, kg_dim)
        
        # Adaptive fusion module
        self.adaptive_fusion = AdaptiveFusionModule(hidden_dim)
        
        # Enhanced classifier
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, text_features, visual_features, kg_features, label_embeddings=None):
        # Encode features
        text_encoded = self.text_encoder(text_features)
        visual_encoded = self.visual_encoder(visual_features)
        kg_encoded = self.kg_encoder(kg_features)
        
        # Add sequence dimension
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        kg_features_3d = kg_features.unsqueeze(1)
        
        # Apply combined module
        text_refined_3d, text_redundancy, text_attn = self.combined_module(text_encoded_3d, kg_features_3d)
        visual_refined_3d, visual_redundancy, visual_attn = self.combined_module(visual_encoded_3d, kg_features_3d)
        
        # Remove sequence dimension
        text_refined = text_refined_3d.squeeze(1)
        visual_refined = visual_refined_3d.squeeze(1)
        
        # Average redundancy scores from text and visual
        redundancy_score = (text_redundancy.squeeze(1) + visual_redundancy.squeeze(1)) / 2.0
        
        # Adaptive fusion
        fused = self.adaptive_fusion(
            text_refined,
            visual_refined,
            kg_encoded,
            redundancy_score
        )
        
        # Classification
        logits = self.enhanced_classifier(fused)
        probs = torch.sigmoid(logits)
        
        return {
            'logits': logits,
            'probs': probs,
            'redundancy_score': redundancy_score,
            'text_encoded': text_encoded,
            'visual_encoded': visual_encoded,
            'kg_encoded': kg_encoded,
            'text_refined': text_refined,
            'visual_refined': visual_refined,
            'fused': fused
        }

class KGDisentangleNetNoRedundancy(BaseModel):
    """
    Ablation variant of KGDisentangleNet without redundancy detection.
    """
    
    def __init__(self, text_dim=300, visual_dim=4096, kg_dim=200, hidden_dim=512, num_classes=23):
        super(KGDisentangleNetNoRedundancy, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=kg_dim,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )
        
        # Graph reasoning module
        self.graph_reasoner = GraphReasoningModule(hidden_dim, kg_dim)
        
        # Simple fusion module (without redundancy suppression)
        self.fusion = nn.Sequential(
            nn.Linear(hidden_dim * 3, hidden_dim),
            nn.ReLU()
        )
        
        # Enhanced classifier
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, text_features, visual_features, kg_features, label_embeddings=None):
        # Encode features
        text_encoded = self.text_encoder(text_features)
        visual_encoded = self.visual_encoder(visual_features)
        kg_encoded = self.kg_encoder(kg_features)
        
        # Add sequence dimension
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        kg_features_3d = kg_features.unsqueeze(1)
        
        # Apply graph reasoning
        text_refined_3d = self.graph_reasoner(text_encoded_3d, kg_features_3d)
        visual_refined_3d = self.graph_reasoner(visual_encoded_3d, kg_features_3d)
        
        # Remove sequence dimension
        text_refined = text_refined_3d.squeeze(1)
        visual_refined = visual_refined_3d.squeeze(1)
        
        # Simple fusion (without redundancy suppression)
        concat_features = torch.cat([text_refined, visual_refined, kg_encoded], dim=-1)
        fused = self.fusion(concat_features)
        
        # Classification
        logits = self.enhanced_classifier(fused)
        probs = torch.sigmoid(logits)
        
        # Create a dummy redundancy score of zeros for compatibility
        redundancy_score = torch.zeros(text_encoded.size(0), 1).to(text_encoded.device)
        
        return {
            'logits': logits,
            'probs': probs,
            'redundancy_score': redundancy_score,
            'text_encoded': text_encoded,
            'visual_encoded': visual_encoded,
            'kg_encoded': kg_encoded,
            'text_refined': text_refined,
            'visual_refined': visual_refined,
            'fused': fused
        }

class KGDisentangleNetNoGraphReasoning(BaseModel):
    """
    Ablation variant of KGDisentangleNet without graph reasoning.
    """
    
    def __init__(self, text_dim=300, visual_dim=4096, kg_dim=200, hidden_dim=512, num_classes=23):
        super(KGDisentangleNetNoGraphReasoning, self).__init__(
            text_dim=text_dim,
            visual_dim=visual_dim,
            kg_dim=kg_dim,
            hidden_dim=hidden_dim,
            num_classes=num_classes
        )
        
        # Redundancy detection module
        self.redundancy_detector = RedundancyDetectionModule(hidden_dim)
        
        # Adaptive fusion module
        self.adaptive_fusion = AdaptiveFusionModule(hidden_dim)
        
        # Enhanced classifier
        self.enhanced_classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, text_features, visual_features, kg_features, label_embeddings=None):
        # Encode features
        text_encoded = self.text_encoder(text_features)
        visual_encoded = self.visual_encoder(visual_features)
        kg_encoded = self.kg_encoder(kg_features)
        
        # Add sequence dimension for redundancy detection
        text_encoded_3d = text_encoded.unsqueeze(1)
        visual_encoded_3d = visual_encoded.unsqueeze(1)
        
        # Detect redundancy
        redundancy_score, attn_weights = self.redundancy_detector(text_encoded_3d, visual_encoded_3d)
        redundancy_score = redundancy_score.squeeze(1)
        
        # No graph reasoning, use encoded features directly
        text_refined = text_encoded
        visual_refined = visual_encoded
        
        # Adaptive fusion
        fused = self.adaptive_fusion(
            text_refined,
            visual_refined,
            kg_encoded,
            redundancy_score
        )
        
        # Classification
        logits = self.enhanced_classifier(fused)
        probs = torch.sigmoid(logits)
        
        return {
            'logits': logits,
            'probs': probs,
            'redundancy_score': redundancy_score,
            'attention_weights': attn_weights,
            'text_encoded': text_encoded,
            'visual_encoded': visual_encoded,
            'kg_encoded': kg_encoded,
            'text_refined': text_refined,
            'visual_refined': visual_refined,
            'fused': fused
        }
