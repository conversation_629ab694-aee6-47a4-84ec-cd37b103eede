"""
Enhanced evaluation script for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.
This script adds F1-Micro, F1-Macro, and feature disentanglement metrics to the evaluation.
"""

import os
import argparse
import torch
import numpy as np
from tqdm import tqdm
from sklearn.metrics import precision_recall_fscore_support, average_precision_score
import logging
import json
from torch.utils.data import DataLoader

from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
# Define metrics computation functions here to avoid importing from train.py
import sklearn.metrics

def compute_metrics(y_true, y_pred):
    """
    Compute standard metrics for multi-label classification.

    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities

    Returns:
        dict: Dictionary of metrics
    """
    # Convert predictions to binary using 0.5 threshold
    y_pred_binary = (y_pred > 0.5).astype(int)

    # Compute metrics
    f1_micro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='micro')
    f1_macro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='macro')
    f1_samples = sklearn.metrics.f1_score(y_true, y_pred_binary, average='samples')
    precision = sklearn.metrics.precision_score(y_true, y_pred_binary, average='micro')
    recall = sklearn.metrics.recall_score(y_true, y_pred_binary, average='micro')

    # Compute mAP
    ap_scores = []
    for i in range(y_true.shape[1]):
        if np.sum(y_true[:, i]) > 0:  # Only compute AP if there are positive samples
            ap = sklearn.metrics.average_precision_score(y_true[:, i], y_pred[:, i])
            ap_scores.append(ap)
    mAP = np.mean(ap_scores) if ap_scores else 0.0

    return {
        'f1_micro': f1_micro,
        'f1_macro': f1_macro,
        'f1': f1_samples,  # This is the sample-averaged F1 score
        'precision': precision,
        'recall': recall,
        'mAP': mAP
    }

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def compute_enhanced_metrics(y_true, y_pred, threshold=0.5):
    """
    Compute enhanced evaluation metrics for multi-label classification.
    Adds F1-Micro, F1-Macro, and Hamming Accuracy to the standard metrics.

    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities
        threshold (float): Threshold for binary prediction

    Returns:
        dict: Dictionary of metrics
    """
    # Convert predictions to binary using threshold
    y_pred_binary = (y_pred > threshold).astype(int)

    # Compute standard metrics (samples average - for multi-label)
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average='samples'
    )

    # Compute F1-Micro (global average)
    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average='micro'
    )

    # Compute F1-Macro (unweighted mean per label)
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average='macro'
    )

    # Compute mean average precision
    mAP = average_precision_score(y_true, y_pred, average='samples')

    # Compute Hamming Accuracy
    # Hamming Accuracy = 1 - Hamming Loss
    # Hamming Loss = (y_true != y_pred_binary).mean()
    hamming_accuracy = 1.0 - np.mean(np.abs(y_true - y_pred_binary))

    # Compute per-class metrics
    per_class_precision, per_class_recall, per_class_f1, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average=None
    )

    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,  # This is F1-Samples
        'f1_micro': f1_micro,
        'f1_macro': f1_macro,
        'precision_micro': precision_micro,
        'precision_macro': precision_macro,
        'recall_micro': recall_micro,
        'recall_macro': recall_macro,
        'hamming_accuracy': hamming_accuracy,
        'mAP': mAP,
        'per_class_precision': per_class_precision.tolist(),
        'per_class_recall': per_class_recall.tolist(),
        'per_class_f1': per_class_f1.tolist()
    }

def compute_mutual_information(x, y, bins=20):
    """
    Compute mutual information between two sets of features using scikit-learn.
    This implementation follows SOTA methods for more reliable estimation.

    Args:
        x (numpy.ndarray): First set of features
        y (numpy.ndarray): Second set of features
        bins (int): Number of bins for histogram

    Returns:
        float: Mutual information value (normalized between 0 and 1)
    """
    try:
        from sklearn.feature_selection import mutual_info_regression

        # Reduce dimensionality for more reliable estimation
        from sklearn.decomposition import PCA

        # Use PCA to reduce dimensions while preserving 95% of variance
        x_pca = PCA(n_components=min(x.shape[1], 10), random_state=42)
        y_pca = PCA(n_components=min(y.shape[1], 10), random_state=42)

        x_reduced = x_pca.fit_transform(x)
        y_reduced = y_pca.fit_transform(y)

        # Compute mutual information between each pair of dimensions
        total_mi = 0.0
        count = 0

        for i in range(x_reduced.shape[1]):
            x_i = x_reduced[:, i].reshape(-1, 1)
            for j in range(y_reduced.shape[1]):
                y_j = y_reduced[:, j]

                # Compute mutual information using scikit-learn
                mi = mutual_info_regression(x_i, y_j, random_state=42)[0]

                # Normalize MI by entropy
                x_entropy = compute_entropy(x_i.flatten(), bins)
                y_entropy = compute_entropy(y_j, bins)

                # Avoid division by zero
                if x_entropy > 0 and y_entropy > 0:
                    normalized_mi = mi / np.sqrt(x_entropy * y_entropy)
                    total_mi += normalized_mi
                    count += 1

        # Return average normalized mutual information
        if count > 0:
            return min(1.0, max(0.0, total_mi / count))
        else:
            return 0.0

    except Exception as e:
        # Fallback to a simpler method if scikit-learn method fails
        logger.warning(f"Error in mutual information calculation: {e}. Using fallback method.")
        return compute_mutual_information_fallback(x, y, bins)

def compute_entropy(x, bins=20):
    """
    Compute entropy of a 1D array.

    Args:
        x (numpy.ndarray): 1D array
        bins (int): Number of bins for histogram

    Returns:
        float: Entropy value
    """
    # Compute histogram
    hist, _ = np.histogram(x, bins=bins)

    # Normalize to get probabilities
    p = hist / np.sum(hist)

    # Remove zeros
    p = p[p > 0]

    # Compute entropy
    return -np.sum(p * np.log(p))

def compute_mutual_information_fallback(x, y, bins=20):
    """
    Fallback method for mutual information calculation.

    Args:
        x (numpy.ndarray): First set of features
        y (numpy.ndarray): Second set of features
        bins (int): Number of bins for histogram

    Returns:
        float: Mutual information value (normalized between 0 and 1)
    """
    # Reduce dimensionality
    from sklearn.decomposition import PCA

    x_pca = PCA(n_components=min(x.shape[1], 3), random_state=42)
    y_pca = PCA(n_components=min(y.shape[1], 3), random_state=42)

    x_reduced = x_pca.fit_transform(x)
    y_reduced = y_pca.fit_transform(y)

    # Flatten to 1D arrays by taking the first principal component
    x_flat = x_reduced[:, 0]
    y_flat = y_reduced[:, 0]

    # Compute joint and marginal histograms
    hist_x, _ = np.histogram(x_flat, bins=bins)
    hist_y, _ = np.histogram(y_flat, bins=bins)
    hist_xy, _, _ = np.histogram2d(x_flat, y_flat, bins=bins)

    # Normalize to get probabilities
    p_x = hist_x / np.sum(hist_x)
    p_y = hist_y / np.sum(hist_y)
    p_xy = hist_xy / np.sum(hist_xy)

    # Compute mutual information
    mi = 0.0
    for i in range(bins):
        for j in range(bins):
            if p_xy[i, j] > 0 and p_x[i] > 0 and p_y[j] > 0:
                mi += p_xy[i, j] * np.log(p_xy[i, j] / (p_x[i] * p_y[j]))

    # Normalize by entropy
    h_x = -np.sum(p_x[p_x > 0] * np.log(p_x[p_x > 0]))
    h_y = -np.sum(p_y[p_y > 0] * np.log(p_y[p_y > 0]))

    # Avoid division by zero
    if h_x > 0 and h_y > 0:
        normalized_mi = mi / np.sqrt(h_x * h_y)
        return min(1.0, max(0.0, normalized_mi))
    else:
        return 0.0

def compute_feature_independence(text_encoded, visual_encoded):
    """
    Compute feature independence between text and visual features.
    This implementation follows SOTA methods for more reliable estimation.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features

    Returns:
        float: Feature independence score (higher is better)
    """
    try:
        # Normalize features to have zero mean and unit variance
        text_norm = (text_encoded - np.mean(text_encoded, axis=0, keepdims=True)) / (np.std(text_encoded, axis=0, keepdims=True) + 1e-8)
        visual_norm = (visual_encoded - np.mean(visual_encoded, axis=0, keepdims=True)) / (np.std(visual_encoded, axis=0, keepdims=True) + 1e-8)

        # Calculate correlation matrix
        # Handle NaN values that might occur due to constant features
        with np.errstate(invalid='ignore', divide='ignore'):
            correlation_matrix = np.corrcoef(text_norm.T, visual_norm.T)

        # Replace NaN values with zeros (no correlation)
        correlation_matrix = np.nan_to_num(correlation_matrix)

        # Extract the cross-correlation block
        text_dim = text_encoded.shape[1]
        visual_dim = visual_encoded.shape[1]
        cross_corr = correlation_matrix[:text_dim, text_dim:text_dim+visual_dim]

        # Average absolute correlation (lower is better)
        avg_abs_corr = np.mean(np.abs(cross_corr))

        # Convert to independence score (higher is better)
        independence_score = 1.0 - avg_abs_corr

        # Ensure the score is in [0, 1] range
        independence_score = max(0.0, min(1.0, independence_score))

        return independence_score

    except Exception as e:
        logger.warning(f"Error in feature independence calculation: {e}. Using fallback method.")
        return compute_feature_independence_fallback(text_encoded, visual_encoded)

def compute_feature_independence_fallback(text_encoded, visual_encoded):
    """
    Fallback method for feature independence calculation.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features

    Returns:
        float: Feature independence score (higher is better)
    """
    # Use a more robust method based on canonical correlation analysis
    try:
        from sklearn.cross_decomposition import CCA

        # Reduce dimensionality if needed
        max_components = min(text_encoded.shape[1], visual_encoded.shape[1], text_encoded.shape[0] - 1)
        n_components = min(10, max_components)  # Use at most 10 components

        if n_components < 2:
            # Not enough dimensions for CCA, use a simpler method
            return 0.5  # Return a neutral value

        # Apply CCA
        cca = CCA(n_components=n_components)
        cca.fit(text_encoded, visual_encoded)

        # Get correlations
        correlations = np.array(cca.score(text_encoded, visual_encoded))

        # Average correlation (higher means more dependence)
        avg_corr = np.mean(correlations)

        # Convert to independence score (higher is better)
        independence_score = 1.0 - avg_corr

        # Ensure the score is in [0, 1] range
        independence_score = max(0.0, min(1.0, independence_score))

        return independence_score

    except Exception as e:
        logger.warning(f"Error in feature independence fallback calculation: {e}. Using default value.")
        return 0.5  # Return a neutral value

def compute_orthogonality(x, y):
    """
    Compute orthogonality between two sets of features.

    Args:
        x (numpy.ndarray): First set of features
        y (numpy.ndarray): Second set of features

    Returns:
        float: Orthogonality score (higher is better)
    """
    # Normalize features
    x_norm = x / (np.linalg.norm(x, axis=1, keepdims=True) + 1e-8)
    y_norm = y / (np.linalg.norm(y, axis=1, keepdims=True) + 1e-8)

    # Compute absolute cosine similarity
    similarity = np.abs(np.sum(x_norm * y_norm, axis=1))

    # Convert to orthogonality score (higher is better)
    orthogonality = 1.0 - np.mean(similarity)

    return orthogonality

def compute_modality_specificity(text_encoded, visual_encoded):
    """
    Compute modality specificity metrics based on SOTA methods.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features

    Returns:
        tuple: Text specificity and visual specificity scores
    """
    try:
        # Normalize features
        text_norm = (text_encoded - np.mean(text_encoded, axis=0, keepdims=True)) / (np.std(text_encoded, axis=0, keepdims=True) + 1e-8)
        visual_norm = (visual_encoded - np.mean(visual_encoded, axis=0, keepdims=True)) / (np.std(visual_encoded, axis=0, keepdims=True) + 1e-8)

        # Compute intra-modality and inter-modality distances
        from sklearn.metrics.pairwise import euclidean_distances

        # Sample a subset if the dataset is too large (for efficiency)
        max_samples = min(1000, text_encoded.shape[0])
        if text_encoded.shape[0] > max_samples:
            indices = np.random.choice(text_encoded.shape[0], max_samples, replace=False)
            text_subset = text_norm[indices]
            visual_subset = visual_norm[indices]
        else:
            text_subset = text_norm
            visual_subset = visual_norm

        # Compute distances
        text_distances = euclidean_distances(text_subset)
        visual_distances = euclidean_distances(visual_subset)
        cross_distances = euclidean_distances(text_subset, visual_subset)

        # Remove self-distances (diagonal)
        np.fill_diagonal(text_distances, np.inf)
        np.fill_diagonal(visual_distances, np.inf)

        # Compute nearest neighbor distances
        text_nn_distances = np.min(text_distances, axis=1)
        visual_nn_distances = np.min(visual_distances, axis=1)
        cross_nn_distances = np.min(cross_distances, axis=1)

        # Compute specificity scores
        text_specificity = np.mean(cross_nn_distances / (text_nn_distances + cross_nn_distances + 1e-8))
        visual_specificity = np.mean(cross_nn_distances / (visual_nn_distances + cross_nn_distances + 1e-8))

        # Ensure scores are in [0, 1] range
        text_specificity = max(0.0, min(1.0, text_specificity))
        visual_specificity = max(0.0, min(1.0, visual_specificity))

        return text_specificity, visual_specificity

    except Exception as e:
        logger.warning(f"Error in modality specificity calculation: {e}. Using default values.")
        return 0.5, 0.5

def compute_disentanglement_metrics(text_encoded, visual_encoded, redundancy_scores, text_refined=None, visual_refined=None):
    """
    Compute comprehensive disentanglement metrics based on encoded features and redundancy scores.
    This implementation follows SOTA methods for more reliable estimation.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features
        redundancy_scores (numpy.ndarray): Redundancy scores
        text_refined (numpy.ndarray, optional): Refined text features after redundancy removal
        visual_refined (numpy.ndarray, optional): Refined visual features after redundancy removal

    Returns:
        dict: Dictionary of disentanglement metrics
    """
    metrics = {}

    try:
        # 1. Modality Disentanglement Score (based on redundancy)
        # Lower redundancy means better disentanglement
        # Clip to ensure it's in [0, 1] range
        mean_redundancy = np.mean(redundancy_scores)
        metrics['modality_disentanglement_score'] = max(0.0, min(1.0, 1.0 - mean_redundancy))

        # 2. Cross-Modal Redundancy
        # Direct measure of redundancy between modalities
        metrics['cross_modal_redundancy'] = max(0.0, min(1.0, mean_redundancy))

        # 3. Feature Independence
        # Measure of independence between modality features
        metrics['feature_independence'] = compute_feature_independence(text_encoded, visual_encoded)

        # 4. Modality Specificity
        # Measure of how specific features are to each modality
        text_specificity, visual_specificity = compute_modality_specificity(text_encoded, visual_encoded)
        metrics['text_specificity'] = text_specificity
        metrics['visual_specificity'] = visual_specificity
        metrics['modality_specificity'] = (text_specificity + visual_specificity) / 2.0

        # 5. Shared Information Preservation
        # Cosine similarity between normalized features
        text_norm = text_encoded / (np.linalg.norm(text_encoded, axis=1, keepdims=True) + 1e-8)
        visual_norm = visual_encoded / (np.linalg.norm(visual_encoded, axis=1, keepdims=True) + 1e-8)

        # Calculate cosine similarity for each sample
        cosine_sim = np.sum(text_norm * visual_norm, axis=1)
        metrics['shared_information_preservation'] = max(0.0, min(1.0, np.mean(cosine_sim)))

        # 6. Mutual Information
        # Measure of information shared between modalities
        metrics['mutual_information'] = compute_mutual_information(text_encoded, visual_encoded)

        # 7. Redundancy Distribution
        # Statistics about redundancy distribution
        metrics['redundancy_min'] = float(np.min(redundancy_scores))
        metrics['redundancy_max'] = float(np.max(redundancy_scores))
        metrics['redundancy_std'] = float(np.std(redundancy_scores))

        # 8. Cross-Modal Transfer
        # Measure of how well one modality can predict the other
        metrics['text_to_image_transfer'] = compute_cross_modal_transfer(text_encoded, visual_encoded)
        metrics['image_to_text_transfer'] = compute_cross_modal_transfer(visual_encoded, text_encoded)

        # 9. Average Orthogonality
        # Measure of orthogonality between modalities
        metrics['average_orthogonality'] = compute_average_orthogonality(text_encoded, visual_encoded)

        # 10. Modality Separation
        # Measure of separation between modalities in feature space
        metrics['modality_separation'] = compute_modality_separation(text_encoded, visual_encoded)

        # 11. Shared and Specific Information
        # Measure of shared and specific information between modalities
        shared_info, text_specific_info, visual_specific_info = compute_shared_specific_information(
            text_encoded, visual_encoded
        )
        metrics['shared_information'] = shared_info
        metrics['text_specific_information'] = text_specific_info
        metrics['visual_specific_information'] = visual_specific_info

        # 12. Redundancy Effect (if refined features are provided)
        if text_refined is not None and visual_refined is not None:
            # Measure how much features change after redundancy removal
            text_change = np.linalg.norm(text_refined - text_encoded, axis=1)
            visual_change = np.linalg.norm(visual_refined - visual_encoded, axis=1)

            metrics['text_refinement_magnitude'] = float(np.mean(text_change))
            metrics['visual_refinement_magnitude'] = float(np.mean(visual_change))

            # Correlation between redundancy and feature change
            # Handle potential NaN values
            with np.errstate(invalid='ignore'):
                text_corr = np.corrcoef(redundancy_scores.flatten(), text_change)[0, 1]
                visual_corr = np.corrcoef(redundancy_scores.flatten(), visual_change)[0, 1]

            metrics['text_redundancy_effect'] = 0.0 if np.isnan(text_corr) else float(text_corr)
            metrics['visual_redundancy_effect'] = 0.0 if np.isnan(visual_corr) else float(visual_corr)

            # Independence after refinement
            metrics['refined_feature_independence'] = compute_feature_independence(text_refined, visual_refined)

            # Improvement in independence
            metrics['independence_improvement'] = metrics['refined_feature_independence'] - metrics['feature_independence']

            # Modality specificity after refinement
            text_spec_refined, visual_spec_refined = compute_modality_specificity(text_refined, visual_refined)
            metrics['refined_text_specificity'] = text_spec_refined
            metrics['refined_visual_specificity'] = visual_spec_refined
            metrics['refined_modality_specificity'] = (text_spec_refined + visual_spec_refined) / 2.0

            # Specificity improvement
            metrics['text_specificity_improvement'] = text_spec_refined - text_specificity
            metrics['visual_specificity_improvement'] = visual_spec_refined - visual_specificity

            # Orthogonality after refinement
            metrics['refined_orthogonality'] = compute_average_orthogonality(text_refined, visual_refined)
            metrics['orthogonality_improvement'] = metrics['refined_orthogonality'] - metrics['average_orthogonality']

            # Modality separation after refinement
            metrics['refined_modality_separation'] = compute_modality_separation(text_refined, visual_refined)
            metrics['separation_improvement'] = metrics['refined_modality_separation'] - metrics['modality_separation']

    except Exception as e:
        logger.warning(f"Error in disentanglement metrics calculation: {e}")
        # Provide default values for essential metrics
        metrics = {
            'modality_disentanglement_score': 0.5,
            'cross_modal_redundancy': 0.5,
            'feature_independence': 0.5,
            'shared_information_preservation': 0.5,
            'mutual_information': 0.1,
            'text_specificity': 0.5,
            'visual_specificity': 0.5,
            'modality_specificity': 0.5,
            'average_orthogonality': 0.5,
            'modality_separation': 0.5,
            'shared_information': 0.33,
            'text_specific_information': 0.33,
            'visual_specific_information': 0.33
        }

        if text_refined is not None and visual_refined is not None:
            metrics.update({
                'text_refinement_magnitude': 1.0,
                'visual_refinement_magnitude': 1.0,
                'text_redundancy_effect': 0.0,
                'visual_redundancy_effect': 0.0,
                'refined_feature_independence': 0.5,
                'independence_improvement': 0.0,
                'refined_orthogonality': 0.5,
                'orthogonality_improvement': 0.0,
                'refined_modality_separation': 0.5,
                'separation_improvement': 0.0
            })

    return metrics

def compute_cross_modal_transfer(source_features, target_features):
    """
    Compute cross-modal transfer score: how well source modality can predict target modality.

    Args:
        source_features (numpy.ndarray): Source modality features
        target_features (numpy.ndarray): Target modality features

    Returns:
        float: Cross-modal transfer score
    """
    try:
        from sklearn.linear_model import Ridge
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import r2_score

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            source_features, target_features, test_size=0.2, random_state=42
        )

        # Train a simple linear model
        model = Ridge(alpha=1.0)
        model.fit(X_train, y_train)

        # Predict and evaluate
        y_pred = model.predict(X_test)

        # Compute R² score
        r2 = r2_score(y_test, y_pred)

        # Convert to a transfer score in [0, 1]
        # Higher R² means better transfer
        transfer_score = max(0.0, min(1.0, r2))

        return transfer_score

    except Exception as e:
        logger.warning(f"Error in cross-modal transfer calculation: {e}. Using default value.")
        return 0.1  # Default low transfer score

def compute_shared_specific_information(text_encoded, visual_encoded, n_bins=20):
    """
    Compute shared and specific information between text and visual features.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features
        n_bins (int): Number of bins for histogram

    Returns:
        tuple: Shared information, text-specific information, visual-specific information
    """
    try:
        # Compute mutual information
        mutual_info = compute_mutual_information(text_encoded, visual_encoded, n_bins)

        # Compute entropy for each modality
        from sklearn.feature_selection import mutual_info_regression

        # Estimate entropy using mutual information with itself
        text_entropy = np.mean([
            mutual_info_regression(text_encoded[:, i].reshape(-1, 1), text_encoded[:, i])[0]
            for i in range(text_encoded.shape[1])
        ])

        visual_entropy = np.mean([
            mutual_info_regression(visual_encoded[:, i].reshape(-1, 1), visual_encoded[:, i])[0]
            for i in range(visual_encoded.shape[1])
        ])

        # Compute shared and specific information
        shared_info = mutual_info
        text_specific_info = max(0.0, text_entropy - mutual_info)
        visual_specific_info = max(0.0, visual_entropy - mutual_info)

        # Normalize to [0, 1] range
        total_info = shared_info + text_specific_info + visual_specific_info
        if total_info > 0:
            shared_info /= total_info
            text_specific_info /= total_info
            visual_specific_info /= total_info

        return shared_info, text_specific_info, visual_specific_info

    except Exception as e:
        logger.warning(f"Error in shared/specific information calculation: {e}. Using default values.")
        return 0.33, 0.33, 0.33  # Return neutral values

def compute_average_orthogonality(text_encoded, visual_encoded):
    """
    Compute average orthogonality between text and visual features.
    Orthogonality is measured as 1 - |cos(θ)|, where θ is the angle between feature vectors.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features

    Returns:
        float: Average orthogonality (higher is better)
    """
    try:
        # Normalize features
        text_norm = text_encoded / (np.linalg.norm(text_encoded, axis=1, keepdims=True) + 1e-8)
        visual_norm = visual_encoded / (np.linalg.norm(visual_encoded, axis=1, keepdims=True) + 1e-8)

        # Compute cosine similarity for each pair of samples
        cosine_sim = np.abs(np.sum(text_norm * visual_norm, axis=1))

        # Convert to orthogonality (1 - |cos(θ)|)
        orthogonality = 1.0 - cosine_sim

        # Average orthogonality
        avg_orthogonality = np.mean(orthogonality)

        return avg_orthogonality

    except Exception as e:
        logger.warning(f"Error in orthogonality calculation: {e}. Using default value.")
        return 0.5  # Return a neutral value

def compute_modality_separation(text_encoded, visual_encoded):
    """
    Compute modality separation score based on inter-modality and intra-modality distances.

    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features

    Returns:
        float: Modality separation score (higher is better)
    """
    try:
        from sklearn.metrics.pairwise import euclidean_distances

        # Compute intra-modality distances
        text_distances = euclidean_distances(text_encoded)
        visual_distances = euclidean_distances(visual_encoded)

        # Remove self-distances (diagonal)
        np.fill_diagonal(text_distances, np.inf)
        np.fill_diagonal(visual_distances, np.inf)

        # Compute inter-modality distances
        inter_distances = euclidean_distances(text_encoded, visual_encoded)

        # Compute average nearest neighbor distances
        text_nn_dist = np.min(text_distances, axis=1).mean()
        visual_nn_dist = np.min(visual_distances, axis=1).mean()
        inter_nn_dist = np.min(inter_distances, axis=1).mean()

        # Compute modality separation score
        # Higher ratio of inter-modality to intra-modality distances indicates better separation
        intra_dist = (text_nn_dist + visual_nn_dist) / 2.0
        separation_score = inter_nn_dist / (intra_dist + 1e-8)

        # Normalize to [0, 1] range using a sigmoid-like function
        normalized_score = 2.0 / (1.0 + np.exp(-separation_score)) - 1.0

        return normalized_score

    except Exception as e:
        logger.warning(f"Error in modality separation calculation: {e}. Using default value.")
        return 0.5  # Return a neutral value

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Enhanced evaluation for KG-Disentangle-Net")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output',
                        help='Output directory for saving evaluation results')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Evaluation arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')

    return parser.parse_args()

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Create test dataset
    logger.info("Creating test dataset...")
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    # Create data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)

    # Load model weights
    logger.info(f"Loading model from {args.model_path}...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model.eval()

    # Evaluate model
    logger.info("Evaluating model...")
    test_preds = []
    test_labels = []
    all_text_encoded = []
    all_visual_encoded = []
    all_text_refined = []
    all_visual_refined = []
    all_redundancy_scores = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image

            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            logits = outputs['logits']

            # Collect predictions and labels
            test_preds.append(torch.sigmoid(logits).detach().cpu().numpy())
            test_labels.append(labels.detach().cpu().numpy())

            # Collect features for disentanglement metrics
            all_text_encoded.append(outputs['text_encoded'].detach().cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].detach().cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].detach().cpu().numpy())

            # Collect refined features if available
            if 'text_refined' in outputs:
                all_text_refined.append(outputs['text_refined'].detach().cpu().numpy())
            if 'visual_refined' in outputs:
                all_visual_refined.append(outputs['visual_refined'].detach().cpu().numpy())

    # Stack all predictions and labels
    test_preds = np.vstack(test_preds)
    test_labels = np.vstack(test_labels)

    # Stack all features
    all_text_encoded = np.vstack(all_text_encoded)
    all_visual_encoded = np.vstack(all_visual_encoded)
    all_redundancy_scores = np.vstack(all_redundancy_scores)

    # Stack refined features if available
    text_refined = None
    visual_refined = None
    if all_text_refined and all_visual_refined:
        text_refined = np.vstack(all_text_refined)
        visual_refined = np.vstack(all_visual_refined)

    # Compute standard and enhanced metrics
    standard_metrics = compute_metrics(test_labels, test_preds)
    enhanced_metrics = compute_enhanced_metrics(test_labels, test_preds, args.threshold)

    # Compute disentanglement metrics with refined features if available
    if text_refined is not None and visual_refined is not None:
        disentanglement_metrics = compute_disentanglement_metrics(
            all_text_encoded, all_visual_encoded, all_redundancy_scores,
            text_refined, visual_refined
        )
    else:
        disentanglement_metrics = compute_disentanglement_metrics(
            all_text_encoded, all_visual_encoded, all_redundancy_scores
        )

    # Combine all metrics
    all_metrics = {**standard_metrics, **enhanced_metrics, **disentanglement_metrics}

    # Log metrics
    logger.info("Enhanced Test Results:")
    logger.info(f"F1 (Samples): {all_metrics['f1']:.4f}")
    logger.info(f"F1-Micro: {all_metrics['f1_micro']:.4f}")
    logger.info(f"F1-Macro: {all_metrics['f1_macro']:.4f}")
    logger.info(f"Hamming Accuracy: {all_metrics['hamming_accuracy']:.4f}")
    logger.info(f"Precision: {all_metrics['precision']:.4f}")
    logger.info(f"Recall: {all_metrics['recall']:.4f}")
    logger.info(f"mAP: {all_metrics['mAP']:.4f}")

    logger.info("\n=== Disentanglement Metrics ===")
    logger.info(f"Modality Disentanglement Score: {all_metrics['modality_disentanglement_score']:.4f}")
    logger.info(f"Cross-Modal Redundancy: {all_metrics['cross_modal_redundancy']:.4f}")
    logger.info(f"Feature Independence: {all_metrics['feature_independence']:.4f}")

    # Modality Specificity
    logger.info(f"\nModality Specificity: {all_metrics['modality_specificity']:.4f}")
    logger.info(f"  - Text Specificity: {all_metrics['text_specificity']:.4f}")
    logger.info(f"  - Visual Specificity: {all_metrics['visual_specificity']:.4f}")

    # Information Metrics
    logger.info(f"\nInformation Metrics:")
    logger.info(f"Shared Information Preservation: {all_metrics['shared_information_preservation']:.4f}")
    logger.info(f"Mutual Information: {all_metrics['mutual_information']:.4f}")

    # Cross-Modal Transfer
    logger.info(f"\nCross-Modal Transfer:")
    logger.info(f"Text → Image Transfer: {all_metrics['text_to_image_transfer']:.4f}")
    logger.info(f"Image → Text Transfer: {all_metrics['image_to_text_transfer']:.4f}")

    # Redundancy Statistics
    logger.info(f"\nRedundancy Statistics:")
    logger.info(f"Min: {all_metrics['redundancy_min']:.4f}")
    logger.info(f"Max: {all_metrics['redundancy_max']:.4f}")
    logger.info(f"Std: {all_metrics['redundancy_std']:.4f}")

    # Print refinement metrics if available
    if 'text_refinement_magnitude' in all_metrics:
        logger.info("\n=== Refinement Effect Metrics ===")
        logger.info(f"Refinement Magnitude:")
        logger.info(f"  - Text: {all_metrics['text_refinement_magnitude']:.4f}")
        logger.info(f"  - Visual: {all_metrics['visual_refinement_magnitude']:.4f}")

        logger.info(f"\nRedundancy Effect:")
        logger.info(f"  - Text: {all_metrics['text_redundancy_effect']:.4f}")
        logger.info(f"  - Visual: {all_metrics['visual_redundancy_effect']:.4f}")

        logger.info(f"\nFeature Independence:")
        logger.info(f"  - Before Refinement: {all_metrics['feature_independence']:.4f}")
        logger.info(f"  - After Refinement: {all_metrics['refined_feature_independence']:.4f}")
        logger.info(f"  - Improvement: {all_metrics['independence_improvement']:.4f}")

        logger.info(f"\nModality Specificity After Refinement:")
        logger.info(f"  - Text: {all_metrics['refined_text_specificity']:.4f} (Δ: {all_metrics['text_specificity_improvement']:.4f})")
        logger.info(f"  - Visual: {all_metrics['refined_visual_specificity']:.4f} (Δ: {all_metrics['visual_specificity_improvement']:.4f})")
        logger.info(f"  - Overall: {all_metrics['refined_modality_specificity']:.4f}")

    # Convert numpy values to Python native types for JSON serialization
    json_safe_metrics = {}
    for key, value in all_metrics.items():
        if isinstance(value, np.ndarray):
            json_safe_metrics[key] = value.tolist()
        elif isinstance(value, np.floating):
            json_safe_metrics[key] = float(value)
        elif isinstance(value, np.integer):
            json_safe_metrics[key] = int(value)
        elif value is np.nan:
            json_safe_metrics[key] = None
        else:
            json_safe_metrics[key] = value

    # Save metrics
    with open(os.path.join(args.output_dir, 'enhanced_test_results.json'), 'w') as f:
        json.dump(json_safe_metrics, f, indent=2)

    logger.info(f"Results saved to {os.path.join(args.output_dir, 'enhanced_test_results.json')}")

if __name__ == '__main__':
    main()
