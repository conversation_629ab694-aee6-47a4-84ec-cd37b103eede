"""
Create publication-quality Knowledge Graph visualizations for MM-IMDB dataset.
This script generates high-quality knowledge graph visualizations suitable for top-tier journals.
"""

import os
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as mpatches
from matplotlib import rcParams
import matplotlib as mpl

# Set up high-quality plot settings for publication
mpl.rcParams['pdf.fonttype'] = 42
mpl.rcParams['ps.fonttype'] = 42
mpl.rcParams['font.family'] = 'Arial'
mpl.rcParams['font.size'] = 12
mpl.rcParams['axes.linewidth'] = 1.5
mpl.rcParams['xtick.major.width'] = 1.5
mpl.rcParams['ytick.major.width'] = 1.5
mpl.rcParams['xtick.major.size'] = 5
mpl.rcParams['ytick.major.size'] = 5

# Create output directory
output_dir = './output/mmimdb_kg_visualization'
os.makedirs(output_dir, exist_ok=True)

# Define MM-IMDB related entities
# These are representative examples from the MM-IMDB dataset
movies = [
    "The Shawshank Redemption", "The Godfather", "The Dark Knight",
    "Schindler's List", "Pulp Fiction", "Forrest Gump",
    "Fight Club", "Inception", "The Matrix"
]

genres = [
    "Drama", "Crime", "Action", "Biography", 
    "Thriller", "Comedy", "Sci-Fi", "Romance"
]

plot_keywords = [
    "prison", "mafia", "superhero", "holocaust",
    "hitman", "life journey", "mental illness", 
    "dream", "virtual reality"
]

directors = [
    "Frank Darabont", "Francis Ford Coppola", "Christopher Nolan",
    "Steven Spielberg", "Quentin Tarantino", "Robert Zemeckis",
    "David Fincher", "Lana Wachowski"
]

actors = [
    "Tim Robbins", "Morgan Freeman", "Al Pacino", "Christian Bale",
    "Liam Neeson", "John Travolta", "Tom Hanks", 
    "Brad Pitt", "Leonardo DiCaprio", "Keanu Reeves"
]

# Create a NetworkX graph
G = nx.Graph()

# Add nodes with attributes
for movie in movies:
    G.add_node(movie, type='movie')

for genre in genres:
    G.add_node(genre, type='genre')

for keyword in plot_keywords:
    G.add_node(keyword, type='keyword')

for director in directors:
    G.add_node(director, type='director')

for actor in actors:
    G.add_node(actor, type='actor')

# Add edges with attributes
# Movie-Genre relations
G.add_edge("The Shawshank Redemption", "Drama", relation="has_genre")
G.add_edge("The Godfather", "Drama", relation="has_genre")
G.add_edge("The Godfather", "Crime", relation="has_genre")
G.add_edge("The Dark Knight", "Action", relation="has_genre")
G.add_edge("The Dark Knight", "Thriller", relation="has_genre")
G.add_edge("Schindler's List", "Biography", relation="has_genre")
G.add_edge("Schindler's List", "Drama", relation="has_genre")
G.add_edge("Pulp Fiction", "Crime", relation="has_genre")
G.add_edge("Pulp Fiction", "Drama", relation="has_genre")
G.add_edge("Forrest Gump", "Drama", relation="has_genre")
G.add_edge("Forrest Gump", "Comedy", relation="has_genre")
G.add_edge("Fight Club", "Drama", relation="has_genre")
G.add_edge("Inception", "Sci-Fi", relation="has_genre")
G.add_edge("Inception", "Action", relation="has_genre")
G.add_edge("The Matrix", "Sci-Fi", relation="has_genre")
G.add_edge("The Matrix", "Action", relation="has_genre")

# Movie-Keyword relations
G.add_edge("The Shawshank Redemption", "prison", relation="has_keyword")
G.add_edge("The Godfather", "mafia", relation="has_keyword")
G.add_edge("The Dark Knight", "superhero", relation="has_keyword")
G.add_edge("Schindler's List", "holocaust", relation="has_keyword")
G.add_edge("Pulp Fiction", "hitman", relation="has_keyword")
G.add_edge("Forrest Gump", "life journey", relation="has_keyword")
G.add_edge("Fight Club", "mental illness", relation="has_keyword")
G.add_edge("Inception", "dream", relation="has_keyword")
G.add_edge("The Matrix", "virtual reality", relation="has_keyword")

# Movie-Director relations
G.add_edge("The Shawshank Redemption", "Frank Darabont", relation="directed_by")
G.add_edge("The Godfather", "Francis Ford Coppola", relation="directed_by")
G.add_edge("The Dark Knight", "Christopher Nolan", relation="directed_by")
G.add_edge("Schindler's List", "Steven Spielberg", relation="directed_by")
G.add_edge("Pulp Fiction", "Quentin Tarantino", relation="directed_by")
G.add_edge("Forrest Gump", "Robert Zemeckis", relation="directed_by")
G.add_edge("Fight Club", "David Fincher", relation="directed_by")
G.add_edge("Inception", "Christopher Nolan", relation="directed_by")
G.add_edge("The Matrix", "Lana Wachowski", relation="directed_by")

# Movie-Actor relations
G.add_edge("The Shawshank Redemption", "Tim Robbins", relation="stars")
G.add_edge("The Shawshank Redemption", "Morgan Freeman", relation="stars")
G.add_edge("The Godfather", "Al Pacino", relation="stars")
G.add_edge("The Dark Knight", "Christian Bale", relation="stars")
G.add_edge("Schindler's List", "Liam Neeson", relation="stars")
G.add_edge("Pulp Fiction", "John Travolta", relation="stars")
G.add_edge("Forrest Gump", "Tom Hanks", relation="stars")
G.add_edge("Fight Club", "Brad Pitt", relation="stars")
G.add_edge("Inception", "Leonardo DiCaprio", relation="stars")
G.add_edge("The Matrix", "Keanu Reeves", relation="stars")

# Add some cross-entity relations to make the graph more interesting
# Genre-Keyword relations
G.add_edge("Drama", "mental illness", relation="associated_with")
G.add_edge("Crime", "mafia", relation="associated_with")
G.add_edge("Action", "superhero", relation="associated_with")
G.add_edge("Sci-Fi", "virtual reality", relation="associated_with")

# Director-Genre preferences
G.add_edge("Christopher Nolan", "Sci-Fi", relation="prefers_genre")
G.add_edge("Quentin Tarantino", "Crime", relation="prefers_genre")
G.add_edge("Steven Spielberg", "Drama", relation="prefers_genre")

# Actor-Genre specializations
G.add_edge("Al Pacino", "Crime", relation="specializes_in")
G.add_edge("Tom Hanks", "Drama", relation="specializes_in")
G.add_edge("Keanu Reeves", "Sci-Fi", relation="specializes_in")

# Define a professional color palette suitable for publication
# Using a color-blind friendly palette
colors = ['#0072B2', '#009E73', '#D55E00', '#CC79A7', '#F0E442']  # Blue, Green, Red, Purple, Yellow

# Create the main visualization with publication-quality layout
plt.figure(figsize=(10, 8), dpi=300)

# Use a deterministic layout algorithm with careful tuning
pos = nx.kamada_kawai_layout(G)  # More balanced layout than spring_layout

# Define node colors, sizes, and shapes based on type
node_colors = []
node_sizes = []
node_shapes = []
for node in G.nodes:
    node_type = G.nodes[node].get('type', 'entity')
    if node_type == 'movie':
        node_colors.append(colors[0])
        node_sizes.append(700)
        node_shapes.append('o')  # Circle
    elif node_type == 'genre':
        node_colors.append(colors[1])
        node_sizes.append(600)
        node_shapes.append('s')  # Square
    elif node_type == 'keyword':
        node_colors.append(colors[2])
        node_sizes.append(500)
        node_shapes.append('d')  # Diamond
    elif node_type == 'director':
        node_colors.append(colors[3])
        node_sizes.append(600)
        node_shapes.append('^')  # Triangle up
    elif node_type == 'actor':
        node_colors.append(colors[4])
        node_sizes.append(600)
        node_shapes.append('v')  # Triangle down

# Draw nodes by shape
for shape in set(node_shapes):
    # Get indices of nodes with this shape
    indices = [i for i, s in enumerate(node_shapes) if s == shape]
    nodelist = [list(G.nodes())[i] for i in indices]
    colorlist = [node_colors[i] for i in indices]
    sizelist = [node_sizes[i] for i in indices]
    
    # Draw nodes
    nx.draw_networkx_nodes(G, pos, 
                          nodelist=nodelist,
                          node_color=colorlist,
                          node_size=sizelist,
                          node_shape=shape,
                          alpha=0.9,
                          linewidths=1.5,
                          edgecolors='black')

# Define edge styles based on relation type
edge_colors = []
edge_styles = []
edge_widths = []

for u, v, data in G.edges(data=True):
    relation = data.get('relation', '')
    if relation == 'has_genre':
        edge_colors.append(colors[0])
        edge_styles.append('solid')
        edge_widths.append(2.0)
    elif relation == 'has_keyword':
        edge_colors.append(colors[1])
        edge_styles.append('solid')
        edge_widths.append(2.0)
    elif relation == 'directed_by':
        edge_colors.append(colors[2])
        edge_styles.append('solid')
        edge_widths.append(2.0)
    elif relation == 'stars':
        edge_colors.append(colors[3])
        edge_styles.append('solid')
        edge_widths.append(2.0)
    elif relation in ['associated_with', 'prefers_genre', 'specializes_in']:
        edge_colors.append('gray')
        edge_styles.append('dashed')
        edge_widths.append(1.5)
    else:
        edge_colors.append('gray')
        edge_styles.append('dotted')
        edge_widths.append(1.0)

# Draw edges with styles
for i, (u, v, data) in enumerate(G.edges(data=True)):
    nx.draw_networkx_edges(G, pos, 
                          edgelist=[(u, v)],
                          width=edge_widths[i],
                          alpha=0.7,
                          edge_color=edge_colors[i],
                          style=edge_styles[i])

# Draw labels with careful positioning and formatting
label_pos = {k: (v[0], v[1] + 0.03) for k, v in pos.items()}  # Adjust label positions
nx.draw_networkx_labels(G, label_pos, 
                       font_size=9,
                       font_family='Arial',
                       font_weight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", 
                                facecolor='white', 
                                alpha=0.8, 
                                edgecolor='gray',
                                linewidth=1))

# Create a custom legend
legend_elements = [
    mpatches.Patch(color=colors[0], label='Movie'),
    mpatches.Patch(color=colors[1], label='Genre'),
    mpatches.Patch(color=colors[2], label='Keyword'),
    mpatches.Patch(color=colors[3], label='Director'),
    mpatches.Patch(color=colors[4], label='Actor')
]

# Add a second legend for edge types
edge_legend_elements = [
    mpatches.Patch(color=colors[0], label='has_genre'),
    mpatches.Patch(color=colors[1], label='has_keyword'),
    mpatches.Patch(color=colors[2], label='directed_by'),
    mpatches.Patch(color=colors[3], label='stars'),
    mpatches.Patch(color='gray', label='other relations')
]

# Position the legends
plt.legend(handles=legend_elements, 
          loc='upper left', 
          title='Node Types',
          frameon=True,
          framealpha=0.9,
          edgecolor='black')

# Add a second legend for edge types at a different location
plt.legend(handles=edge_legend_elements, 
          loc='upper right', 
          title='Edge Types',
          frameon=True,
          framealpha=0.9,
          edgecolor='black')

# Remove axis
plt.axis('off')

# Add a title with professional formatting
plt.title("MM-IMDB Knowledge Graph", 
         fontsize=16, 
         fontweight='bold', 
         pad=20)

# Save the figure with publication quality
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'mmimdb_knowledge_graph.png'), 
           dpi=600, 
           bbox_inches='tight',
           format='png')
plt.savefig(os.path.join(output_dir, 'mmimdb_knowledge_graph.pdf'), 
           dpi=600, 
           bbox_inches='tight',
           format='pdf')

# Create a focused visualization for Inception
inception_subgraph = nx.ego_graph(G, "Inception", radius=1)

plt.figure(figsize=(8, 6), dpi=300)

# Use a deterministic layout algorithm
pos_subgraph = nx.kamada_kawai_layout(inception_subgraph)

# Define node attributes for the subgraph
node_colors_sub = []
node_sizes_sub = []
node_shapes_sub = []

for node in inception_subgraph.nodes:
    node_type = G.nodes[node].get('type', 'entity')
    if node_type == 'movie':
        node_colors_sub.append(colors[0])
        node_sizes_sub.append(900)
        node_shapes_sub.append('o')
    elif node_type == 'genre':
        node_colors_sub.append(colors[1])
        node_sizes_sub.append(700)
        node_shapes_sub.append('s')
    elif node_type == 'keyword':
        node_colors_sub.append(colors[2])
        node_sizes_sub.append(700)
        node_shapes_sub.append('d')
    elif node_type == 'director':
        node_colors_sub.append(colors[3])
        node_sizes_sub.append(700)
        node_shapes_sub.append('^')
    elif node_type == 'actor':
        node_colors_sub.append(colors[4])
        node_sizes_sub.append(700)
        node_shapes_sub.append('v')

# Draw nodes by shape
for shape in set(node_shapes_sub):
    # Get indices of nodes with this shape
    indices = [i for i, s in enumerate(node_shapes_sub) if s == shape]
    nodelist = [list(inception_subgraph.nodes())[i] for i in indices]
    colorlist = [node_colors_sub[i] for i in indices]
    sizelist = [node_sizes_sub[i] for i in indices]
    
    # Draw nodes
    nx.draw_networkx_nodes(inception_subgraph, pos_subgraph, 
                          nodelist=nodelist,
                          node_color=colorlist,
                          node_size=sizelist,
                          node_shape=shape,
                          alpha=0.9,
                          linewidths=1.5,
                          edgecolors='black')

# Define edge attributes for the subgraph
edge_colors_sub = []
edge_styles_sub = []
edge_widths_sub = []

for u, v, data in inception_subgraph.edges(data=True):
    relation = data.get('relation', '')
    if relation == 'has_genre':
        edge_colors_sub.append(colors[0])
        edge_styles_sub.append('solid')
        edge_widths_sub.append(2.5)
    elif relation == 'has_keyword':
        edge_colors_sub.append(colors[1])
        edge_styles_sub.append('solid')
        edge_widths_sub.append(2.5)
    elif relation == 'directed_by':
        edge_colors_sub.append(colors[2])
        edge_styles_sub.append('solid')
        edge_widths_sub.append(2.5)
    elif relation == 'stars':
        edge_colors_sub.append(colors[3])
        edge_styles_sub.append('solid')
        edge_widths_sub.append(2.5)
    else:
        edge_colors_sub.append('gray')
        edge_styles_sub.append('dashed')
        edge_widths_sub.append(1.5)

# Draw edges with styles
for i, (u, v, data) in enumerate(inception_subgraph.edges(data=True)):
    nx.draw_networkx_edges(inception_subgraph, pos_subgraph, 
                          edgelist=[(u, v)],
                          width=edge_widths_sub[i],
                          alpha=0.8,
                          edge_color=edge_colors_sub[i],
                          style=edge_styles_sub[i])

# Draw edge labels
edge_labels = {(u, v): d['relation'] for u, v, d in inception_subgraph.edges(data=True)}
nx.draw_networkx_edge_labels(inception_subgraph, pos_subgraph, 
                            edge_labels=edge_labels, 
                            font_size=8,
                            font_family='Arial',
                            bbox=dict(boxstyle="round,pad=0.2", 
                                     facecolor='white', 
                                     alpha=0.8))

# Draw node labels
label_pos_sub = {k: (v[0], v[1] + 0.03) for k, v in pos_subgraph.items()}
nx.draw_networkx_labels(inception_subgraph, label_pos_sub, 
                       font_size=10,
                       font_family='Arial',
                       font_weight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", 
                                facecolor='white', 
                                alpha=0.8, 
                                edgecolor='gray',
                                linewidth=1))

# Add a title
plt.title("Knowledge Graph for 'Inception'", 
         fontsize=16, 
         fontweight='bold', 
         pad=20)

# Remove axis
plt.axis('off')

# Save the figure with publication quality
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'inception_knowledge_graph.png'), 
           dpi=600, 
           bbox_inches='tight',
           format='png')
plt.savefig(os.path.join(output_dir, 'inception_knowledge_graph.pdf'), 
           dpi=600, 
           bbox_inches='tight',
           format='pdf')

print(f"Publication-quality knowledge graph visualizations saved to {output_dir}")
print(f"Files saved in both PNG and PDF formats for journal submission")
