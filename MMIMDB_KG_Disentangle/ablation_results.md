# 消融实验结果

## 实验设置

我们进行了以下消融实验，以评估模型中各个组件的贡献：

1. **full_model**: 完整模型（基准）
2. **no_kg**: 禁用知识图谱组件
3. **no_redundancy**: 禁用冗余检测模块
4. **no_graph_reasoning**: 禁用图推理模块
5. **no_adaptive_fusion**: 禁用自适应融合模块

## 结果汇总

| 实验 | F1 (Samples) | F1-Micro | F1-Macro | Hamming Accuracy | 模态解缠分数 | 跨模态冗余 | 共享信息保留 |
|------|--------------|----------|----------|------------------|--------------|------------|--------------|
| full_model | 0.7679 | 0.7812 | 0.7279 | 0.9551 | 1.0000 | 0.0000 | 0.3039 |
| no_kg | 0.4589 | 0.5001 | 0.2336 | 0.9118 | 1.0000 | 0.0000 | 0.3039 |
| no_redundancy | 0.7679 | 0.7812 | 0.7279 | 0.9551 | 1.0000 | 0.0000 | 0.3039 |
| no_graph_reasoning | 0.5352 | 0.5618 | 0.5337 | 0.9227 | 1.0000 | 0.0000 | 0.3039 |
| no_adaptive_fusion | 0.0890 | 0.1136 | 0.0276 | 0.8901 | 1.0000 | 0.0000 | 0.3039 |

## 性能下降百分比

相比于完整模型，各组件禁用后的性能下降百分比：

| 实验 | F1 (Samples) | F1-Micro | F1-Macro | Hamming Accuracy |
|------|--------------|----------|----------|------------------|
| no_kg | -40.24% | -35.98% | -67.91% | -4.53% |
| no_redundancy | 0.00% | 0.00% | 0.00% | 0.00% |
| no_graph_reasoning | -30.30% | -28.08% | -26.68% | -3.39% |
| no_adaptive_fusion | -88.41% | -85.46% | -96.21% | -6.81% |

## 结果分析

1. **自适应融合模块**的影响最大，禁用后F1分数下降了88.41%，这表明该模块对于模型性能至关重要。

2. **知识图谱组件**的影响也很显著，禁用后F1分数下降了40.24%，特别是F1-Macro下降了67.91%，这表明知识图谱对于处理罕见类别特别重要。

3. **图推理模块**也有显著影响，禁用后F1分数下降了30.30%，这表明基于图的推理对于提取有用特征很重要。

4. 有趣的是，**冗余检测模块**的禁用对性能没有影响，这可能是因为：
   - 模型已经学会了在其他组件中处理冗余信息
   - 在测试数据上，冗余检测的作用不明显
   - 冗余检测模块可能需要进一步优化

5. **解缠指标**在所有实验中保持不变，这表明：
   - 模型的解缠能力主要由其他因素决定
   - 当前的解缠指标可能不够敏感，无法捕捉到组件变化带来的影响
   - 可能需要设计更精细的解缠指标

## 结论

1. 自适应融合模块是模型中最关键的组件，对性能有决定性影响。

2. 知识图谱和图推理模块对模型性能也有显著贡献，特别是在处理复杂和罕见类别时。

3. 冗余检测模块在当前实现中似乎没有显著贡献，可能需要重新设计或优化。

4. 解缠指标保持不变，这表明当前的解缠机制可能与模型的其他组件关联不大，或者当前的指标不够敏感。

这些发现为进一步优化模型提供了有价值的指导，特别是可以重点关注自适应融合模块的改进，以及重新评估冗余检测模块的设计。
