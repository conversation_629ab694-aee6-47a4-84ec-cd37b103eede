"""
Visualize a representative Movie Knowledge Graph.
This script creates a visualization of a movie knowledge graph structure.
"""

import os
import matplotlib.pyplot as plt
import networkx as nx
import random

# Create output directory
output_dir = './output/movie_kg_visualization'
os.makedirs(output_dir, exist_ok=True)

# Define movie-related entities
movies = [
    "The Godfather", "Inception", "The Dark Knight", 
    "Pulp Fiction", "Forrest Gump", "The Matrix",
    "Titanic", "Avatar", "Interstellar"
]

genres = [
    "Drama", "Action", "Thriller", "Crime", 
    "Sci-Fi", "Comedy", "Romance", "Adventure"
]

actors = [
    "<PERSON> Pacino", "<PERSON> DiCaprio", "<PERSON> Bale", 
    "John Tra<PERSON>lta", "<PERSON> Hanks", "<PERSON><PERSON> Reeves",
    "Kate Winslet", "Sam <PERSON>ington", "<PERSON>"
]

directors = [
    "<PERSON>", "<PERSON> Nolan", 
    "<PERSON> Tara<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>"
]

concepts = [
    "Time Travel", "Artificial Intelligence", "Family", 
    "Revenge", "Love", "War", "Space", "Dreams"
]

# Create a NetworkX graph
G = nx.Graph()

# Add nodes with attributes
for movie in movies:
    G.add_node(movie, type='movie')

for genre in genres:
    G.add_node(genre, type='genre')

for actor in actors:
    G.add_node(actor, type='actor')

for director in directors:
    G.add_node(director, type='director')

for concept in concepts:
    G.add_node(concept, type='concept')

# Add edges with attributes
# Movie-Genre relations
G.add_edge("The Godfather", "Drama", relation="has_genre")
G.add_edge("The Godfather", "Crime", relation="has_genre")
G.add_edge("Inception", "Sci-Fi", relation="has_genre")
G.add_edge("Inception", "Action", relation="has_genre")
G.add_edge("The Dark Knight", "Action", relation="has_genre")
G.add_edge("The Dark Knight", "Thriller", relation="has_genre")
G.add_edge("Pulp Fiction", "Crime", relation="has_genre")
G.add_edge("Pulp Fiction", "Drama", relation="has_genre")
G.add_edge("Forrest Gump", "Drama", relation="has_genre")
G.add_edge("Forrest Gump", "Comedy", relation="has_genre")
G.add_edge("The Matrix", "Sci-Fi", relation="has_genre")
G.add_edge("The Matrix", "Action", relation="has_genre")
G.add_edge("Titanic", "Drama", relation="has_genre")
G.add_edge("Titanic", "Romance", relation="has_genre")
G.add_edge("Avatar", "Sci-Fi", relation="has_genre")
G.add_edge("Avatar", "Adventure", relation="has_genre")
G.add_edge("Interstellar", "Sci-Fi", relation="has_genre")
G.add_edge("Interstellar", "Adventure", relation="has_genre")

# Movie-Actor relations
G.add_edge("The Godfather", "Al Pacino", relation="stars")
G.add_edge("Inception", "Leonardo DiCaprio", relation="stars")
G.add_edge("The Dark Knight", "Christian Bale", relation="stars")
G.add_edge("Pulp Fiction", "John Travolta", relation="stars")
G.add_edge("Forrest Gump", "Tom Hanks", relation="stars")
G.add_edge("The Matrix", "Keanu Reeves", relation="stars")
G.add_edge("Titanic", "Leonardo DiCaprio", relation="stars")
G.add_edge("Titanic", "Kate Winslet", relation="stars")
G.add_edge("Avatar", "Sam Worthington", relation="stars")
G.add_edge("Interstellar", "Matthew McConaughey", relation="stars")

# Movie-Director relations
G.add_edge("The Godfather", "Francis Ford Coppola", relation="directed_by")
G.add_edge("Inception", "Christopher Nolan", relation="directed_by")
G.add_edge("The Dark Knight", "Christopher Nolan", relation="directed_by")
G.add_edge("Pulp Fiction", "Quentin Tarantino", relation="directed_by")
G.add_edge("Forrest Gump", "Robert Zemeckis", relation="directed_by")
G.add_edge("The Matrix", "Lana Wachowski", relation="directed_by")
G.add_edge("Titanic", "James Cameron", relation="directed_by")
G.add_edge("Avatar", "James Cameron", relation="directed_by")
G.add_edge("Interstellar", "Christopher Nolan", relation="directed_by")

# Movie-Concept relations
G.add_edge("The Godfather", "Family", relation="has_concept")
G.add_edge("The Godfather", "Revenge", relation="has_concept")
G.add_edge("Inception", "Dreams", relation="has_concept")
G.add_edge("Inception", "Time Travel", relation="has_concept")
G.add_edge("The Dark Knight", "Revenge", relation="has_concept")
G.add_edge("Pulp Fiction", "Revenge", relation="has_concept")
G.add_edge("Forrest Gump", "Love", relation="has_concept")
G.add_edge("The Matrix", "Artificial Intelligence", relation="has_concept")
G.add_edge("Titanic", "Love", relation="has_concept")
G.add_edge("Avatar", "War", relation="has_concept")
G.add_edge("Interstellar", "Space", relation="has_concept")
G.add_edge("Interstellar", "Time Travel", relation="has_concept")

# Create the visualization
plt.figure(figsize=(20, 16))

# Define node positions
pos = nx.spring_layout(G, seed=42, k=0.5)  # k controls the distance between nodes

# Define node colors based on type
node_colors = []
for node in G.nodes:
    node_type = G.nodes[node].get('type', 'entity')
    if node_type == 'movie':
        node_colors.append('lightblue')
    elif node_type == 'genre':
        node_colors.append('lightgreen')
    elif node_type == 'actor':
        node_colors.append('salmon')
    elif node_type == 'director':
        node_colors.append('orange')
    elif node_type == 'concept':
        node_colors.append('purple')
    else:
        node_colors.append('gray')

# Draw nodes
nx.draw_networkx_nodes(G, pos, node_size=700, node_color=node_colors, alpha=0.8)

# Draw edges
nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5)

# Draw labels
nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif')

# Add a title
plt.title("Movie Knowledge Graph Visualization", fontsize=20)

# Add a legend
legend_elements = [
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='lightblue', markersize=15, label='Movie'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='lightgreen', markersize=15, label='Genre'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='salmon', markersize=15, label='Actor'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='orange', markersize=15, label='Director'),
    plt.Line2D([0], [0], marker='o', color='w', markerfacecolor='purple', markersize=15, label='Concept')
]
plt.legend(handles=legend_elements, loc='upper right')

# Remove axis
plt.axis('off')

# Save the figure
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'movie_knowledge_graph.png'), dpi=300, bbox_inches='tight')

# Create a more focused visualization showing a subgraph
# Focus on Christopher Nolan movies and their connections
nolan_movies = ["Inception", "The Dark Knight", "Interstellar"]
nolan_subgraph = G.subgraph(nolan_movies + 
                           [n for movie in nolan_movies for n in G.neighbors(movie)])

plt.figure(figsize=(16, 12))

# Define node positions for the subgraph
pos_subgraph = nx.spring_layout(nolan_subgraph, seed=42, k=0.7)

# Define node colors based on type
node_colors_subgraph = []
for node in nolan_subgraph.nodes:
    node_type = G.nodes[node].get('type', 'entity')
    if node_type == 'movie':
        node_colors_subgraph.append('lightblue')
    elif node_type == 'genre':
        node_colors_subgraph.append('lightgreen')
    elif node_type == 'actor':
        node_colors_subgraph.append('salmon')
    elif node_type == 'director':
        node_colors_subgraph.append('orange')
    elif node_type == 'concept':
        node_colors_subgraph.append('purple')
    else:
        node_colors_subgraph.append('gray')

# Draw nodes
nx.draw_networkx_nodes(nolan_subgraph, pos_subgraph, node_size=900, 
                      node_color=node_colors_subgraph, alpha=0.8)

# Draw edges
nx.draw_networkx_edges(nolan_subgraph, pos_subgraph, width=1.5, alpha=0.7)

# Draw labels
nx.draw_networkx_labels(nolan_subgraph, pos_subgraph, font_size=12, font_family='sans-serif')

# Draw edge labels (relations)
edge_labels = {(u, v): d['relation'] for u, v, d in nolan_subgraph.edges(data=True)}
nx.draw_networkx_edge_labels(nolan_subgraph, pos_subgraph, edge_labels=edge_labels, font_size=10)

# Add a title
plt.title("Christopher Nolan Movies Knowledge Graph", fontsize=20)

# Add a legend
plt.legend(handles=legend_elements, loc='upper right')

# Remove axis
plt.axis('off')

# Save the figure
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'nolan_movies_knowledge_graph.png'), dpi=300, bbox_inches='tight')

print(f"Movie knowledge graph visualizations saved to {output_dir}")
