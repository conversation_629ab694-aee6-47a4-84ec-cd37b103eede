"""
Ablation study script for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.
This script runs different model configurations by disabling specific components
and records the performance metrics.
"""

import os
import argparse
import torch
import torch.nn as nn
import numpy as np
from tqdm import tqdm
import logging
import json
from torch.utils.data import DataLoader
import copy
from collections import OrderedDict

from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
from enhanced_evaluate import compute_enhanced_metrics, compute_disentanglement_metrics

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class AblatedModel(nn.Module):
    """Wrapper for the KGDisentangleNet model with ablation capabilities."""
    
    def __init__(self, base_model, ablation_config):
        """
        Initialize the ablated model.
        
        Args:
            base_model (KGDisentangleNet): The base model to ablate
            ablation_config (dict): Configuration for ablation
                - disable_kg (bool): Whether to disable knowledge graph
                - disable_redundancy (bool): Whether to disable redundancy detection
                - disable_graph_reasoning (bool): Whether to disable graph reasoning
                - disable_adaptive_fusion (bool): Whether to disable adaptive fusion
        """
        super(AblatedModel, self).__init__()
        self.base_model = base_model
        self.ablation_config = ablation_config
    
    def forward(self, text_features, visual_features, kg_features, label_embeddings=None):
        """Forward pass with ablation."""
        # Encode features
        text_encoded = self.base_model.text_encoder(text_features)
        visual_encoded = self.base_model.visual_encoder(visual_features)
        
        # Disable KG if specified
        if self.ablation_config['disable_kg']:
            # Replace KG features with zeros
            kg_encoded = torch.zeros_like(self.base_model.kg_encoder(kg_features))
        else:
            kg_encoded = self.base_model.kg_encoder(kg_features)
        
        # Step 1: Redundancy detection
        if self.ablation_config['disable_redundancy']:
            # Set redundancy score to zero (no redundancy)
            redundancy_score = torch.zeros(text_encoded.size(0), 1).to(text_encoded.device)
            attn_weights = None
        else:
            # Add sequence dimension for redundancy detection
            text_encoded_3d = text_encoded.unsqueeze(1)
            visual_encoded_3d = visual_encoded.unsqueeze(1)
            redundancy_score, attn_weights = self.base_model.redundancy_detector(text_encoded_3d, visual_encoded_3d)
            redundancy_score = redundancy_score.squeeze(1)
        
        # Step 2: Graph reasoning
        if self.ablation_config['disable_graph_reasoning']:
            # Skip graph reasoning
            text_refined = text_encoded
            visual_refined = visual_encoded
        else:
            # Add sequence dimension for graph reasoning
            text_encoded_3d = text_encoded.unsqueeze(1)
            visual_encoded_3d = visual_encoded.unsqueeze(1)
            kg_features_3d = kg_features.unsqueeze(1)
            
            text_refined_3d = self.base_model.graph_reasoner(text_encoded_3d, kg_features_3d)
            visual_refined_3d = self.base_model.graph_reasoner(visual_encoded_3d, kg_features_3d)
            
            # Remove sequence dimension
            text_refined = text_refined_3d.squeeze(1)
            visual_refined = visual_refined_3d.squeeze(1)
        
        # Step 3: Adaptive fusion
        if self.ablation_config['disable_adaptive_fusion']:
            # Simple fusion (average)
            fused = (text_refined + visual_refined + kg_encoded) / 3
        else:
            fused = self.base_model.adaptive_fusion(
                text_refined,
                visual_refined,
                kg_encoded,
                redundancy_score
            )
        
        # Classification
        logits = self.base_model.enhanced_classifier(fused)
        probs = torch.sigmoid(logits)
        
        return {
            'logits': logits,
            'probs': probs,
            'redundancy_score': redundancy_score,
            'attention_weights': attn_weights,
            'text_encoded': text_encoded,
            'visual_encoded': visual_encoded,
            'kg_encoded': kg_encoded,
            'text_refined': text_refined,
            'visual_refined': visual_refined,
            'fused': fused
        }

def run_ablation_experiment(args, ablation_config, experiment_name):
    """
    Run an ablation experiment with the specified configuration.
    
    Args:
        args (argparse.Namespace): Command line arguments
        ablation_config (dict): Configuration for ablation
        experiment_name (str): Name of the experiment
    
    Returns:
        dict: Metrics from the experiment
    """
    # Create output directory
    experiment_dir = os.path.join(args.output_dir, experiment_name)
    os.makedirs(experiment_dir, exist_ok=True)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create test dataset
    logger.info("Creating test dataset...")
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )
    
    # Create data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create base model
    logger.info("Creating model...")
    base_model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)
    
    # Load model weights
    logger.info(f"Loading model from {args.model_path}...")
    base_model.load_state_dict(torch.load(args.model_path, map_location=device))
    
    # Create ablated model
    ablated_model = AblatedModel(base_model, ablation_config).to(device)
    ablated_model.eval()
    
    # Evaluate model
    logger.info(f"Evaluating ablated model: {experiment_name}...")
    test_preds = []
    test_labels = []
    all_text_encoded = []
    all_visual_encoded = []
    all_redundancy_scores = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc=f"Testing {experiment_name}"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None
            
            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features
            
            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image
            
            # Forward pass
            outputs = ablated_model(text_features, image_features, kg_features, label_embeddings)
            logits = outputs['logits']
            
            # Collect predictions and labels
            test_preds.append(torch.sigmoid(logits).detach().cpu().numpy())
            test_labels.append(labels.detach().cpu().numpy())
            
            # Collect features for disentanglement metrics
            all_text_encoded.append(outputs['text_encoded'].detach().cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].detach().cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].detach().cpu().numpy())
    
    # Stack all predictions and labels
    test_preds = np.vstack(test_preds)
    test_labels = np.vstack(test_labels)
    
    # Stack all features
    all_text_encoded = np.vstack(all_text_encoded)
    all_visual_encoded = np.vstack(all_visual_encoded)
    all_redundancy_scores = np.vstack(all_redundancy_scores)
    
    # Compute metrics
    classification_metrics = compute_enhanced_metrics(test_labels, test_preds, args.threshold)
    disentanglement_metrics = compute_disentanglement_metrics(
        all_text_encoded, all_visual_encoded, all_redundancy_scores
    )
    
    # Combine all metrics
    all_metrics = {**classification_metrics, **disentanglement_metrics}
    
    # Log metrics
    logger.info(f"Results for {experiment_name}:")
    logger.info(f"F1 (Samples): {all_metrics['f1']:.4f}")
    logger.info(f"F1-Micro: {all_metrics['f1_micro']:.4f}")
    logger.info(f"F1-Macro: {all_metrics['f1_macro']:.4f}")
    logger.info(f"Hamming Accuracy: {all_metrics['hamming_accuracy']:.4f}")
    logger.info(f"Precision: {all_metrics['precision']:.4f}")
    logger.info(f"Recall: {all_metrics['recall']:.4f}")
    logger.info(f"mAP: {all_metrics['mAP']:.4f}")
    
    logger.info("\nDisentanglement Metrics:")
    logger.info(f"Modality Disentanglement Score: {all_metrics['modality_disentanglement_score']:.4f}")
    logger.info(f"Cross-Modal Redundancy: {all_metrics['cross_modal_redundancy']:.4f}")
    logger.info(f"Shared Information Preservation: {all_metrics['shared_information_preservation']:.4f}")
    
    # Convert numpy values to Python native types for JSON serialization
    json_safe_metrics = {}
    for key, value in all_metrics.items():
        if isinstance(value, np.ndarray):
            json_safe_metrics[key] = value.tolist()
        elif isinstance(value, np.floating):
            json_safe_metrics[key] = float(value)
        elif isinstance(value, np.integer):
            json_safe_metrics[key] = int(value)
        elif value is np.nan:
            json_safe_metrics[key] = None
        else:
            json_safe_metrics[key] = value
    
    # Save metrics
    with open(os.path.join(experiment_dir, 'metrics.json'), 'w') as f:
        json.dump(json_safe_metrics, f, indent=2)
    
    logger.info(f"Results saved to {os.path.join(experiment_dir, 'metrics.json')}")
    
    return all_metrics

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Ablation study for KG-Disentangle-Net")
    
    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/ablation',
                        help='Output directory for saving evaluation results')
    
    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')
    
    # Evaluation arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')
    
    return parser.parse_args()

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Define ablation configurations
    ablation_configs = [
        {
            'name': 'full_model',
            'config': {
                'disable_kg': False,
                'disable_redundancy': False,
                'disable_graph_reasoning': False,
                'disable_adaptive_fusion': False
            }
        },
        {
            'name': 'no_kg',
            'config': {
                'disable_kg': True,
                'disable_redundancy': False,
                'disable_graph_reasoning': False,
                'disable_adaptive_fusion': False
            }
        },
        {
            'name': 'no_redundancy',
            'config': {
                'disable_kg': False,
                'disable_redundancy': True,
                'disable_graph_reasoning': False,
                'disable_adaptive_fusion': False
            }
        },
        {
            'name': 'no_graph_reasoning',
            'config': {
                'disable_kg': False,
                'disable_redundancy': False,
                'disable_graph_reasoning': True,
                'disable_adaptive_fusion': False
            }
        },
        {
            'name': 'no_adaptive_fusion',
            'config': {
                'disable_kg': False,
                'disable_redundancy': False,
                'disable_graph_reasoning': False,
                'disable_adaptive_fusion': True
            }
        }
    ]
    
    # Run ablation experiments
    all_results = {}
    for ablation in ablation_configs:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running ablation experiment: {ablation['name']}")
        logger.info(f"{'='*50}")
        
        metrics = run_ablation_experiment(args, ablation['config'], ablation['name'])
        all_results[ablation['name']] = metrics
    
    # Create summary table
    summary = {
        'experiment': [],
        'f1': [],
        'f1_micro': [],
        'f1_macro': [],
        'hamming_accuracy': [],
        'modality_disentanglement_score': [],
        'cross_modal_redundancy': [],
        'shared_information_preservation': []
    }
    
    for name, metrics in all_results.items():
        summary['experiment'].append(name)
        summary['f1'].append(metrics['f1'])
        summary['f1_micro'].append(metrics['f1_micro'])
        summary['f1_macro'].append(metrics['f1_macro'])
        summary['hamming_accuracy'].append(metrics['hamming_accuracy'])
        summary['modality_disentanglement_score'].append(metrics['modality_disentanglement_score'])
        summary['cross_modal_redundancy'].append(metrics['cross_modal_redundancy'])
        summary['shared_information_preservation'].append(metrics['shared_information_preservation'])
    
    # Save summary
    with open(os.path.join(args.output_dir, 'summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)
    
    # Print summary table
    logger.info("\n\nAblation Study Summary:")
    logger.info(f"{'Experiment':<20} {'F1':<10} {'F1-Micro':<10} {'F1-Macro':<10} {'Hamming':<10} {'Disent.':<10} {'Redundancy':<10} {'Shared':<10}")
    logger.info("-" * 90)
    
    for i in range(len(summary['experiment'])):
        logger.info(f"{summary['experiment'][i]:<20} "
                   f"{summary['f1'][i]:.4f}     "
                   f"{summary['f1_micro'][i]:.4f}     "
                   f"{summary['f1_macro'][i]:.4f}     "
                   f"{summary['hamming_accuracy'][i]:.4f}     "
                   f"{summary['modality_disentanglement_score'][i]:.4f}     "
                   f"{summary['cross_modal_redundancy'][i]:.4f}     "
                   f"{summary['shared_information_preservation'][i]:.4f}")
    
    logger.info(f"\nSummary saved to {os.path.join(args.output_dir, 'summary.json')}")

if __name__ == '__main__':
    main()
