"""
<PERSON><PERSON>t to extract ablation experiment results and generate comparison tables.
This script loads results from JSON files and generates comparison tables.
"""

import os
import json
import argparse
import numpy as np
import pandas as pd
from tabulate import tabulate
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Extract Ablation Experiment Results")
    
    parser.add_argument('--results_dir', type=str, default='./output/ablation_models',
                        help='Directory containing ablation experiment results')
    parser.add_argument('--output_dir', type=str, default='./output/ablation_tables',
                        help='Output directory for saving tables')
    parser.add_argument('--model_variants', type=str, nargs='+',
                        default=['full', 'combined', 'no_redundancy', 'no_graph_reasoning'],
                        help='Model variants to include in comparison')
    
    return parser.parse_args()

def load_results(results_dir, variant):
    """
    Load results for a model variant.
    
    Args:
        results_dir (str): Directory containing results
        variant (str): Model variant name
        
    Returns:
        dict: Results for the model variant
    """
    results_path = os.path.join(results_dir, variant, 'test_results.json')
    if os.path.exists(results_path):
        with open(results_path, 'r') as f:
            return json.load(f)
    else:
        logger.warning(f"Results not found for variant: {variant}")
        return None

def generate_comparison_table(results, model_variants, output_dir):
    """
    Generate comparison table for model variants.
    
    Args:
        results (dict): Dictionary of results for each variant
        model_variants (list): List of model variant names
        output_dir (str): Output directory for saving tables
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Define model variants and their descriptions
    variant_descriptions = {
        'full': 'Full model with all components',
        'combined': 'Model with combined redundancy detection and graph reasoning',
        'no_redundancy': 'Model without redundancy detection module',
        'no_graph_reasoning': 'Model without knowledge graph reasoning module'
    }
    
    # Define metrics to include in the table
    classification_metrics = [
        'f1_micro',
        'f1_macro',
        'precision',
        'recall',
        'mAP'
    ]
    
    disentanglement_metrics = [
        'modality_disentanglement_score',
        'cross_modal_redundancy',
        'feature_independence',
        'shared_information_preservation',
        'mutual_information'
    ]
    
    if 'modality_specificity' in next(iter(results.values())):
        disentanglement_metrics.extend([
            'modality_specificity',
            'text_specificity',
            'visual_specificity'
        ])
    
    if 'text_to_image_transfer' in next(iter(results.values())):
        disentanglement_metrics.extend([
            'text_to_image_transfer',
            'image_to_text_transfer'
        ])
    
    # Create comparison table
    table_data = []
    headers = ['Metric'] + [variant_descriptions.get(v, v) for v in model_variants]
    
    # Classification metrics
    table_data.append(["=== Classification Metrics ==="])
    for metric in classification_metrics:
        row = [metric]
        for variant in model_variants:
            if variant in results and metric in results[variant]:
                row.append(f"{results[variant][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)
    
    # Disentanglement metrics
    table_data.append([""])
    table_data.append(["=== Disentanglement Metrics ==="])
    for metric in disentanglement_metrics:
        row = [metric]
        for variant in model_variants:
            if variant in results and metric in results[variant]:
                row.append(f"{results[variant][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)
    
    # Print comparison table
    logger.info("\nMetrics Comparison:")
    logger.info("\n" + tabulate(table_data, headers=headers, tablefmt='grid'))
    
    # Save comparison table
    with open(os.path.join(output_dir, 'metrics_comparison.txt'), 'w') as f:
        f.write(tabulate(table_data, headers=headers, tablefmt='grid'))
    
    # Save comparison table as CSV
    with open(os.path.join(output_dir, 'metrics_comparison.csv'), 'w') as f:
        f.write(','.join(['Metric'] + [variant_descriptions.get(v, v) for v in model_variants]) + '\n')
        for row in table_data:
            if len(row) == 1:  # Section header
                f.write(f"{row[0]}\n")
            else:
                f.write(','.join([str(cell) for cell in row]) + '\n')
    
    # Create LaTeX table
    latex_table = "\\begin{table}[htbp]\n\\centering\n\\caption{Ablation Study Results}\n\\label{tab:ablation}\n"
    latex_table += "\\begin{tabular}{l" + "c" * len(model_variants) + "}\n\\toprule\n"
    
    # Add headers
    latex_table += "Metric & " + " & ".join([variant_descriptions.get(v, v) for v in model_variants]) + " \\\\\n\\midrule\n"
    
    # Add classification metrics
    latex_table += "\\multicolumn{" + str(len(model_variants) + 1) + "}{l}{\\textbf{Classification Metrics}} \\\\\n"
    for i, row in enumerate(table_data):
        if i == 0 or len(row) == 1:  # Skip section headers
            continue
        if row[0] == "":  # Reached disentanglement metrics
            break
        latex_table += row[0] + " & " + " & ".join(row[1:]) + " \\\\\n"
    
    # Add disentanglement metrics
    latex_table += "\\midrule\n\\multicolumn{" + str(len(model_variants) + 1) + "}{l}{\\textbf{Disentanglement Metrics}} \\\\\n"
    disentanglement_started = False
    for row in table_data:
        if len(row) == 1 and row[0] == "=== Disentanglement Metrics ===":
            disentanglement_started = True
            continue
        if disentanglement_started and len(row) > 1:
            latex_table += row[0] + " & " + " & ".join(row[1:]) + " \\\\\n"
    
    latex_table += "\\bottomrule\n\\end{tabular}\n\\end{table}"
    
    # Save LaTeX table
    with open(os.path.join(output_dir, 'metrics_comparison.tex'), 'w') as f:
        f.write(latex_table)
    
    logger.info(f"Tables saved to {output_dir}")

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load results for each variant
    results = {}
    for variant in args.model_variants:
        variant_results = load_results(args.results_dir, variant)
        if variant_results:
            results[variant] = variant_results
    
    # Generate comparison table
    if results:
        generate_comparison_table(results, args.model_variants, args.output_dir)
    else:
        logger.error("No results found for any variant.")

if __name__ == '__main__':
    main()
