"""
Knowledge Graph Disentanglement Metrics.
This module provides metrics to evaluate how knowledge graphs enhance disentanglement
and improve classification performance.
"""

import os
import numpy as np
import torch
import sklearn.metrics
import logging
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import pearsonr

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def compute_kg_enhanced_disentanglement_metrics(text_encoded, visual_encoded, kg_encoded, graph_enhanced, labels):
    """
    Compute metrics to evaluate how knowledge graphs enhance disentanglement.
    
    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features
        kg_encoded (numpy.ndarray): Knowledge graph encoded features
        graph_enhanced (numpy.ndarray): Graph-enhanced features
        labels (numpy.ndarray): Ground truth labels
        
    Returns:
        dict: Dictionary of KG-enhanced disentanglement metrics
    """
    metrics = {}
    
    try:
        # 1. Knowledge Integration Ratio
        # Measure the ratio of knowledge graph information integrated into features
        # Higher value indicates more knowledge integration
        text_kg_sim = compute_cosine_similarity(text_encoded, kg_encoded)
        visual_kg_sim = compute_cosine_similarity(visual_encoded, kg_encoded)
        enhanced_kg_sim = compute_cosine_similarity(graph_enhanced, kg_encoded)
        
        # Compute integration ratio (how much KG information is added)
        text_integration_ratio = enhanced_kg_sim / (text_kg_sim + 1e-8)
        visual_integration_ratio = enhanced_kg_sim / (visual_kg_sim + 1e-8)
        
        metrics['text_kg_integration_ratio'] = min(10.0, float(text_integration_ratio))  # Cap at 10 to avoid extreme values
        metrics['visual_kg_integration_ratio'] = min(10.0, float(visual_integration_ratio))
        
        # 2. Modality Invariance in Graph Reasoning
        # Measure how well graph reasoning preserves modality-invariant information
        # Compute correlation between original and enhanced features
        text_invariance = compute_feature_correlation(text_encoded, graph_enhanced)
        visual_invariance = compute_feature_correlation(visual_encoded, graph_enhanced)
        
        metrics['text_modality_invariance'] = float(text_invariance)
        metrics['visual_modality_invariance'] = float(visual_invariance)
        
        # 3. Modality Specificity in Graph Reasoning
        # Measure how well graph reasoning enhances modality-specific information
        # Compute orthogonality between modalities after enhancement
        original_orthogonality = compute_orthogonality(text_encoded, visual_encoded)
        enhanced_orthogonality = compute_orthogonality(
            graph_enhanced, visual_encoded if np.mean(text_invariance) > np.mean(visual_invariance) else text_encoded
        )
        
        metrics['original_modality_orthogonality'] = float(original_orthogonality)
        metrics['enhanced_modality_orthogonality'] = float(enhanced_orthogonality)
        metrics['orthogonality_improvement'] = float(enhanced_orthogonality - original_orthogonality)
        
        # 4. Knowledge-Enhanced Classification Performance
        # Measure how knowledge graph enhances classification performance
        # Split data for evaluation
        X_train_text, X_test_text, X_train_visual, X_test_visual, X_train_kg, X_test_kg, X_train_enhanced, X_test_enhanced, y_train, y_test = train_test_split(
            text_encoded, visual_encoded, kg_encoded, graph_enhanced, labels, test_size=0.3, random_state=42
        )
        
        # Evaluate classification performance for each feature type
        text_metrics = evaluate_classification(X_train_text, X_test_text, y_train, y_test)
        visual_metrics = evaluate_classification(X_train_visual, X_test_visual, y_train, y_test)
        kg_metrics = evaluate_classification(X_train_kg, X_test_kg, y_train, y_test)
        enhanced_metrics = evaluate_classification(X_train_enhanced, X_test_enhanced, y_train, y_test)
        
        # Compute improvement ratios
        metrics['text_f1_score'] = float(text_metrics['f1_macro'])
        metrics['visual_f1_score'] = float(visual_metrics['f1_macro'])
        metrics['kg_f1_score'] = float(kg_metrics['f1_macro'])
        metrics['enhanced_f1_score'] = float(enhanced_metrics['f1_macro'])
        
        metrics['text_improvement_ratio'] = float(enhanced_metrics['f1_macro'] / (text_metrics['f1_macro'] + 1e-8))
        metrics['visual_improvement_ratio'] = float(enhanced_metrics['f1_macro'] / (visual_metrics['f1_macro'] + 1e-8))
        metrics['kg_improvement_ratio'] = float(enhanced_metrics['f1_macro'] / (kg_metrics['f1_macro'] + 1e-8))
        
        # 5. Feature Interpretability Enhancement
        # Measure how knowledge graph enhances feature interpretability
        # Compute correlation between feature distances and label distances
        text_interpretability = compute_feature_label_correlation(text_encoded, labels)
        visual_interpretability = compute_feature_label_correlation(visual_encoded, labels)
        kg_interpretability = compute_feature_label_correlation(kg_encoded, labels)
        enhanced_interpretability = compute_feature_label_correlation(graph_enhanced, labels)
        
        metrics['text_interpretability'] = float(text_interpretability)
        metrics['visual_interpretability'] = float(visual_interpretability)
        metrics['kg_interpretability'] = float(kg_interpretability)
        metrics['enhanced_interpretability'] = float(enhanced_interpretability)
        
        metrics['text_interpretability_improvement'] = float(enhanced_interpretability - text_interpretability)
        metrics['visual_interpretability_improvement'] = float(enhanced_interpretability - visual_interpretability)
        
        return metrics
    
    except Exception as e:
        logger.warning(f"Error in KG-enhanced disentanglement metrics calculation: {e}")
        return {
            'text_kg_integration_ratio': 1.0,
            'visual_kg_integration_ratio': 1.0,
            'text_modality_invariance': 0.5,
            'visual_modality_invariance': 0.5,
            'original_modality_orthogonality': 0.5,
            'enhanced_modality_orthogonality': 0.5,
            'orthogonality_improvement': 0.0,
            'text_f1_score': 0.0,
            'visual_f1_score': 0.0,
            'kg_f1_score': 0.0,
            'enhanced_f1_score': 0.0,
            'text_improvement_ratio': 1.0,
            'visual_improvement_ratio': 1.0,
            'kg_improvement_ratio': 1.0,
            'text_interpretability': 0.0,
            'visual_interpretability': 0.0,
            'kg_interpretability': 0.0,
            'enhanced_interpretability': 0.0,
            'text_interpretability_improvement': 0.0,
            'visual_interpretability_improvement': 0.0
        }

def compute_cosine_similarity(features1, features2):
    """
    Compute average cosine similarity between two sets of features.
    
    Args:
        features1 (numpy.ndarray): First set of features
        features2 (numpy.ndarray): Second set of features
        
    Returns:
        float: Average cosine similarity
    """
    # Normalize features
    features1_norm = features1 / (np.linalg.norm(features1, axis=1, keepdims=True) + 1e-8)
    features2_norm = features2 / (np.linalg.norm(features2, axis=1, keepdims=True) + 1e-8)
    
    # Compute cosine similarity
    similarity = np.mean(np.sum(features1_norm * features2_norm, axis=1))
    
    return similarity

def compute_feature_correlation(features1, features2):
    """
    Compute average correlation between corresponding dimensions of two feature sets.
    
    Args:
        features1 (numpy.ndarray): First set of features
        features2 (numpy.ndarray): Second set of features
        
    Returns:
        float: Average correlation
    """
    # Compute correlation for each dimension
    correlations = []
    min_dims = min(features1.shape[1], features2.shape[1])
    
    for i in range(min_dims):
        try:
            corr, _ = pearsonr(features1[:, i], features2[:, i])
            if not np.isnan(corr):
                correlations.append(abs(corr))  # Use absolute correlation
        except:
            pass
    
    # Return average correlation
    if correlations:
        return np.mean(correlations)
    else:
        return 0.0

def compute_orthogonality(features1, features2):
    """
    Compute orthogonality between two sets of features.
    Orthogonality is measured as 1 - |cos(θ)|, where θ is the angle between feature vectors.
    
    Args:
        features1 (numpy.ndarray): First set of features
        features2 (numpy.ndarray): Second set of features
        
    Returns:
        float: Average orthogonality (higher is better)
    """
    # Normalize features
    features1_norm = features1 / (np.linalg.norm(features1, axis=1, keepdims=True) + 1e-8)
    features2_norm = features2 / (np.linalg.norm(features2, axis=1, keepdims=True) + 1e-8)
    
    # Compute cosine similarity for each pair of samples
    cosine_sim = np.abs(np.sum(features1_norm * features2_norm, axis=1))
    
    # Convert to orthogonality (1 - |cos(θ)|)
    orthogonality = 1.0 - cosine_sim
    
    # Average orthogonality
    avg_orthogonality = np.mean(orthogonality)
    
    return avg_orthogonality

def evaluate_classification(X_train, X_test, y_train, y_test):
    """
    Evaluate classification performance.
    
    Args:
        X_train (numpy.ndarray): Training features
        X_test (numpy.ndarray): Test features
        y_train (numpy.ndarray): Training labels
        y_test (numpy.ndarray): Test labels
        
    Returns:
        dict: Classification metrics
    """
    # Convert multi-label to multi-class for simplicity
    y_train_class = y_train.argmax(axis=1) if len(y_train.shape) > 1 else y_train
    y_test_class = y_test.argmax(axis=1) if len(y_test.shape) > 1 else y_test
    
    # Train classifier
    clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
    clf.fit(X_train, y_train_class)
    
    # Predict
    y_pred = clf.predict(X_test)
    
    # Compute metrics
    accuracy = accuracy_score(y_test_class, y_pred)
    f1_macro = f1_score(y_test_class, y_pred, average='macro')
    f1_micro = f1_score(y_test_class, y_pred, average='micro')
    precision = precision_score(y_test_class, y_pred, average='macro')
    recall = recall_score(y_test_class, y_pred, average='macro')
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'f1_micro': f1_micro,
        'precision': precision,
        'recall': recall
    }

def compute_feature_label_correlation(features, labels):
    """
    Compute correlation between feature distances and label distances.
    
    Args:
        features (numpy.ndarray): Features
        labels (numpy.ndarray): Labels
        
    Returns:
        float: Correlation between feature distances and label distances
    """
    # Compute feature distances
    feature_dists = sklearn.metrics.pairwise.euclidean_distances(features)
    
    # Compute label distances
    label_dists = sklearn.metrics.pairwise.euclidean_distances(labels)
    
    # Flatten the distance matrices
    n = feature_dists.shape[0]
    feature_dists_flat = feature_dists[np.triu_indices(n, k=1)]
    label_dists_flat = label_dists[np.triu_indices(n, k=1)]
    
    # Compute correlation
    try:
        corr, _ = pearsonr(feature_dists_flat, label_dists_flat)
        return abs(corr)  # Use absolute correlation
    except:
        return 0.0
