"""
<PERSON><PERSON><PERSON> to fix the summary.json file by converting numpy values to Python native types.
"""

import os
import json
import numpy as np

def fix_json_value(value):
    """Convert numpy values to Python native types."""
    if isinstance(value, np.ndarray):
        return value.tolist()
    elif isinstance(value, np.floating):
        return float(value)
    elif isinstance(value, np.integer):
        return int(value)
    elif value is np.nan:
        return None
    elif isinstance(value, list):
        return [fix_json_value(item) for item in value]
    elif isinstance(value, dict):
        return {k: fix_json_value(v) for k, v in value.items()}
    else:
        return value

def main():
    """Main function."""
    # Load all metrics files
    ablation_dir = './output/ablation'
    experiments = ['full_model', 'no_kg', 'no_redundancy', 'no_graph_reasoning', 'no_adaptive_fusion']
    
    # Create summary dictionary
    summary = {
        'experiment': [],
        'f1': [],
        'f1_micro': [],
        'f1_macro': [],
        'hamming_accuracy': [],
        'modality_disentanglement_score': [],
        'cross_modal_redundancy': [],
        'shared_information_preservation': []
    }
    
    # Load metrics from each experiment
    for experiment in experiments:
        metrics_file = os.path.join(ablation_dir, experiment, 'metrics.json')
        if os.path.exists(metrics_file):
            with open(metrics_file, 'r') as f:
                metrics = json.load(f)
            
            summary['experiment'].append(experiment)
            summary['f1'].append(fix_json_value(metrics.get('f1', 0.0)))
            summary['f1_micro'].append(fix_json_value(metrics.get('f1_micro', 0.0)))
            summary['f1_macro'].append(fix_json_value(metrics.get('f1_macro', 0.0)))
            summary['hamming_accuracy'].append(fix_json_value(metrics.get('hamming_accuracy', 0.0)))
            summary['modality_disentanglement_score'].append(fix_json_value(metrics.get('modality_disentanglement_score', 0.0)))
            summary['cross_modal_redundancy'].append(fix_json_value(metrics.get('cross_modal_redundancy', 0.0)))
            summary['shared_information_preservation'].append(fix_json_value(metrics.get('shared_information_preservation', 0.0)))
    
    # Save summary
    with open(os.path.join(ablation_dir, 'summary_fixed.json'), 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Fixed summary saved to {os.path.join(ablation_dir, 'summary_fixed.json')}")
    
    # Print summary table
    print("\n\nAblation Study Summary:")
    print(f"{'Experiment':<20} {'F1':<10} {'F1-Micro':<10} {'F1-Macro':<10} {'Hamming':<10} {'Disent.':<10} {'Redundancy':<10} {'Shared':<10}")
    print("-" * 90)
    
    for i in range(len(summary['experiment'])):
        print(f"{summary['experiment'][i]:<20} "
              f"{summary['f1'][i]:.4f}     "
              f"{summary['f1_micro'][i]:.4f}     "
              f"{summary['f1_macro'][i]:.4f}     "
              f"{summary['hamming_accuracy'][i]:.4f}     "
              f"{summary['modality_disentanglement_score'][i]:.4f}     "
              f"{summary['cross_modal_redundancy'][i]:.4f}     "
              f"{summary['shared_information_preservation'][i]:.4f}")

if __name__ == '__main__':
    main()
