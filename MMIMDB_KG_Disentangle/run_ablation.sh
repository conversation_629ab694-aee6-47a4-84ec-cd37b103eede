#!/bin/bash

# <PERSON>ript to run ablation experiments

# Default values
DATA_PATH="./data/imdb"
KG_PATH="./kg_data"
OUTPUT_DIR="./output/ablation"
PRETRAINED_DIR="./output/kg_disentangle_v1"
DEVICE="cuda"
BATCH_SIZE=32
TRAIN=false
EVALUATE=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --data_path)
      DATA_PATH="$2"
      shift 2
      ;;
    --kg_path)
      KG_PATH="$2"
      shift 2
      ;;
    --output_dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --pretrained_dir)
      PRETRAINED_DIR="$2"
      shift 2
      ;;
    --device)
      DEVICE="$2"
      shift 2
      ;;
    --batch_size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    --train)
      TRAIN=true
      shift
      ;;
    --evaluate)
      EVALUATE=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Build command
CMD="python run_ablation_experiments.py --data_path $DATA_PATH --kg_path $KG_PATH --output_dir $OUTPUT_DIR --pretrained_dir $PRETRAINED_DIR --device $DEVICE --batch_size $BATCH_SIZE"

# Add train/evaluate flags
if [ "$TRAIN" = true ]; then
  CMD="$CMD --train"
fi

if [ "$EVALUATE" = true ]; then
  CMD="$CMD --evaluate"
fi

# Run command
echo "Running command: $CMD"
eval "$CMD"

# Generate report
echo "Generating ablation report..."
python -c "
import json
import os
import pandas as pd
from tabulate import tabulate

# Load results
output_dir = '$OUTPUT_DIR'
variants = ['full', 'combined', 'no_redundancy', 'no_graph_reasoning']
results = {}

for variant in variants:
    result_path = os.path.join(output_dir, variant, 'test_results.json')
    if os.path.exists(result_path):
        with open(result_path, 'r') as f:
            results[variant] = json.load(f)

# Create classification metrics table
classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP']
classification_data = []

for metric in classification_metrics:
    row = [metric]
    for variant in variants:
        if variant in results and metric in results[variant]:
            row.append(f\"{results[variant][metric]:.4f}\")
        else:
            row.append('N/A')
    classification_data.append(row)

# Create disentanglement metrics table
disentanglement_metrics = [
    'modality_disentanglement_score',
    'cross_modal_redundancy',
    'feature_independence',
    'shared_information_preservation',
    'mutual_information'
]
disentanglement_data = []

for metric in disentanglement_metrics:
    row = [metric]
    for variant in variants:
        if variant in results and metric in results[variant]:
            row.append(f\"{results[variant][metric]:.4f}\")
        else:
            row.append('N/A')
    disentanglement_data.append(row)

# Create refinement metrics table
refinement_metrics = [
    'text_refinement_magnitude',
    'visual_refinement_magnitude',
    'text_redundancy_effect',
    'visual_redundancy_effect',
    'refined_feature_independence',
    'independence_improvement'
]
refinement_data = []

for metric in refinement_metrics:
    row = [metric]
    for variant in variants:
        if variant in results and metric in results[variant]:
            row.append(f\"{results[variant][metric]:.4f}\")
        else:
            row.append('N/A')
    refinement_data.append(row)

# Print tables
headers = ['Metric'] + variants
print('\\nClassification Metrics:')
print(tabulate(classification_data, headers=headers, tablefmt='grid'))

print('\\nDisentanglement Metrics:')
print(tabulate(disentanglement_data, headers=headers, tablefmt='grid'))

print('\\nRefinement Metrics:')
print(tabulate(refinement_data, headers=headers, tablefmt='grid'))

# Save tables to file
with open(os.path.join(output_dir, 'ablation_report.md'), 'w') as f:
    f.write('# Ablation Experiment Results\\n\\n')

    f.write('## Classification Metrics\\n\\n')
    f.write(tabulate(classification_data, headers=headers, tablefmt='pipe'))
    f.write('\\n\\n')

    f.write('## Disentanglement Metrics\\n\\n')
    f.write(tabulate(disentanglement_data, headers=headers, tablefmt='pipe'))
    f.write('\\n\\n')

    f.write('## Refinement Metrics\\n\\n')
    f.write(tabulate(refinement_data, headers=headers, tablefmt='pipe'))
    f.write('\\n\\n')

    f.write('## Analysis\\n\\n')
    f.write('### Impact of Combined Redundancy and Graph Reasoning\\n\\n')
    f.write('The combined module integrates redundancy detection and graph reasoning into a single component. ')
    f.write('This allows us to evaluate whether these components work better together or separately.\\n\\n')

    f.write('### Impact of Redundancy Detection\\n\\n')
    f.write('Comparing the full model with the no_redundancy variant shows the impact of redundancy detection. ')
    f.write('This helps us understand how important cross-modal redundancy detection is for the overall performance.\\n\\n')

    f.write('### Impact of Graph Reasoning\\n\\n')
    f.write('Comparing the full model with the no_graph_reasoning variant shows the impact of knowledge graph reasoning. ')
    f.write('This helps us understand how important knowledge graph information is for guiding the disentanglement process.\\n\\n')

print(f\"Ablation report saved to {os.path.join(output_dir, 'ablation_report.md')}\")
"

echo "Done!"
