"""
Component Effectiveness Metrics for Multimodal Feature Disentanglement.
This module provides metrics to evaluate the effectiveness of individual components
in the multimodal feature disentanglement model.
"""

import os
import numpy as np
import torch
import sklearn.metrics
import logging
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, f1_score
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def compute_graph_reasoning_effectiveness(text_encoded, visual_encoded, kg_encoded, graph_enhanced, labels):
    """
    Compute metrics to evaluate the effectiveness of the graph reasoning component.
    
    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features
        kg_encoded (numpy.ndarray): Knowledge graph encoded features
        graph_enhanced (numpy.ndarray): Graph-enhanced features
        labels (numpy.ndarray): Ground truth labels
        
    Returns:
        dict: Dictionary of effectiveness metrics
    """
    metrics = {}
    
    try:
        # 1. Knowledge Integration Degree
        # Measure how much knowledge graph information is integrated into the enhanced features
        # Higher cosine similarity with kg_encoded indicates more knowledge integration
        kg_norm = kg_encoded / (np.linalg.norm(kg_encoded, axis=1, keepdims=True) + 1e-8)
        graph_norm = graph_enhanced / (np.linalg.norm(graph_enhanced, axis=1, keepdims=True) + 1e-8)
        knowledge_integration = np.mean(np.sum(kg_norm * graph_norm, axis=1))
        metrics['knowledge_integration_degree'] = knowledge_integration
        
        # 2. Feature Enhancement Magnitude
        # Measure how much the features change after graph reasoning
        # Compute distance between original and enhanced features
        text_change = np.mean(np.linalg.norm(graph_enhanced - text_encoded, axis=1))
        visual_change = np.mean(np.linalg.norm(graph_enhanced - visual_encoded, axis=1))
        metrics['text_enhancement_magnitude'] = text_change
        metrics['visual_enhancement_magnitude'] = visual_change
        
        # 3. Classification Improvement
        # Measure how much the graph reasoning improves classification performance
        # Split data for quick evaluation
        X_train_text, X_test_text, X_train_visual, X_test_visual, X_train_graph, X_test_graph, y_train, y_test = train_test_split(
            text_encoded, visual_encoded, graph_enhanced, labels, test_size=0.3, random_state=42
        )
        
        # Train and evaluate classifier on text features
        text_clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
        text_clf.fit(X_train_text, y_train.argmax(axis=1))
        text_preds = text_clf.predict(X_test_text)
        text_acc = accuracy_score(y_test.argmax(axis=1), text_preds)
        
        # Train and evaluate classifier on visual features
        visual_clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
        visual_clf.fit(X_train_visual, y_train.argmax(axis=1))
        visual_preds = visual_clf.predict(X_test_visual)
        visual_acc = accuracy_score(y_test.argmax(axis=1), visual_preds)
        
        # Train and evaluate classifier on graph-enhanced features
        graph_clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
        graph_clf.fit(X_train_graph, y_train.argmax(axis=1))
        graph_preds = graph_clf.predict(X_test_graph)
        graph_acc = accuracy_score(y_test.argmax(axis=1), graph_preds)
        
        # Compute improvement
        metrics['text_classification_accuracy'] = text_acc
        metrics['visual_classification_accuracy'] = visual_acc
        metrics['graph_classification_accuracy'] = graph_acc
        metrics['text_improvement'] = graph_acc - text_acc
        metrics['visual_improvement'] = graph_acc - visual_acc
        
        # 4. Semantic Consistency
        # Measure how well the graph-enhanced features preserve semantic information
        # Compute correlation between feature distances and label distances
        feature_dists = sklearn.metrics.pairwise.euclidean_distances(graph_enhanced)
        label_dists = sklearn.metrics.pairwise.euclidean_distances(labels)
        
        # Flatten the distance matrices
        n = feature_dists.shape[0]
        feature_dists_flat = feature_dists[np.triu_indices(n, k=1)]
        label_dists_flat = label_dists[np.triu_indices(n, k=1)]
        
        # Compute correlation
        semantic_consistency = np.corrcoef(feature_dists_flat, label_dists_flat)[0, 1]
        metrics['semantic_consistency'] = semantic_consistency
        
        return metrics
    
    except Exception as e:
        logger.warning(f"Error in graph reasoning effectiveness calculation: {e}")
        return {
            'knowledge_integration_degree': 0.0,
            'text_enhancement_magnitude': 0.0,
            'visual_enhancement_magnitude': 0.0,
            'text_classification_accuracy': 0.0,
            'visual_classification_accuracy': 0.0,
            'graph_classification_accuracy': 0.0,
            'text_improvement': 0.0,
            'visual_improvement': 0.0,
            'semantic_consistency': 0.0
        }

def compute_redundancy_detection_effectiveness(text_encoded, visual_encoded, redundancy_scores, labels):
    """
    Compute metrics to evaluate the effectiveness of the redundancy detection component.
    
    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features
        redundancy_scores (numpy.ndarray): Redundancy scores
        labels (numpy.ndarray): Ground truth labels
        
    Returns:
        dict: Dictionary of effectiveness metrics
    """
    metrics = {}
    
    try:
        # 1. Redundancy Distribution
        # Statistics about redundancy distribution
        metrics['redundancy_mean'] = float(np.mean(redundancy_scores))
        metrics['redundancy_std'] = float(np.std(redundancy_scores))
        metrics['redundancy_min'] = float(np.min(redundancy_scores))
        metrics['redundancy_max'] = float(np.max(redundancy_scores))
        
        # 2. Modality Similarity vs Redundancy Correlation
        # Measure correlation between modality similarity and redundancy scores
        # Compute cosine similarity between text and visual features
        text_norm = text_encoded / (np.linalg.norm(text_encoded, axis=1, keepdims=True) + 1e-8)
        visual_norm = visual_encoded / (np.linalg.norm(visual_encoded, axis=1, keepdims=True) + 1e-8)
        cosine_sim = np.sum(text_norm * visual_norm, axis=1)
        
        # Compute correlation with redundancy scores
        similarity_redundancy_corr = np.corrcoef(cosine_sim, redundancy_scores.flatten())[0, 1]
        metrics['similarity_redundancy_correlation'] = float(similarity_redundancy_corr)
        
        # 3. Label Consistency vs Redundancy Correlation
        # Measure correlation between label consistency and redundancy scores
        # Compute label consistency (samples with more labels should have more redundancy)
        label_counts = np.sum(labels, axis=1)
        
        # Compute correlation with redundancy scores
        label_redundancy_corr = np.corrcoef(label_counts, redundancy_scores.flatten())[0, 1]
        metrics['label_redundancy_correlation'] = float(label_redundancy_corr)
        
        # 4. High vs Low Redundancy Feature Separation
        # Measure separation between high and low redundancy features
        # Split samples into high and low redundancy groups
        median_redundancy = np.median(redundancy_scores)
        high_redundancy_mask = redundancy_scores.flatten() > median_redundancy
        low_redundancy_mask = ~high_redundancy_mask
        
        # Compute average feature distance within and between groups
        if np.sum(high_redundancy_mask) > 0 and np.sum(low_redundancy_mask) > 0:
            high_text = text_encoded[high_redundancy_mask]
            low_text = text_encoded[low_redundancy_mask]
            high_visual = visual_encoded[high_redundancy_mask]
            low_visual = visual_encoded[low_redundancy_mask]
            
            # Compute average distance within high redundancy group
            high_text_dists = sklearn.metrics.pairwise.euclidean_distances(high_text)
            high_visual_dists = sklearn.metrics.pairwise.euclidean_distances(high_visual)
            high_within_dist = (np.mean(high_text_dists) + np.mean(high_visual_dists)) / 2
            
            # Compute average distance within low redundancy group
            low_text_dists = sklearn.metrics.pairwise.euclidean_distances(low_text)
            low_visual_dists = sklearn.metrics.pairwise.euclidean_distances(low_visual)
            low_within_dist = (np.mean(low_text_dists) + np.mean(low_visual_dists)) / 2
            
            # Compute average distance between high and low redundancy groups
            text_between_dists = sklearn.metrics.pairwise.euclidean_distances(high_text, low_text)
            visual_between_dists = sklearn.metrics.pairwise.euclidean_distances(high_visual, low_visual)
            between_dist = (np.mean(text_between_dists) + np.mean(visual_between_dists)) / 2
            
            # Compute separation score (higher is better)
            separation_score = between_dist / ((high_within_dist + low_within_dist) / 2)
            metrics['redundancy_group_separation'] = float(separation_score)
        else:
            metrics['redundancy_group_separation'] = 0.0
        
        return metrics
    
    except Exception as e:
        logger.warning(f"Error in redundancy detection effectiveness calculation: {e}")
        return {
            'redundancy_mean': 0.0,
            'redundancy_std': 0.0,
            'redundancy_min': 0.0,
            'redundancy_max': 0.0,
            'similarity_redundancy_correlation': 0.0,
            'label_redundancy_correlation': 0.0,
            'redundancy_group_separation': 0.0
        }

def compute_adaptive_fusion_effectiveness(text_encoded, visual_encoded, kg_encoded, fused_features, redundancy_scores, labels):
    """
    Compute metrics to evaluate the effectiveness of the adaptive fusion component.
    
    Args:
        text_encoded (numpy.ndarray): Text encoded features
        visual_encoded (numpy.ndarray): Visual encoded features
        kg_encoded (numpy.ndarray): Knowledge graph encoded features
        fused_features (numpy.ndarray): Fused features
        redundancy_scores (numpy.ndarray): Redundancy scores
        labels (numpy.ndarray): Ground truth labels
        
    Returns:
        dict: Dictionary of effectiveness metrics
    """
    metrics = {}
    
    try:
        # 1. Fusion Balance
        # Measure how balanced the fusion is between text and visual features
        # Compute correlation between fused features and each modality
        text_fusion_corr = np.mean([np.corrcoef(fused_features[:, i], text_encoded[:, i])[0, 1] 
                                   for i in range(min(fused_features.shape[1], text_encoded.shape[1]))])
        visual_fusion_corr = np.mean([np.corrcoef(fused_features[:, i], visual_encoded[:, i])[0, 1] 
                                     for i in range(min(fused_features.shape[1], visual_encoded.shape[1]))])
        kg_fusion_corr = np.mean([np.corrcoef(fused_features[:, i], kg_encoded[:, i])[0, 1] 
                                 for i in range(min(fused_features.shape[1], kg_encoded.shape[1]))])
        
        metrics['text_fusion_correlation'] = float(text_fusion_corr)
        metrics['visual_fusion_correlation'] = float(visual_fusion_corr)
        metrics['kg_fusion_correlation'] = float(kg_fusion_corr)
        
        # Compute fusion balance (closer to 1 means more balanced)
        fusion_balance = 1.0 - abs(text_fusion_corr - visual_fusion_corr) / (text_fusion_corr + visual_fusion_corr + 1e-8)
        metrics['fusion_balance'] = float(fusion_balance)
        
        # 2. Redundancy-Aware Fusion
        # Measure correlation between redundancy scores and fusion weights
        # Estimate fusion weights by comparing feature distances
        text_dists = np.linalg.norm(fused_features - text_encoded, axis=1)
        visual_dists = np.linalg.norm(fused_features - visual_encoded, axis=1)
        estimated_text_weight = visual_dists / (text_dists + visual_dists + 1e-8)
        
        # Compute correlation with redundancy scores
        redundancy_fusion_corr = np.corrcoef(redundancy_scores.flatten(), estimated_text_weight)[0, 1]
        metrics['redundancy_fusion_correlation'] = float(redundancy_fusion_corr)
        
        # 3. Classification Performance Improvement
        # Measure how much the fusion improves classification performance
        # Split data for quick evaluation
        X_train_text, X_test_text, X_train_visual, X_test_visual, X_train_fused, X_test_fused, y_train, y_test = train_test_split(
            text_encoded, visual_encoded, fused_features, labels, test_size=0.3, random_state=42
        )
        
        # Train and evaluate classifier on text features
        text_clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
        text_clf.fit(X_train_text, y_train.argmax(axis=1))
        text_preds = text_clf.predict(X_test_text)
        text_acc = accuracy_score(y_test.argmax(axis=1), text_preds)
        
        # Train and evaluate classifier on visual features
        visual_clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
        visual_clf.fit(X_train_visual, y_train.argmax(axis=1))
        visual_preds = visual_clf.predict(X_test_visual)
        visual_acc = accuracy_score(y_test.argmax(axis=1), visual_preds)
        
        # Train and evaluate classifier on fused features
        fused_clf = LogisticRegression(max_iter=1000, class_weight='balanced', n_jobs=-1)
        fused_clf.fit(X_train_fused, y_train.argmax(axis=1))
        fused_preds = fused_clf.predict(X_test_fused)
        fused_acc = accuracy_score(y_test.argmax(axis=1), fused_preds)
        
        # Compute improvement
        metrics['text_classification_accuracy'] = text_acc
        metrics['visual_classification_accuracy'] = visual_acc
        metrics['fused_classification_accuracy'] = fused_acc
        metrics['text_improvement'] = fused_acc - text_acc
        metrics['visual_improvement'] = fused_acc - visual_acc
        
        return metrics
    
    except Exception as e:
        logger.warning(f"Error in adaptive fusion effectiveness calculation: {e}")
        return {
            'text_fusion_correlation': 0.0,
            'visual_fusion_correlation': 0.0,
            'kg_fusion_correlation': 0.0,
            'fusion_balance': 0.0,
            'redundancy_fusion_correlation': 0.0,
            'text_classification_accuracy': 0.0,
            'visual_classification_accuracy': 0.0,
            'fused_classification_accuracy': 0.0,
            'text_improvement': 0.0,
            'visual_improvement': 0.0
        }
