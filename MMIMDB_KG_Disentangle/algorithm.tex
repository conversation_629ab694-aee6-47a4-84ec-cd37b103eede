\begin{table}[htbp]
\centering
\caption{The algorithm of KG-HierDisNet}
\label{tab:algorithm}
\resizebox{\columnwidth}{!}{%
\begin{tabular}{@{}p{0.9\columnwidth}@{}}
\toprule
\textbf{Require:} \\
\quad Multimodal dataset $\mathcal{D} = \{(x_i^t, x_i^v, y_i)\}_{i=1}^N$, Knowledge graph $\mathcal{G} = (\mathcal{V}, \mathcal{E}, \mathcal{R}, \mathcal{H})$ \\
\quad Hyperparameters: balancing weights $\lambda$, learning rate $\alpha$, Adam parameters $\beta_1, \beta_2$ \\
\quad Initial parameters $\theta_0$ for all modules \\
\midrule
\textbf{Phase 1: Knowledge Graph Representation Learning} \\
\quad \textbf{While} not converged \textbf{do} \\
\quad \quad Compute KG losses: $\mathcal{L}_{struct}$, $\mathcal{L}_{sem}$, $\mathcal{L}_{hier}$, $\mathcal{L}_{reg}$ \\
\quad \quad Total KG loss: $\mathcal{L}_{SKGCRL} = \mathcal{L}_{struct} + \lambda_1 \mathcal{L}_{sem} + \lambda_2 \mathcal{L}_{hier} + \lambda_3 \mathcal{L}_{reg}$ \\
\quad \quad Update parameters using Adam optimizer \\
\midrule
\textbf{Phase 2: Joint Training} \\
\textbf{While} $\theta_t$ has not converged \textbf{do} \\
\quad \textbf{Forward propagation:} \\
\quad \quad \textbf{1. Feature extraction and semantic enhancement:} \\
\quad \quad \quad Extract features: $\mathbf{h}^t = \mathcal{F}_t(x^t)$, $\mathbf{h}^v = \mathcal{F}_v(x^v)$ \\
\quad \quad \quad Enhance with KG: $\mathbf{z}^t = \mathbf{h}^t + \sum_{v \in \mathcal{V}_t} \gamma_v^t \cdot \mathbf{v}$, $\mathbf{z}^v = \mathbf{h}^v + \sum_{v \in \mathcal{V}_v} \gamma_v^v \cdot \mathbf{v}$ \\
\quad \quad \textbf{2. Hierarchical disentanglement:} \\
\quad \quad \quad Compute semantic relevance matrix $\mathbf{S}_{tv}$ \\
\quad \quad \quad Extract MIR: $\mathbf{z}^{mir} = \mathbf{S}_{tv} \odot \mathbf{z}^t \odot \mathbf{z}^v$ \\
\quad \quad \quad Extract IMSR: $\mathbf{z}^{t,imsr}$, $\mathbf{z}^{v,imsr}$ \\
\quad \quad \quad Extract EMSR: $\mathbf{z}^{t,emsr}$, $\mathbf{z}^{v,emsr}$ \\
\quad \quad \textbf{3. Dynamic semantic-aware fusion:} \\
\quad \quad \quad Compute attention: $\mathbf{A} = \text{MultiHead}(\mathbf{Q}, \mathbf{K}, \mathbf{V})$ \\
\quad \quad \quad Compute fusion gates: $\mathbf{g} = \sigma_g(\mathbf{W}_g [\mathbf{A} \| \mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg}] + \mathbf{b}_g)$ \\
\quad \quad \quad Fuse representations: $\mathbf{z}^{fused} = \mathbf{g}_1 \odot \mathbf{z}^{mir} + \mathbf{g}_2 \odot \mathbf{z}^{t,emsr} + \mathbf{g}_3 \odot \mathbf{z}^{v,emsr} + \mathbf{g}_4 \odot \mathbf{z}^{kg}$ \\
\quad \quad \textbf{4. Hierarchical classification:} \\
\quad \quad \quad Generate expert predictions: $\hat{y}^{mir}$, $\hat{y}^{t,emsr}$, $\hat{y}^{v,emsr}$, $\hat{y}^{kg}$, $\hat{y}^{fused}$ \\
\quad \quad \quad Compute expert weights: $\boldsymbol{\alpha} = \text{softmax}(\mathbf{W}_{\alpha} [\mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg} \| \mathbf{z}^{fused}] + \mathbf{b}_{\alpha})$ \\
\quad \quad \quad Ensemble prediction: $\hat{y} = \sum_{i=1}^5 \alpha_i \hat{y}^i$ \\
\quad \quad \quad Apply KG correction: $\hat{y}^{corrected} = \hat{y} + \Delta \hat{y}^{kg}$ \\
\quad \textbf{Loss calculation:} \\
\quad \quad \textbf{1. Disentanglement losses:} \\
\quad \quad \quad Orthogonal constraints: $\mathcal{L}_{ortho}$ (between MIR, EMSR, IMSR) \\
\quad \quad \quad Mutual information minimization: $\mathcal{L}_{mi}$ \\
\quad \quad \quad Reconstruction loss: $\mathcal{L}_{recon}$ \\
\quad \quad \quad $\mathcal{L}_{disent} = \mathcal{L}_{ortho} + \mathcal{L}_{mi} + \mathcal{L}_{recon}$ \\
\quad \quad \textbf{2. Other losses:} \\
\quad \quad \quad Contrastive learning loss: $\mathcal{L}_{HCO}$ \\
\quad \quad \quad Classification loss: $\mathcal{L}_{cls}$ \\
\quad \quad \quad Knowledge graph consistency loss: $\mathcal{L}_{kg\_cons}$ \\
\quad \quad \textbf{3. Total loss:} \\
\quad \quad \quad $\mathcal{L}_{total} = \mathcal{L}_{cls} + \mathcal{L}_{disent} + \mathcal{L}_{HCO} + \mathcal{L}_{kg\_cons}$ \\
\quad \textbf{Optimization:} \\
\quad \quad Update parameters using Adam optimizer \\
\quad \quad Apply dynamic weight adjustment for loss components \\
\midrule
\textbf{Phase 3: Fine-tuning} \\
\quad \textbf{While} not converged \textbf{do} \\
\quad \quad Perform forward propagation as in Phase 2 \\
\quad \quad Optimize parameters focusing on classification loss $\mathcal{L}_{cls}$ \\
\midrule
\textbf{Return} $\theta_t$ \\
\bottomrule
\end{tabular}
}
\end{table}
