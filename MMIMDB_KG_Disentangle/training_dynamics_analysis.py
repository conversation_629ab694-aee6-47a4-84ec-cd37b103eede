"""
<PERSON><PERSON><PERSON> for analyzing and visualizing the impact of redundancy detection on training dynamics.
This script focuses on:
1. Comparing training curves with and without redundancy detection
2. Analyzing convergence speed and stability
3. Visualizing how redundancy detection affects loss components
4. Creating beautiful and informative visualizations
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import pandas as pd
import argparse
from matplotlib.ticker import MaxNLocator
import matplotlib.gridspec as gridspec
from matplotlib.colors import LinearSegmentedColormap

# Set up beautiful visualization style
plt.style.use('seaborn-v0_8-whitegrid')
custom_params = {
    'axes.spines.right': False,
    'axes.spines.top': False,
    'axes.grid': True,
    'grid.linestyle': '--',
    'grid.alpha': 0.7,
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif'],
    'figure.figsize': (12, 8),
    'figure.dpi': 150,
}
plt.rcParams.update(custom_params)

# Custom color palettes
main_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Training dynamics analysis for KG-Disentangle-Net")
    
    parser.add_argument('--log_dir', type=str, required=True,
                        help='Directory containing training logs')
    parser.add_argument('--output_dir', type=str, default='./output/visualizations/training_dynamics',
                        help='Output directory for saving visualizations')
    parser.add_argument('--compare_logs', type=str, nargs='+', default=None,
                        help='Paths to additional log files for comparison')
    parser.add_argument('--compare_names', type=str, nargs='+', default=None,
                        help='Names for the comparison models')
    
    return parser.parse_args()

def load_training_logs(log_path):
    """Load training logs from a file."""
    logs = []
    
    # Check if the log file is JSON or text
    if log_path.endswith('.json'):
        with open(log_path, 'r') as f:
            logs = json.load(f)
    else:
        # Parse text log file
        with open(log_path, 'r') as f:
            lines = f.readlines()
        
        # Extract metrics from each line
        for line in lines:
            if 'loss' in line.lower() and 'epoch' in line.lower():
                try:
                    # Parse metrics from the line
                    metrics = {}
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part.endswith(':') and i + 1 < len(parts):
                            key = part[:-1].lower()
                            try:
                                value = float(parts[i + 1])
                                metrics[key] = value
                            except ValueError:
                                pass
                    
                    if metrics:
                        logs.append(metrics)
                except Exception as e:
                    print(f"Error parsing line: {line}")
                    print(f"Error: {e}")
    
    return logs

def extract_metrics(logs):
    """Extract metrics from logs."""
    epochs = []
    train_loss = []
    val_loss = []
    train_f1 = []
    val_f1 = []
    redundancy_loss = []
    classification_loss = []
    
    for entry in logs:
        if 'epoch' in entry:
            epochs.append(entry['epoch'])
        elif 'step' in entry:
            epochs.append(entry['step'])
        else:
            continue
        
        if 'loss' in entry:
            train_loss.append(entry['loss'])
        elif 'train_loss' in entry:
            train_loss.append(entry['train_loss'])
        else:
            train_loss.append(None)
        
        if 'val_loss' in entry:
            val_loss.append(entry['val_loss'])
        else:
            val_loss.append(None)
        
        if 'f1' in entry:
            train_f1.append(entry['f1'])
        elif 'train_f1' in entry:
            train_f1.append(entry['train_f1'])
        else:
            train_f1.append(None)
        
        if 'val_f1' in entry:
            val_f1.append(entry['val_f1'])
        else:
            val_f1.append(None)
        
        if 'redundancy_loss' in entry:
            redundancy_loss.append(entry['redundancy_loss'])
        else:
            redundancy_loss.append(None)
        
        if 'classification_loss' in entry:
            classification_loss.append(entry['classification_loss'])
        else:
            classification_loss.append(None)
    
    return {
        'epochs': epochs,
        'train_loss': train_loss,
        'val_loss': val_loss,
        'train_f1': train_f1,
        'val_f1': val_f1,
        'redundancy_loss': redundancy_loss,
        'classification_loss': classification_loss
    }

def visualize_training_curves(metrics, output_dir, name='model'):
    """Visualize training curves."""
    # Create a figure with subplots
    fig = plt.figure(figsize=(20, 15))
    gs = gridspec.GridSpec(2, 2, figure=fig)
    
    # Loss curves
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.plot(metrics['epochs'], metrics['train_loss'], color=main_colors[0], linewidth=2, label='Train Loss')
    if any(metrics['val_loss']):
        ax1.plot(metrics['epochs'], metrics['val_loss'], color=main_colors[1], linewidth=2, label='Validation Loss')
    ax1.set_title('Loss Curves', fontsize=18, pad=20)
    ax1.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax1.set_ylabel('Loss', fontsize=14, labelpad=10)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # F1 curves
    ax2 = fig.add_subplot(gs[0, 1])
    if any(metrics['train_f1']):
        ax2.plot(metrics['epochs'], metrics['train_f1'], color=main_colors[0], linewidth=2, label='Train F1')
    if any(metrics['val_f1']):
        ax2.plot(metrics['epochs'], metrics['val_f1'], color=main_colors[1], linewidth=2, label='Validation F1')
    ax2.set_title('F1 Score Curves', fontsize=18, pad=20)
    ax2.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax2.set_ylabel('F1 Score', fontsize=14, labelpad=10)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    # Loss components
    ax3 = fig.add_subplot(gs[1, 0])
    if any(metrics['classification_loss']):
        ax3.plot(metrics['epochs'], metrics['classification_loss'], color=main_colors[2], linewidth=2, label='Classification Loss')
    if any(metrics['redundancy_loss']):
        ax3.plot(metrics['epochs'], metrics['redundancy_loss'], color=main_colors[3], linewidth=2, label='Redundancy Loss')
    ax3.set_title('Loss Components', fontsize=18, pad=20)
    ax3.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax3.set_ylabel('Loss', fontsize=14, labelpad=10)
    ax3.legend(fontsize=12)
    ax3.grid(True, alpha=0.3)
    
    # Loss ratio
    ax4 = fig.add_subplot(gs[1, 1])
    if any(metrics['classification_loss']) and any(metrics['redundancy_loss']):
        # Calculate ratio of redundancy loss to classification loss
        ratio = []
        for cl, rl in zip(metrics['classification_loss'], metrics['redundancy_loss']):
            if cl is not None and rl is not None and cl != 0:
                ratio.append(rl / cl)
            else:
                ratio.append(None)
        
        ax4.plot(metrics['epochs'], ratio, color=main_colors[4], linewidth=2)
        ax4.set_title('Redundancy Loss / Classification Loss Ratio', fontsize=18, pad=20)
        ax4.set_xlabel('Epoch', fontsize=14, labelpad=10)
        ax4.set_ylabel('Ratio', fontsize=14, labelpad=10)
        ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{name}_training_curves.png'), dpi=300, bbox_inches='tight')
    plt.close()

def visualize_convergence_comparison(metrics_list, names, output_dir):
    """Visualize convergence comparison between different models."""
    # Create a figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Loss convergence
    for i, (metrics, name) in enumerate(zip(metrics_list, names)):
        ax1.plot(metrics['epochs'], metrics['train_loss'], color=main_colors[i], linewidth=2, label=f'{name} Train Loss')
        if any(metrics['val_loss']):
            ax1.plot(metrics['epochs'], metrics['val_loss'], color=main_colors[i], linestyle='--', linewidth=2, label=f'{name} Val Loss')
    
    ax1.set_title('Loss Convergence Comparison', fontsize=18, pad=20)
    ax1.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax1.set_ylabel('Loss', fontsize=14, labelpad=10)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # F1 convergence
    for i, (metrics, name) in enumerate(zip(metrics_list, names)):
        if any(metrics['train_f1']):
            ax2.plot(metrics['epochs'], metrics['train_f1'], color=main_colors[i], linewidth=2, label=f'{name} Train F1')
        if any(metrics['val_f1']):
            ax2.plot(metrics['epochs'], metrics['val_f1'], color=main_colors[i], linestyle='--', linewidth=2, label=f'{name} Val F1')
    
    ax2.set_title('F1 Score Convergence Comparison', fontsize=18, pad=20)
    ax2.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax2.set_ylabel('F1 Score', fontsize=14, labelpad=10)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'convergence_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create a figure for loss components comparison
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Classification loss comparison
    for i, (metrics, name) in enumerate(zip(metrics_list, names)):
        if any(metrics['classification_loss']):
            ax1.plot(metrics['epochs'], metrics['classification_loss'], color=main_colors[i], linewidth=2, label=f'{name}')
    
    ax1.set_title('Classification Loss Comparison', fontsize=18, pad=20)
    ax1.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax1.set_ylabel('Loss', fontsize=14, labelpad=10)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # Redundancy loss comparison
    for i, (metrics, name) in enumerate(zip(metrics_list, names)):
        if any(metrics['redundancy_loss']):
            ax2.plot(metrics['epochs'], metrics['redundancy_loss'], color=main_colors[i], linewidth=2, label=f'{name}')
    
    ax2.set_title('Redundancy Loss Comparison', fontsize=18, pad=20)
    ax2.set_xlabel('Epoch', fontsize=14, labelpad=10)
    ax2.set_ylabel('Loss', fontsize=14, labelpad=10)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'loss_components_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

def analyze_convergence_speed(metrics_list, names, output_dir):
    """Analyze and visualize convergence speed."""
    # Define convergence thresholds
    loss_thresholds = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]
    f1_thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    
    # Calculate epochs to reach each threshold
    loss_convergence = {name: [] for name in names}
    f1_convergence = {name: [] for name in names}
    
    for i, (metrics, name) in enumerate(zip(metrics_list, names)):
        # Normalize losses to [0, 1] range for fair comparison
        if any(metrics['train_loss']):
            min_loss = min(filter(lambda x: x is not None, metrics['train_loss']))
            max_loss = max(filter(lambda x: x is not None, metrics['train_loss']))
            range_loss = max_loss - min_loss
            
            if range_loss > 0:
                normalized_loss = [(x - min_loss) / range_loss if x is not None else None for x in metrics['train_loss']]
                
                for threshold in loss_thresholds:
                    for epoch, loss in enumerate(normalized_loss):
                        if loss is not None and loss <= threshold:
                            loss_convergence[name].append((threshold, epoch))
                            break
        
        # F1 score convergence
        if any(metrics['val_f1']):
            for threshold in f1_thresholds:
                for epoch, f1 in enumerate(metrics['val_f1']):
                    if f1 is not None and f1 >= threshold:
                        f1_convergence[name].append((threshold, epoch))
                        break
    
    # Create convergence speed visualization
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Loss convergence speed
    for i, name in enumerate(names):
        if loss_convergence[name]:
            thresholds, epochs = zip(*loss_convergence[name])
            ax1.plot(thresholds, epochs, color=main_colors[i], linewidth=2, marker='o', label=name)
    
    ax1.set_title('Loss Convergence Speed', fontsize=18, pad=20)
    ax1.set_xlabel('Normalized Loss Threshold', fontsize=14, labelpad=10)
    ax1.set_ylabel('Epochs to Reach Threshold', fontsize=14, labelpad=10)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.invert_xaxis()  # Lower loss is better
    
    # F1 convergence speed
    for i, name in enumerate(names):
        if f1_convergence[name]:
            thresholds, epochs = zip(*f1_convergence[name])
            ax2.plot(thresholds, epochs, color=main_colors[i], linewidth=2, marker='o', label=name)
    
    ax2.set_title('F1 Score Convergence Speed', fontsize=18, pad=20)
    ax2.set_xlabel('F1 Score Threshold', fontsize=14, labelpad=10)
    ax2.set_ylabel('Epochs to Reach Threshold', fontsize=14, labelpad=10)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'convergence_speed.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    return loss_convergence, f1_convergence

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load main training logs
    print(f"Loading training logs from {args.log_dir}...")
    main_logs = load_training_logs(args.log_dir)
    main_metrics = extract_metrics(main_logs)
    
    # Visualize main training curves
    print("Visualizing training curves...")
    visualize_training_curves(main_metrics, args.output_dir, name='main_model')
    
    # Load comparison logs if provided
    if args.compare_logs:
        print("Loading comparison logs...")
        comparison_logs = []
        comparison_metrics = []
        
        for log_path in args.compare_logs:
            logs = load_training_logs(log_path)
            metrics = extract_metrics(logs)
            comparison_logs.append(logs)
            comparison_metrics.append(metrics)
        
        # Set comparison names
        if args.compare_names and len(args.compare_names) == len(args.compare_logs):
            comparison_names = args.compare_names
        else:
            comparison_names = [f'Model {i+1}' for i in range(len(args.compare_logs))]
        
        # Visualize comparison training curves
        print("Visualizing comparison training curves...")
        for i, (metrics, name) in enumerate(zip(comparison_metrics, comparison_names)):
            visualize_training_curves(metrics, args.output_dir, name=name)
        
        # Visualize convergence comparison
        print("Visualizing convergence comparison...")
        all_metrics = [main_metrics] + comparison_metrics
        all_names = ['Main Model'] + comparison_names
        visualize_convergence_comparison(all_metrics, all_names, args.output_dir)
        
        # Analyze convergence speed
        print("Analyzing convergence speed...")
        loss_convergence, f1_convergence = analyze_convergence_speed(all_metrics, all_names, args.output_dir)
        
        # Save convergence analysis
        convergence_analysis = {
            'loss_convergence': {name: dict(convergence) for name, convergence in loss_convergence.items()},
            'f1_convergence': {name: dict(convergence) for name, convergence in f1_convergence.items()}
        }
        
        with open(os.path.join(args.output_dir, 'convergence_analysis.json'), 'w') as f:
            json.dump(convergence_analysis, f, indent=2)
    
    print(f"Visualizations saved to {args.output_dir}")

if __name__ == '__main__':
    main()
