"""
Visualize MM-IMDB Knowledge Graph.
This script creates a visualization of the MM-IMDB knowledge graph structure.
"""

import os
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patches as mpatches

# Create output directory
output_dir = './output/mmimdb_kg_visualization'
os.makedirs(output_dir, exist_ok=True)

# Define MM-IMDB related entities
# These are representative examples from the MM-IMDB dataset
movies = [
    "The Shawshank Redemption", "The Godfather", "The Dark Knight",
    "Schindler's List", "Pulp Fiction", "Forrest Gump",
    "Fight Club", "Inception", "The Matrix"
]

genres = [
    "Drama", "Crime", "Action", "Biography", 
    "Thriller", "Comedy", "Sci-Fi", "Romance"
]

plot_keywords = [
    "prison", "mafia", "superhero", "holocaust",
    "hitman", "life journey", "mental illness", 
    "dream", "virtual reality"
]

directors = [
    "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>"
]

actors = [
    "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Christian <PERSON>le",
    "<PERSON> <PERSON>on", "<PERSON>volta", "Tom <PERSON>", 
    "Brad Pitt", "Leonardo DiCaprio", "<PERSON>anu <PERSON>"
]

# Create a NetworkX graph
G = nx.Graph()

# Add nodes with attributes
for movie in movies:
    G.add_node(movie, type='movie')

for genre in genres:
    G.add_node(genre, type='genre')

for keyword in plot_keywords:
    G.add_node(keyword, type='keyword')

for director in directors:
    G.add_node(director, type='director')

for actor in actors:
    G.add_node(actor, type='actor')

# Add edges with attributes
# Movie-Genre relations
G.add_edge("The Shawshank Redemption", "Drama", relation="has_genre")
G.add_edge("The Godfather", "Drama", relation="has_genre")
G.add_edge("The Godfather", "Crime", relation="has_genre")
G.add_edge("The Dark Knight", "Action", relation="has_genre")
G.add_edge("The Dark Knight", "Thriller", relation="has_genre")
G.add_edge("Schindler's List", "Biography", relation="has_genre")
G.add_edge("Schindler's List", "Drama", relation="has_genre")
G.add_edge("Pulp Fiction", "Crime", relation="has_genre")
G.add_edge("Pulp Fiction", "Drama", relation="has_genre")
G.add_edge("Forrest Gump", "Drama", relation="has_genre")
G.add_edge("Forrest Gump", "Comedy", relation="has_genre")
G.add_edge("Fight Club", "Drama", relation="has_genre")
G.add_edge("Inception", "Sci-Fi", relation="has_genre")
G.add_edge("Inception", "Action", relation="has_genre")
G.add_edge("The Matrix", "Sci-Fi", relation="has_genre")
G.add_edge("The Matrix", "Action", relation="has_genre")

# Movie-Keyword relations
G.add_edge("The Shawshank Redemption", "prison", relation="has_keyword")
G.add_edge("The Godfather", "mafia", relation="has_keyword")
G.add_edge("The Dark Knight", "superhero", relation="has_keyword")
G.add_edge("Schindler's List", "holocaust", relation="has_keyword")
G.add_edge("Pulp Fiction", "hitman", relation="has_keyword")
G.add_edge("Forrest Gump", "life journey", relation="has_keyword")
G.add_edge("Fight Club", "mental illness", relation="has_keyword")
G.add_edge("Inception", "dream", relation="has_keyword")
G.add_edge("The Matrix", "virtual reality", relation="has_keyword")

# Movie-Director relations
G.add_edge("The Shawshank Redemption", "Frank Darabont", relation="directed_by")
G.add_edge("The Godfather", "Francis Ford Coppola", relation="directed_by")
G.add_edge("The Dark Knight", "Christopher Nolan", relation="directed_by")
G.add_edge("Schindler's List", "Steven Spielberg", relation="directed_by")
G.add_edge("Pulp Fiction", "Quentin Tarantino", relation="directed_by")
G.add_edge("Forrest Gump", "Robert Zemeckis", relation="directed_by")
G.add_edge("Fight Club", "David Fincher", relation="directed_by")
G.add_edge("Inception", "Christopher Nolan", relation="directed_by")
G.add_edge("The Matrix", "Lana Wachowski", relation="directed_by")

# Movie-Actor relations
G.add_edge("The Shawshank Redemption", "Tim Robbins", relation="stars")
G.add_edge("The Shawshank Redemption", "Morgan Freeman", relation="stars")
G.add_edge("The Godfather", "Al Pacino", relation="stars")
G.add_edge("The Dark Knight", "Christian Bale", relation="stars")
G.add_edge("Schindler's List", "Liam Neeson", relation="stars")
G.add_edge("Pulp Fiction", "John Travolta", relation="stars")
G.add_edge("Forrest Gump", "Tom Hanks", relation="stars")
G.add_edge("Fight Club", "Brad Pitt", relation="stars")
G.add_edge("Inception", "Leonardo DiCaprio", relation="stars")
G.add_edge("The Matrix", "Keanu Reeves", relation="stars")

# Add some cross-entity relations to make the graph more interesting
# Genre-Keyword relations
G.add_edge("Drama", "mental illness", relation="associated_with")
G.add_edge("Crime", "mafia", relation="associated_with")
G.add_edge("Action", "superhero", relation="associated_with")
G.add_edge("Sci-Fi", "virtual reality", relation="associated_with")

# Director-Genre preferences
G.add_edge("Christopher Nolan", "Sci-Fi", relation="prefers_genre")
G.add_edge("Quentin Tarantino", "Crime", relation="prefers_genre")
G.add_edge("Steven Spielberg", "Drama", relation="prefers_genre")

# Actor-Genre specializations
G.add_edge("Al Pacino", "Crime", relation="specializes_in")
G.add_edge("Tom Hanks", "Drama", relation="specializes_in")
G.add_edge("Keanu Reeves", "Sci-Fi", relation="specializes_in")

# Create a custom colormap for node types
colors = ['#4287f5', '#42f56f', '#f5d442', '#f54242', '#9942f5']  # Blue, Green, Yellow, Red, Purple
node_type_cmap = LinearSegmentedColormap.from_list('node_type_cmap', colors, N=5)

# Create the visualization with a professional look
plt.figure(figsize=(20, 16))
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'DejaVu Sans']

# Define node positions using a force-directed layout
pos = nx.spring_layout(G, seed=42, k=0.3)  # k controls the distance between nodes

# Define node colors and sizes based on type
node_colors = []
node_sizes = []
for node in G.nodes:
    node_type = G.nodes[node].get('type', 'entity')
    if node_type == 'movie':
        node_colors.append(colors[0])
        node_sizes.append(800)
    elif node_type == 'genre':
        node_colors.append(colors[1])
        node_sizes.append(700)
    elif node_type == 'keyword':
        node_colors.append(colors[2])
        node_sizes.append(600)
    elif node_type == 'director':
        node_colors.append(colors[3])
        node_sizes.append(700)
    elif node_type == 'actor':
        node_colors.append(colors[4])
        node_sizes.append(700)

# Draw nodes with a black border
nx.draw_networkx_nodes(G, pos, node_size=node_sizes, node_color=node_colors, 
                      alpha=0.9, linewidths=1, edgecolors='black')

# Define edge colors based on relation type
edge_colors = []
for u, v, data in G.edges(data=True):
    relation = data.get('relation', '')
    if relation == 'has_genre':
        edge_colors.append('#1f77b4')  # Blue
    elif relation == 'has_keyword':
        edge_colors.append('#ff7f0e')  # Orange
    elif relation == 'directed_by':
        edge_colors.append('#2ca02c')  # Green
    elif relation == 'stars':
        edge_colors.append('#d62728')  # Red
    elif relation == 'associated_with':
        edge_colors.append('#9467bd')  # Purple
    elif relation == 'prefers_genre' or relation == 'specializes_in':
        edge_colors.append('#8c564b')  # Brown
    else:
        edge_colors.append('#7f7f7f')  # Gray

# Draw edges with varying widths
nx.draw_networkx_edges(G, pos, width=1.5, alpha=0.7, edge_color=edge_colors)

# Draw labels with a white background for better readability
for node, (x, y) in pos.items():
    plt.text(x, y, node, fontsize=10, ha='center', va='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8, edgecolor='none'))

# Add a title with a professional font
plt.title("MM-IMDB Knowledge Graph", fontsize=24, fontweight='bold', pad=20)

# Add a legend for node types
legend_elements = [
    mpatches.Patch(color=colors[0], label='Movie'),
    mpatches.Patch(color=colors[1], label='Genre'),
    mpatches.Patch(color=colors[2], label='Keyword'),
    mpatches.Patch(color=colors[3], label='Director'),
    mpatches.Patch(color=colors[4], label='Actor')
]
plt.legend(handles=legend_elements, loc='upper right', fontsize=12)

# Add a caption explaining the knowledge graph
plt.figtext(0.5, 0.01, 
           "Knowledge Graph for MM-IMDB Dataset: Nodes represent entities (movies, genres, keywords, directors, actors)\n"
           "and edges represent relationships between entities (has_genre, has_keyword, directed_by, stars, etc.).",
           ha='center', fontsize=12, bbox=dict(boxstyle="round,pad=0.5", facecolor='white', alpha=0.8))

# Remove axis
plt.axis('off')

# Add a subtle grid in the background
plt.grid(False)

# Save the figure with high resolution
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'mmimdb_knowledge_graph.png'), dpi=300, bbox_inches='tight')

# Create a focused visualization showing a movie and its connections
# Focus on "Inception" and its immediate connections
inception_subgraph = nx.ego_graph(G, "Inception", radius=1)

plt.figure(figsize=(16, 12))
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'Helvetica', 'DejaVu Sans']

# Define node positions for the subgraph
pos_subgraph = nx.spring_layout(inception_subgraph, seed=42, k=0.5)

# Define node colors and sizes based on type
node_colors_subgraph = []
node_sizes_subgraph = []
for node in inception_subgraph.nodes:
    node_type = G.nodes[node].get('type', 'entity')
    if node_type == 'movie':
        node_colors_subgraph.append(colors[0])
        node_sizes_subgraph.append(1200)  # Make the movie node larger
    elif node_type == 'genre':
        node_colors_subgraph.append(colors[1])
        node_sizes_subgraph.append(900)
    elif node_type == 'keyword':
        node_colors_subgraph.append(colors[2])
        node_sizes_subgraph.append(900)
    elif node_type == 'director':
        node_colors_subgraph.append(colors[3])
        node_sizes_subgraph.append(900)
    elif node_type == 'actor':
        node_colors_subgraph.append(colors[4])
        node_sizes_subgraph.append(900)

# Draw nodes with a black border
nx.draw_networkx_nodes(inception_subgraph, pos_subgraph, node_size=node_sizes_subgraph, 
                      node_color=node_colors_subgraph, alpha=0.9, linewidths=1.5, edgecolors='black')

# Define edge colors based on relation type
edge_colors_subgraph = []
for u, v, data in inception_subgraph.edges(data=True):
    relation = data.get('relation', '')
    if relation == 'has_genre':
        edge_colors_subgraph.append('#1f77b4')  # Blue
    elif relation == 'has_keyword':
        edge_colors_subgraph.append('#ff7f0e')  # Orange
    elif relation == 'directed_by':
        edge_colors_subgraph.append('#2ca02c')  # Green
    elif relation == 'stars':
        edge_colors_subgraph.append('#d62728')  # Red
    else:
        edge_colors_subgraph.append('#7f7f7f')  # Gray

# Draw edges with varying widths
nx.draw_networkx_edges(inception_subgraph, pos_subgraph, width=2.0, alpha=0.8, edge_color=edge_colors_subgraph)

# Draw edge labels (relations)
edge_labels = {(u, v): d['relation'] for u, v, d in inception_subgraph.edges(data=True)}
nx.draw_networkx_edge_labels(inception_subgraph, pos_subgraph, edge_labels=edge_labels, font_size=12)

# Draw labels with a white background for better readability
for node, (x, y) in pos_subgraph.items():
    plt.text(x, y, node, fontsize=12, ha='center', va='center',
             bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8, edgecolor='none'))

# Add a title with a professional font
plt.title("Knowledge Graph for 'Inception'", fontsize=24, fontweight='bold', pad=20)

# Add a legend for node types
plt.legend(handles=legend_elements, loc='upper right', fontsize=12)

# Remove axis
plt.axis('off')

# Save the figure with high resolution
plt.tight_layout()
plt.savefig(os.path.join(output_dir, 'inception_knowledge_graph.png'), dpi=300, bbox_inches='tight')

print(f"MM-IMDB knowledge graph visualizations saved to {output_dir}")
