#!/bin/bash

# Script to run all visualizations

# Default values
MODEL_PATH=""
OUTPUT_DIR="./output/visualizations"
DEVICE="cuda"
INTERACTIVE=false
NUM_SAMPLES=1000
SAVE_FEATURES=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --model_path)
      MODEL_PATH="$2"
      shift 2
      ;;
    --output_dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --device)
      DEVICE="$2"
      shift 2
      ;;
    --interactive)
      INTERACTIVE=true
      shift
      ;;
    --num_samples)
      NUM_SAMPLES="$2"
      shift 2
      ;;
    --save_features)
      SAVE_FEATURES=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if model path is provided
if [ -z "$MODEL_PATH" ]; then
  echo "Error: --model_path is required"
  echo "Usage: ./run_visualizations.sh --model_path <path_to_model> [--output_dir <output_dir>] [--device <cuda|cpu>] [--interactive] [--num_samples <num_samples>] [--save_features]"
  exit 1
fi

# Create output directories
FEATURES_DIR="$OUTPUT_DIR/features"
RESULTS_DIR="$OUTPUT_DIR/results"
REDUNDANCY_DIR="$OUTPUT_DIR/redundancy"
TRAINING_DIR="$OUTPUT_DIR/training"

mkdir -p "$FEATURES_DIR"
mkdir -p "$RESULTS_DIR"
mkdir -p "$REDUNDANCY_DIR"
mkdir -p "$TRAINING_DIR"

# Get experiment name from model path
EXP_NAME=$(basename $(dirname "$MODEL_PATH"))

# Get model directory
MODEL_DIR=$(dirname "$MODEL_PATH")

# Run feature visualizations
echo "Running feature visualizations..."
FEATURE_CMD="python visualize_features.py --model_path $MODEL_PATH --output_dir $FEATURES_DIR --device $DEVICE --num_samples $NUM_SAMPLES"

if [ "$INTERACTIVE" = true ]; then
  FEATURE_CMD="$FEATURE_CMD --interactive"
fi

if [ "$SAVE_FEATURES" = true ]; then
  FEATURE_CMD="$FEATURE_CMD --save_features"
fi

echo "Command: $FEATURE_CMD"
eval "$FEATURE_CMD"

# Run results visualizations
echo "Running results visualizations..."
RESULTS_CMD="python visualize_results.py --results_dir $MODEL_DIR --output_dir $RESULTS_DIR"

if [ "$INTERACTIVE" = true ]; then
  RESULTS_CMD="$RESULTS_CMD --interactive"
fi

echo "Command: $RESULTS_CMD"
eval "$RESULTS_CMD"

# Run redundancy analysis
echo "Running redundancy analysis..."
REDUNDANCY_CMD="python redundancy_analysis.py --model_path $MODEL_PATH --output_dir $REDUNDANCY_DIR --device $DEVICE"

echo "Command: $REDUNDANCY_CMD"
eval "$REDUNDANCY_CMD"

# Run training dynamics analysis if log file exists
LOG_FILE="$MODEL_DIR/train.log"
if [ -f "$LOG_FILE" ]; then
  echo "Running training dynamics analysis..."
  TRAINING_CMD="python training_dynamics_analysis.py --log_dir $LOG_FILE --output_dir $TRAINING_DIR"
  
  echo "Command: $TRAINING_CMD"
  eval "$TRAINING_CMD"
else
  echo "No training log file found at $LOG_FILE, skipping training dynamics analysis"
fi

# Run ablation study visualizations if ablation results exist
ABLATION_DIR="./output/ablation"
if [ -d "$ABLATION_DIR" ]; then
  echo "Running ablation study visualizations..."
  
  # Create ablation visualizations directory
  ABLATION_VIZ_DIR="$OUTPUT_DIR/ablation"
  mkdir -p "$ABLATION_VIZ_DIR"
  
  # Copy ablation results to visualizations directory
  cp -r "$ABLATION_DIR"/* "$ABLATION_VIZ_DIR"
  
  # Create visualizations from ablation results
  python visualize_results.py --results_dir "$ABLATION_VIZ_DIR/full_model" --output_dir "$ABLATION_VIZ_DIR/visualizations" \
    --compare_dirs "$ABLATION_VIZ_DIR/no_kg" "$ABLATION_VIZ_DIR/no_redundancy" "$ABLATION_VIZ_DIR/no_graph_reasoning" "$ABLATION_VIZ_DIR/no_adaptive_fusion" \
    --compare_names "No KG" "No Redundancy" "No Graph Reasoning" "No Adaptive Fusion"
  
  if [ "$INTERACTIVE" = true ]; then
    python visualize_results.py --results_dir "$ABLATION_VIZ_DIR/full_model" --output_dir "$ABLATION_VIZ_DIR/visualizations" \
      --compare_dirs "$ABLATION_VIZ_DIR/no_kg" "$ABLATION_VIZ_DIR/no_redundancy" "$ABLATION_VIZ_DIR/no_graph_reasoning" "$ABLATION_VIZ_DIR/no_adaptive_fusion" \
      --compare_names "No KG" "No Redundancy" "No Graph Reasoning" "No Adaptive Fusion" --interactive
  fi
else
  echo "No ablation results found at $ABLATION_DIR, skipping ablation study visualizations"
fi

echo "All visualizations completed!"
echo "Results saved to $OUTPUT_DIR"
