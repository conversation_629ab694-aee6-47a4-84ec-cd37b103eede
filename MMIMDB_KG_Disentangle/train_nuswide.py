"""
Training script for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network on NUS-WIDE dataset.
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import logging
import json
from tqdm import tqdm
import random
from datetime import datetime
from sklearn.metrics import precision_score, recall_score, f1_score, average_precision_score

from utils.nuswide_dataset import NUSWIDEDataset
from models.kg_disentangle_net import KGDisentangleNet
from enhanced_evaluate import compute_disentanglement_metrics

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def set_seed(seed):
    """Set random seed for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def train_nuswide(args):
    """
    Train the KG-Disentangle-Net model on NUS-WIDE dataset.

    Args:
        args: Command line arguments
    """
    # Set random seed
    set_seed(args.seed)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Create data loaders
    logger.info("Creating data loaders...")
    train_dataset = NUSWIDEDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='train',
        sample_ratio=args.sample_ratio
    )

    val_dataset = NUSWIDEDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='val'
    )

    test_dataset = NUSWIDEDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    logger.info(f"Train dataset size: {len(train_dataset)}")
    logger.info(f"Validation dataset size: {len(val_dataset)}")
    logger.info(f"Test dataset size: {len(test_dataset)}")

    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)

    # Define loss function and optimizer
    criterion = nn.BCEWithLogitsLoss()
    optimizer = optim.Adam(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='max', factor=0.5, patience=3)

    # Load model if testing
    if not getattr(args, 'do_train', True):
        model_path = os.path.join(args.output_dir, 'best_model.pth')
        logger.info(f"Loading model from {model_path}")
        model.load_state_dict(torch.load(model_path))
        test_metrics = evaluate(model, test_loader, criterion, device, args)
        logger.info(f"Test metrics: {test_metrics}")
        return

    # Training loop
    logger.info("Starting training...")
    best_val_f1 = 0.0
    best_epoch = 0
    patience_counter = 0
    train_metrics_history = []
    val_metrics_history = []

    for epoch in range(args.num_epochs):
        logger.info(f"Epoch {epoch+1}/{args.num_epochs}")

        # Train
        train_loss, train_metrics = train_epoch(model, train_loader, criterion, optimizer, device, args)
        logger.info(f"Train loss: {train_loss:.4f}, Train metrics: {train_metrics}")
        train_metrics_history.append({"epoch": epoch+1, "loss": train_loss, **train_metrics})

        # Validate
        val_loss, val_metrics = evaluate(model, val_loader, criterion, device, args)
        logger.info(f"Validation loss: {val_loss:.4f}, Validation metrics: {val_metrics}")
        val_metrics_history.append({"epoch": epoch+1, "loss": val_loss, **val_metrics})

        # Update learning rate
        scheduler.step(val_metrics['f1'])

        # Save best model
        if val_metrics['f1'] > best_val_f1:
            best_val_f1 = val_metrics['f1']
            best_epoch = epoch + 1
            patience_counter = 0
            logger.info(f"New best validation F1: {best_val_f1:.4f}")
            torch.save(model.state_dict(), os.path.join(args.output_dir, 'best_model.pth'))
        else:
            patience_counter += 1
            logger.info(f"No improvement for {patience_counter} epochs")

        # Save latest model
        torch.save(model.state_dict(), os.path.join(args.output_dir, 'latest_model.pth'))

        # Save metrics history
        with open(os.path.join(args.output_dir, 'train_metrics.json'), 'w') as f:
            json.dump(train_metrics_history, f, indent=2)
        with open(os.path.join(args.output_dir, 'val_metrics.json'), 'w') as f:
            json.dump(val_metrics_history, f, indent=2)

        # Early stopping
        if patience_counter >= args.patience:
            logger.info(f"Early stopping after {epoch+1} epochs")
            break

    # Load best model and evaluate on test set
    logger.info(f"Loading best model from epoch {best_epoch}")
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'best_model.pth')))
    test_loss, test_metrics = evaluate(model, test_loader, criterion, device, args)
    logger.info(f"Test loss: {test_loss:.4f}, Test metrics: {test_metrics}")

    # Save test metrics
    with open(os.path.join(args.output_dir, 'test_metrics.json'), 'w') as f:
        json.dump({"loss": test_loss, **test_metrics}, f, indent=2)

    logger.info("Training completed")

def train_epoch(model, dataloader, criterion, optimizer, device, args):
    """
    Train the model for one epoch.

    Args:
        model: The model to train
        dataloader: DataLoader for training data
        criterion: Loss function
        optimizer: Optimizer
        device: Device to use
        args: Command line arguments

    Returns:
        tuple: (average loss, metrics)
    """
    model.train()
    total_loss = 0.0
    all_preds = []
    all_labels = []

    for batch in tqdm(dataloader, desc="Training"):
        # Get batch data
        images = batch['image'].to(device)
        texts = batch['text']  # List of text strings
        labels = batch['labels'].to(device)
        kg_features = batch['kg_features'].to(device)
        label_embeddings = batch['label_embeddings'].to(device)
        kg_weights = batch['kg_weight'].to(device)

        # Forward pass
        outputs, disentanglement_loss = model(images, texts, kg_features, label_embeddings, kg_weights)

        # Calculate classification loss
        classification_loss = criterion(outputs, labels)

        # Total loss
        loss = classification_loss + disentanglement_loss

        # Backward pass and optimize
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

        total_loss += loss.item()

        # Store predictions and labels for metrics calculation
        preds = torch.sigmoid(outputs).detach().cpu().numpy()
        all_preds.append(preds)
        all_labels.append(labels.detach().cpu().numpy())

    # Calculate average loss
    avg_loss = total_loss / len(dataloader)

    # Calculate metrics
    all_preds = np.concatenate(all_preds, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    metrics = calculate_metrics(all_preds, all_labels)

    return avg_loss, metrics

def evaluate(model, dataloader, criterion, device, args):
    """
    Evaluate the model.

    Args:
        model: The model to evaluate
        dataloader: DataLoader for evaluation data
        criterion: Loss function
        device: Device to use
        args: Command line arguments

    Returns:
        tuple: (average loss, metrics)
    """
    model.eval()
    total_loss = 0.0
    all_preds = []
    all_labels = []
    all_features = {
        'text_invariant': [],
        'visual_invariant': [],
        'text_specific': [],
        'visual_specific': []
    }

    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Evaluating"):
            # Get batch data
            images = batch['image'].to(device)
            texts = batch['text']  # List of text strings
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device)
            kg_weights = batch['kg_weight'].to(device)

            # Forward pass
            outputs, disentanglement_loss, features = model(images, texts, kg_features, label_embeddings, kg_weights, return_features=True)

            # Calculate classification loss
            classification_loss = criterion(outputs, labels)

            # Total loss
            loss = classification_loss + disentanglement_loss

            total_loss += loss.item()

            # Store predictions and labels for metrics calculation
            preds = torch.sigmoid(outputs).detach().cpu().numpy()
            all_preds.append(preds)
            all_labels.append(labels.detach().cpu().numpy())

            # Store features for disentanglement metrics
            for key in all_features:
                if key in features:
                    all_features[key].append(features[key].detach().cpu().numpy())

    # Calculate average loss
    avg_loss = total_loss / len(dataloader)

    # Calculate metrics
    all_preds = np.concatenate(all_preds, axis=0)
    all_labels = np.concatenate(all_labels, axis=0)
    metrics = calculate_metrics(all_preds, all_labels)

    # Calculate disentanglement metrics
    for key in all_features:
        if all_features[key]:
            all_features[key] = np.concatenate(all_features[key], axis=0)

    if all(len(all_features[key]) > 0 for key in all_features):
        disentanglement_metrics = compute_disentanglement_metrics(
            all_features['text_invariant'],
            all_features['visual_invariant'],
            all_features['text_specific'],
            all_features['visual_specific']
        )
        metrics.update(disentanglement_metrics)

    return avg_loss, metrics

def calculate_metrics(predictions, labels, threshold=0.5):
    """
    Calculate evaluation metrics.

    Args:
        predictions: Model predictions
        labels: Ground truth labels
        threshold: Threshold for binary classification

    Returns:
        dict: Dictionary of metrics
    """
    # Apply threshold to get binary predictions
    binary_preds = (predictions > threshold).astype(int)

    # Calculate metrics
    precision_micro = precision_score(labels, binary_preds, average='micro')
    precision_macro = precision_score(labels, binary_preds, average='macro')
    recall_micro = recall_score(labels, binary_preds, average='micro')
    recall_macro = recall_score(labels, binary_preds, average='macro')
    f1_micro = f1_score(labels, binary_preds, average='micro')
    f1_macro = f1_score(labels, binary_preds, average='macro')
    
    # Calculate mAP
    mAP = average_precision_score(labels, predictions, average='macro')

    return {
        'precision_micro': float(precision_micro),
        'precision_macro': float(precision_macro),
        'recall_micro': float(recall_micro),
        'recall_macro': float(recall_macro),
        'f1_micro': float(f1_micro),
        'f1_macro': float(f1_macro),
        'f1': float(f1_micro),  # For compatibility with scheduler
        'mAP': float(mAP)
    }
