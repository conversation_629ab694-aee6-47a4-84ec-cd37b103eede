"""
Evaluation script for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.
"""

import os
import argparse
import torch
import numpy as np
from tqdm import tqdm
import json
import logging
from torch.utils.data import DataLoader
from sklearn.metrics import precision_recall_fscore_support, average_precision_score
import matplotlib.pyplot as plt
import seaborn as sns

from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
from utils.losses import compute_metrics

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Evaluate KG-Disentangle-Net on MM-IMDB")
    
    # Data arguments
    parser.add_argument('--data_path', type=str, default='/home/<USER>/workplace/dwb/data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./evaluation',
                        help='Output directory for saving evaluation results')
    
    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')
    
    # Evaluation arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')
    parser.add_argument('--split', type=str, default='test', choices=['train', 'val', 'test'],
                        help='Dataset split to evaluate on')
    parser.add_argument('--visualize', action='store_true',
                        help='Whether to generate visualizations')
    
    return parser.parse_args()

def evaluate(args):
    """Evaluate the model."""
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create dataset
    logger.info(f"Creating {args.split} dataset...")
    dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode=args.split
    )
    
    # Create data loader
    dataloader = DataLoader(
        dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)
    
    # Load model weights
    logger.info(f"Loading model from {args.model_path}...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model.eval()
    
    # Evaluate model
    logger.info("Evaluating model...")
    all_preds = []
    all_labels = []
    all_redundancy_scores = []
    all_imdb_ids = []
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc=f"Evaluating on {args.split}"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None
            imdb_ids = batch['imdb_id']
            
            # Forward pass
            outputs = model(text, image, kg_features, label_embeddings)
            logits = outputs['logits']
            
            # Get predictions and redundancy scores
            preds = torch.sigmoid(logits).detach().cpu().numpy()
            redundancy_scores = outputs['redundancy_score'].detach().cpu().numpy() if 'redundancy_score' in outputs else None
            
            # Update statistics
            all_preds.append(preds)
            all_labels.append(labels.detach().cpu().numpy())
            if redundancy_scores is not None:
                all_redundancy_scores.append(redundancy_scores)
            all_imdb_ids.extend(imdb_ids)
    
    # Concatenate predictions and labels
    all_preds = np.vstack(all_preds)
    all_labels = np.vstack(all_labels)
    if all_redundancy_scores:
        all_redundancy_scores = np.concatenate(all_redundancy_scores)
    
    # Compute metrics
    metrics = compute_metrics(all_labels, all_preds, threshold=args.threshold)
    
    # Log metrics
    logger.info(f"Evaluation Results on {args.split} split:")
    logger.info(f"F1: {metrics['f1']:.4f}")
    logger.info(f"Precision: {metrics['precision']:.4f}")
    logger.info(f"Recall: {metrics['recall']:.4f}")
    logger.info(f"mAP: {metrics['mAP']:.4f}")
    
    # Save metrics
    with open(os.path.join(args.output_dir, f'{args.split}_metrics.json'), 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Save predictions
    np.save(os.path.join(args.output_dir, f'{args.split}_preds.npy'), all_preds)
    np.save(os.path.join(args.output_dir, f'{args.split}_labels.npy'), all_labels)
    if all_redundancy_scores:
        np.save(os.path.join(args.output_dir, f'{args.split}_redundancy_scores.npy'), all_redundancy_scores)
    
    # Save predictions with movie IDs
    predictions_with_ids = []
    for i, imdb_id in enumerate(all_imdb_ids):
        predictions_with_ids.append({
            'imdb_id': imdb_id,
            'predictions': all_preds[i].tolist(),
            'labels': all_labels[i].tolist(),
            'redundancy_score': all_redundancy_scores[i].item() if all_redundancy_scores else None
        })
    
    with open(os.path.join(args.output_dir, f'{args.split}_predictions.json'), 'w') as f:
        json.dump(predictions_with_ids, f, indent=2)
    
    # Generate visualizations if requested
    if args.visualize:
        visualize_results(args, metrics, all_preds, all_labels, all_redundancy_scores, dataset.target_names)
    
    return metrics

def visualize_results(args, metrics, preds, labels, redundancy_scores, target_names):
    """Generate visualizations of the results."""
    logger.info("Generating visualizations...")
    
    # Create visualization directory
    vis_dir = os.path.join(args.output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)
    
    # Set style
    plt.style.use('seaborn-darkgrid')
    
    # 1. Per-class metrics
    plt.figure(figsize=(12, 8))
    x = np.arange(len(target_names))
    width = 0.25
    
    plt.bar(x - width, metrics['per_class_precision'], width, label='Precision')
    plt.bar(x, metrics['per_class_recall'], width, label='Recall')
    plt.bar(x + width, metrics['per_class_f1'], width, label='F1')
    
    plt.xlabel('Genre')
    plt.ylabel('Score')
    plt.title('Per-Class Metrics')
    plt.xticks(x, target_names, rotation=45, ha='right')
    plt.legend()
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'per_class_metrics.png'), dpi=300)
    plt.close()
    
    # 2. Confusion matrix (for multi-label, we use a different approach)
    # Convert predictions to binary
    binary_preds = (preds > args.threshold).astype(int)
    
    # Compute co-occurrence matrix
    co_occurrence = np.zeros((len(target_names), len(target_names)))
    for i in range(len(binary_preds)):
        for j in range(len(target_names)):
            if binary_preds[i, j] == 1:
                for k in range(len(target_names)):
                    if labels[i, k] == 1:
                        co_occurrence[j, k] += 1
    
    # Normalize by the number of true positives
    for j in range(len(target_names)):
        true_positives = np.sum(binary_preds[:, j] * labels[:, j])
        if true_positives > 0:
            co_occurrence[j, :] /= true_positives
    
    # Plot co-occurrence matrix
    plt.figure(figsize=(12, 10))
    sns.heatmap(co_occurrence, annot=True, fmt='.2f', cmap='Blues',
                xticklabels=target_names, yticklabels=target_names)
    plt.xlabel('True Genre')
    plt.ylabel('Predicted Genre')
    plt.title('Normalized Co-occurrence Matrix')
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'co_occurrence_matrix.png'), dpi=300)
    plt.close()
    
    # 3. Redundancy score distribution (if available)
    if redundancy_scores is not None:
        plt.figure(figsize=(10, 6))
        sns.histplot(redundancy_scores, kde=True)
        plt.xlabel('Redundancy Score')
        plt.ylabel('Count')
        plt.title('Distribution of Redundancy Scores')
        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, 'redundancy_distribution.png'), dpi=300)
        plt.close()
        
        # 4. Redundancy vs. Performance
        # Bin redundancy scores
        num_bins = 10
        bin_edges = np.linspace(0, 1, num_bins + 1)
        bin_indices = np.digitize(redundancy_scores, bin_edges) - 1
        bin_indices = np.clip(bin_indices, 0, num_bins - 1)
        
        # Compute metrics per bin
        bin_f1 = np.zeros(num_bins)
        bin_counts = np.zeros(num_bins)
        
        for i in range(len(redundancy_scores)):
            bin_idx = bin_indices[i]
            sample_f1 = 2 * np.sum(binary_preds[i] * labels[i]) / (np.sum(binary_preds[i]) + np.sum(labels[i]) + 1e-8)
            bin_f1[bin_idx] += sample_f1
            bin_counts[bin_idx] += 1
        
        # Normalize
        bin_f1 = np.divide(bin_f1, bin_counts, out=np.zeros_like(bin_f1), where=bin_counts > 0)
        
        # Plot
        plt.figure(figsize=(10, 6))
        bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
        plt.bar(bin_centers, bin_f1, width=(bin_edges[1] - bin_edges[0]) * 0.8)
        plt.xlabel('Redundancy Score')
        plt.ylabel('Average F1 Score')
        plt.title('Relationship Between Redundancy and Performance')
        plt.tight_layout()
        plt.savefig(os.path.join(vis_dir, 'redundancy_vs_performance.png'), dpi=300)
        plt.close()
    
    logger.info(f"Visualizations saved to {vis_dir}")

if __name__ == '__main__':
    args = parse_args()
    evaluate(args)
