# MIR-Flickr Experiment for KG-Disentangle-Net

本文档描述了如何在MIR-Flickr数据集上运行知识图谱增强的跨模态语义解缠网络(KG-Disentangle-Net)。

## 数据集

MIR-Flickr数据集是一个Web图像数据集，包含：
- 25,000张图像
- 用户生成的标签
- 38个概念标签（多标签分类）

该数据集提供了与MM-IMDB不同的领域（通用图像vs.电影），但保持了相同的多标签分类任务结构，非常适合验证我们方法的泛化能力。

## 设置

### 前提条件

- Python 3.6+
- PyTorch 1.7+
- CUDA支持的GPU（推荐）

### 目录结构

MIR-Flickr数据集应按以下方式组织：

```
/home/<USER>/workplace/dwb/data/mirflickr/
├── images/                # 图像文件
├── tags/                  # 图像标签
│   └── im*.txt
└── annotations/           # 概念标签
    └── annotation_*.txt
```

## 运行实验

### 自动设置和训练

要自动下载、准备数据集并运行实验，请使用提供的shell脚本：

```bash
chmod +x run_mirflickr_experiment.sh
./run_mirflickr_experiment.sh
```

该脚本将：
1. 下载并准备MIR-Flickr数据集（如果需要）
2. 为MIR-Flickr构建知识图谱
3. 在MIR-Flickr上训练KG-Disentangle-Net模型
4. 将结果保存到输出目录

实验将在后台运行，日志将保存到`logs/`目录。

### 手动步骤

如果您更喜欢手动运行步骤：

1. **准备数据集**：
   ```bash
   python prepare_mirflickr.py --output_dir /home/<USER>/workplace/dwb/data/mirflickr
   ```

   如果下载失败，可以创建模拟数据：
   ```bash
   python prepare_mirflickr.py --output_dir /home/<USER>/workplace/dwb/data/mirflickr --mock --num_mock_samples 1000
   ```

2. **构建知识图谱**：
   ```bash
   python run_mirflickr.py --mode build_kg --data_path /home/<USER>/workplace/dwb/data/mirflickr --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data
   ```

3. **训练模型**：
   ```bash
   python run_mirflickr.py --mode train --data_path /home/<USER>/workplace/dwb/data/mirflickr --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data --output_dir ./output_mirflickr
   ```

4. **测试模型**：
   ```bash
   python run_mirflickr.py --mode test --data_path /home/<USER>/workplace/dwb/data/mirflickr --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data --output_dir ./output_mirflickr/your_experiment_name
   ```

## 与MM-IMDB比较结果

要比较MM-IMDB和MIR-Flickr之间的结果，请使用比较脚本：

```bash
python compare_mirflickr_results.py --mmimdb_metrics ./output/your_mmimdb_experiment/test_metrics.json --mirflickr_metrics ./output_mirflickr/your_mirflickr_experiment/test_metrics.json --output_dir ./comparison_results
```

这将生成：
- 分类指标的比较图
- 解缠指标的比较图
- 包含所有指标的Markdown表格
- 包含所有指标的CSV文件

## 与MM-IMDB的主要区别

MIR-Flickr实现与MM-IMDB的区别如下：

1. **数据集结构**：
   - MIR-Flickr使用标签而不是情节摘要作为文本特征
   - MIR-Flickr有38个概念类别（相比MM-IMDB的23个类别）
   - MIR-Flickr中的图像更加多样化（通用Web图像vs.电影海报）

2. **知识图谱**：
   - 不同的实体类型（图像、标签、概念vs.电影、导演、演员、类型）
   - 不同的关系类型（图像-标签、图像-概念、标签-概念、共现）

3. **模型配置**：
   - 相同的模型架构但有38个输出类别
   - 相同的解缠方法但应用于不同领域

## 预期结果

实验应该展示：
1. 我们的方法在不同领域的有效性
2. 知识图谱增强解缠方法的泛化能力
3. MM-IMDB和MIR-Flickr之间可比较的解缠指标
4. 分类性能中的领域特定差异

## 故障排除

- 如果遇到内存问题，请尝试减小批量大小
- 如果数据集下载失败，您可能需要从MIR-Flickr网站手动下载
- 检查`logs/`目录中的日志以获取详细的错误消息
- 如果无法获取真实数据，可以使用`--mock`选项创建模拟数据进行代码验证
