"""
<PERSON><PERSON><PERSON> to download and prepare the MIR-Flickr dataset for the KG-Disentangle-Net model.
"""

import os
import argparse
import urllib.request
import zipfile
import tarfile
import shutil
import logging
from tqdm import tqdm
import numpy as np
import scipy.io as sio

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# URLs for MIR-Flickr dataset
MIRFLICKR_URL = "http://press.liacs.nl/mirflickr/mirflickr25k.v3b.zip"

class DownloadProgressBar(tqdm):
    def update_to(self, b=1, bsize=1, tsize=None):
        if tsize is not None:
            self.total = tsize
        self.update(b * bsize - self.n)

def download_url(url, output_path):
    """Download a file from a URL."""
    with DownloadProgressBar(unit='B', unit_scale=True, miniters=1, desc=url.split('/')[-1]) as t:
        urllib.request.urlretrieve(url, filename=output_path, reporthook=t.update_to)

def extract_zip(zip_path, extract_path):
    """Extract a zip file."""
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

def download_mirflickr(output_dir):
    """Download the MIR-Flickr dataset."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Download and extract
    zip_path = os.path.join(output_dir, "mirflickr25k.zip")
    
    # Download if not already downloaded
    if not os.path.exists(zip_path):
        logger.info(f"Downloading MIR-Flickr from {MIRFLICKR_URL}")
        download_url(MIRFLICKR_URL, zip_path)
    else:
        logger.info("MIR-Flickr already downloaded")
    
    # Extract if not already extracted
    images_dir = os.path.join(output_dir, 'images')
    if not os.path.exists(images_dir) or len(os.listdir(images_dir)) < 25000:
        logger.info("Extracting MIR-Flickr")
        extract_zip(zip_path, output_dir)
        
        # Move files to the correct structure
        organize_mirflickr(output_dir)
    else:
        logger.info("MIR-Flickr already extracted")

def organize_mirflickr(data_dir):
    """Organize the MIR-Flickr dataset into a standard structure."""
    logger.info("Organizing MIR-Flickr dataset")
    
    # Create directories
    os.makedirs(os.path.join(data_dir, 'images'), exist_ok=True)
    os.makedirs(os.path.join(data_dir, 'tags'), exist_ok=True)
    os.makedirs(os.path.join(data_dir, 'annotations'), exist_ok=True)
    
    # Move images
    mirflickr_dir = os.path.join(data_dir, 'mirflickr')
    if os.path.exists(mirflickr_dir):
        # Move images
        images_src = os.path.join(mirflickr_dir, 'images')
        images_dst = os.path.join(data_dir, 'images')
        
        if os.path.exists(images_src) and not os.listdir(images_dst):
            logger.info("Moving images...")
            for img_file in tqdm(os.listdir(images_src)):
                if img_file.endswith('.jpg'):
                    shutil.copy(
                        os.path.join(images_src, img_file),
                        os.path.join(images_dst, img_file)
                    )
        
        # Move tags
        tags_src = os.path.join(mirflickr_dir, 'tags')
        tags_dst = os.path.join(data_dir, 'tags')
        
        if os.path.exists(tags_src) and not os.listdir(tags_dst):
            logger.info("Moving tags...")
            for tag_file in tqdm(os.listdir(tags_src)):
                if tag_file.endswith('.txt'):
                    shutil.copy(
                        os.path.join(tags_src, tag_file),
                        os.path.join(tags_dst, tag_file)
                    )
        
        # Move annotations
        annotations_src = os.path.join(mirflickr_dir, 'annotations')
        annotations_dst = os.path.join(data_dir, 'annotations')
        
        if os.path.exists(annotations_src) and not os.listdir(annotations_dst):
            logger.info("Moving annotations...")
            for ann_file in tqdm(os.listdir(annotations_src)):
                if ann_file.endswith('.txt'):
                    shutil.copy(
                        os.path.join(annotations_src, ann_file),
                        os.path.join(annotations_dst, ann_file)
                    )
    
    # If the dataset structure is different, try to find and organize files
    else:
        logger.info("Standard MIR-Flickr structure not found, attempting to locate files...")
        
        # Look for images
        for root, dirs, files in os.walk(data_dir):
            for file in files:
                if file.endswith('.jpg'):
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join(data_dir, 'images', file)
                    if not os.path.exists(dst_path):
                        shutil.copy(src_path, dst_path)
                
                elif file.endswith('.txt') and 'tag' in root.lower():
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join(data_dir, 'tags', file)
                    if not os.path.exists(dst_path):
                        shutil.copy(src_path, dst_path)
                
                elif file.endswith('.txt') and 'annot' in root.lower():
                    src_path = os.path.join(root, file)
                    dst_path = os.path.join(data_dir, 'annotations', file)
                    if not os.path.exists(dst_path):
                        shutil.copy(src_path, dst_path)

def create_mock_data(output_dir, num_samples=1000):
    """Create mock data for testing if download fails."""
    logger.info(f"Creating mock MIR-Flickr dataset with {num_samples} samples")
    
    import random
    import numpy as np
    from PIL import Image
    
    # Create directories
    os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'tags'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'annotations'), exist_ok=True)
    
    # Create mock images
    for i in range(1, num_samples + 1):
        img_id = f'im{i}'
        img_path = os.path.join(output_dir, 'images', f'{img_id}.jpg')
        
        if not os.path.exists(img_path):
            # Create a random color image
            img = Image.new('RGB', (256, 256), color=(
                random.randint(0, 255),
                random.randint(0, 255),
                random.randint(0, 255)
            ))
            img.save(img_path)
        
        # Create mock tags
        tags_path = os.path.join(output_dir, 'tags', f'{img_id}.txt')
        if not os.path.exists(tags_path):
            # Generate random tags
            num_tags = random.randint(3, 10)
            tags = [f'tag{random.randint(1, 100)}' for _ in range(num_tags)]
            
            with open(tags_path, 'w') as f:
                f.write(' '.join(tags))
    
    # Create mock annotations (38 concepts)
    for i in range(1, 39):
        ann_path = os.path.join(output_dir, 'annotations', f'annotation_{i}.txt')
        if not os.path.exists(ann_path):
            # Generate random annotations (1 for positive, 0 for negative)
            annotations = [str(random.randint(0, 1)) for _ in range(num_samples)]
            
            with open(ann_path, 'w') as f:
                f.write('\n'.join(annotations))
    
    logger.info(f"Created mock dataset at {output_dir}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Download and prepare MIR-Flickr dataset")
    parser.add_argument('--output_dir', type=str, default='/home/<USER>/workplace/dwb/data/mirflickr',
                        help='Output directory for the dataset')
    parser.add_argument('--mock', action='store_true',
                        help='Create mock data instead of downloading')
    parser.add_argument('--num_mock_samples', type=int, default=1000,
                        help='Number of mock samples to create')
    args = parser.parse_args()
    
    if args.mock:
        # Create mock data
        create_mock_data(args.output_dir, args.num_mock_samples)
    else:
        # Download dataset
        download_mirflickr(args.output_dir)
        
        # Organize dataset
        organize_mirflickr(args.output_dir)
    
    logger.info(f"MIR-Flickr dataset prepared at {args.output_dir}")

if __name__ == '__main__':
    main()
