#!/bin/bash

# Script to run enhanced evaluation on the best model

# Default values
EXP_NAME=""
OUTPUT_DIR="./output"
DEVICE="cuda"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --exp_name)
      EXP_NAME="$2"
      shift 2
      ;;
    --output_dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --device)
      DEVICE="$2"
      shift 2
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Check if experiment name is provided
if [ -z "$EXP_NAME" ]; then
  echo "Error: --exp_name is required"
  echo "Usage: ./run_enhanced_eval.sh --exp_name <experiment_name> [--output_dir <output_dir>] [--device <cuda|cpu>]"
  exit 1
fi

# Run enhanced evaluation
echo "Running enhanced evaluation for experiment: $EXP_NAME"
python run_enhanced_eval.py --exp_name "$EXP_NAME" --output_dir "$OUTPUT_DIR" --device "$DEVICE"

# Check if the command was successful
if [ $? -eq 0 ]; then
  echo "Enhanced evaluation completed successfully"
  echo "Results saved to $OUTPUT_DIR/$EXP_NAME/enhanced_test_results.json"
else
  echo "Enhanced evaluation failed"
fi
