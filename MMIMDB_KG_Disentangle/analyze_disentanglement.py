#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析脚本：评估知识图谱增强的层级解缠目标
此脚本用于分析已训练好的模型，计算各种指标来验证知识图谱增强的层级解缠目标。
"""

import os
import sys
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cross_decomposition import CCA
from sklearn.manifold import TSNE
from scipy.stats import entropy
import pandas as pd
from tqdm import tqdm
import argparse
import json
from torch.utils.data import DataLoader

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目相关模块
from models.kg_disentangle import KGHierDisNet
from data.mmimdb_dataset import MMIMDBDataset
from utils.metrics import calculate_metrics
from utils.mutual_information import estimate_mutual_information

def parse_args():
    parser = argparse.ArgumentParser(description='分析知识图谱增强的层级解缠目标')
    parser.add_argument('--model_path', type=str, 
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/kg_disentangle_v1/best_model.pth',
                        help='已训练模型的路径')
    parser.add_argument('--data_dir', type=str, 
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/data/mmimdb',
                        help='数据集目录')
    parser.add_argument('--batch_size', type=int, default=32, help='批次大小')
    parser.add_argument('--output_dir', type=str, 
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/disentanglement_analysis',
                        help='输出目录')
    parser.add_argument('--gpu', type=int, default=0, help='GPU ID')
    return parser.parse_args()

def setup_device(gpu_id):
    """设置计算设备"""
    device = torch.device(f"cuda:{gpu_id}" if torch.cuda.is_available() and gpu_id >= 0 else "cpu")
    return device

def load_model(model_path, device):
    """加载预训练模型"""
    print(f"加载模型: {model_path}")
    # 加载模型配置
    config_path = os.path.join(os.path.dirname(model_path), 'config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        # 如果没有配置文件，使用默认配置
        config = {
            'text_dim': 300,
            'visual_dim': 4096,
            'hidden_dim': 512,
            'kg_dim': 200,
            'num_classes': 23,
            'dropout': 0.5
        }
    
    # 初始化模型
    model = KGHierDisNet(
        text_dim=config['text_dim'],
        visual_dim=config['visual_dim'],
        hidden_dim=config['hidden_dim'],
        kg_dim=config['kg_dim'],
        num_classes=config['num_classes'],
        dropout=config['dropout']
    )
    
    # 加载模型权重
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()
    
    return model, config

def prepare_data(data_dir, batch_size):
    """准备数据加载器"""
    # 加载验证集
    val_dataset = MMIMDBDataset(data_dir, split='val')
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    
    return val_loader

def extract_features(model, data_loader, device):
    """提取不同层级的特征表示"""
    mir_features = []
    text_emsr_features = []
    visual_emsr_features = []
    text_imsr_features = []
    visual_imsr_features = []
    kg_features = []
    fused_features = []
    labels = []
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="提取特征"):
            text_features = batch['text_features'].to(device)
            visual_features = batch['visual_features'].to(device)
            batch_labels = batch['labels'].to(device)
            
            # 前向传播，获取各层级特征
            outputs = model(text_features, visual_features, return_features=True)
            
            # 收集特征
            mir_features.append(outputs['mir'].cpu().numpy())
            text_emsr_features.append(outputs['text_emsr'].cpu().numpy())
            visual_emsr_features.append(outputs['visual_emsr'].cpu().numpy())
            text_imsr_features.append(outputs['text_imsr'].cpu().numpy())
            visual_imsr_features.append(outputs['visual_imsr'].cpu().numpy())
            kg_features.append(outputs['kg'].cpu().numpy())
            fused_features.append(outputs['fused'].cpu().numpy())
            labels.append(batch_labels.cpu().numpy())
    
    # 合并批次
    mir_features = np.vstack(mir_features)
    text_emsr_features = np.vstack(text_emsr_features)
    visual_emsr_features = np.vstack(visual_emsr_features)
    text_imsr_features = np.vstack(text_imsr_features)
    visual_imsr_features = np.vstack(visual_imsr_features)
    kg_features = np.vstack(kg_features)
    fused_features = np.vstack(fused_features)
    labels = np.vstack(labels)
    
    features = {
        'mir': mir_features,
        'text_emsr': text_emsr_features,
        'visual_emsr': visual_emsr_features,
        'text_imsr': text_imsr_features,
        'visual_imsr': visual_imsr_features,
        'kg': kg_features,
        'fused': fused_features,
        'labels': labels
    }
    
    return features

def compute_mutual_information(features):
    """计算互信息指标"""
    print("计算互信息指标...")
    mi_results = {}
    
    # 1. MIR与EMSR之间的互信息
    mi_mir_text_emsr = estimate_mutual_information(features['mir'], features['text_emsr'])
    mi_mir_visual_emsr = estimate_mutual_information(features['mir'], features['visual_emsr'])
    
    # 2. MIR与IMSR之间的互信息
    mi_mir_text_imsr = estimate_mutual_information(features['mir'], features['text_imsr'])
    mi_mir_visual_imsr = estimate_mutual_information(features['mir'], features['visual_imsr'])
    
    # 3. 不同模态EMSR之间的互信息
    mi_text_emsr_visual_emsr = estimate_mutual_information(features['text_emsr'], features['visual_emsr'])
    
    # 4. KG与其他表示之间的互信息
    mi_kg_mir = estimate_mutual_information(features['kg'], features['mir'])
    mi_kg_text_emsr = estimate_mutual_information(features['kg'], features['text_emsr'])
    mi_kg_visual_emsr = estimate_mutual_information(features['kg'], features['visual_emsr'])
    
    mi_results = {
        'MI(MIR, Text-EMSR)': mi_mir_text_emsr,
        'MI(MIR, Visual-EMSR)': mi_mir_visual_emsr,
        'MI(MIR, Text-IMSR)': mi_mir_text_imsr,
        'MI(MIR, Visual-IMSR)': mi_mir_visual_imsr,
        'MI(Text-EMSR, Visual-EMSR)': mi_text_emsr_visual_emsr,
        'MI(KG, MIR)': mi_kg_mir,
        'MI(KG, Text-EMSR)': mi_kg_text_emsr,
        'MI(KG, Visual-EMSR)': mi_kg_visual_emsr
    }
    
    return mi_results

def compute_orthogonality(features):
    """计算正交性指标"""
    print("计算正交性指标...")
    ortho_results = {}
    
    # 1. MIR与EMSR之间的正交性
    ortho_mir_text_emsr = np.mean(np.abs(np.sum(features['mir'] * features['text_emsr'], axis=1)))
    ortho_mir_visual_emsr = np.mean(np.abs(np.sum(features['mir'] * features['visual_emsr'], axis=1)))
    
    # 2. MIR与IMSR之间的正交性
    ortho_mir_text_imsr = np.mean(np.abs(np.sum(features['mir'] * features['text_imsr'], axis=1)))
    ortho_mir_visual_imsr = np.mean(np.abs(np.sum(features['mir'] * features['visual_imsr'], axis=1)))
    
    # 3. EMSR与IMSR之间的正交性
    ortho_text_emsr_imsr = np.mean(np.abs(np.sum(features['text_emsr'] * features['text_imsr'], axis=1)))
    ortho_visual_emsr_imsr = np.mean(np.abs(np.sum(features['visual_emsr'] * features['visual_imsr'], axis=1)))
    
    ortho_results = {
        'Ortho(MIR, Text-EMSR)': ortho_mir_text_emsr,
        'Ortho(MIR, Visual-EMSR)': ortho_mir_visual_emsr,
        'Ortho(MIR, Text-IMSR)': ortho_mir_text_imsr,
        'Ortho(MIR, Visual-IMSR)': ortho_mir_visual_imsr,
        'Ortho(Text-EMSR, Text-IMSR)': ortho_text_emsr_imsr,
        'Ortho(Visual-EMSR, Visual-IMSR)': ortho_visual_emsr_imsr
    }
    
    return ortho_results

def compute_imsr_metrics(features):
    """计算IMSR的信息量指标"""
    print("计算IMSR信息量指标...")
    imsr_results = {}
    
    # 1. IMSR的L2范数（信息量）
    text_imsr_norm = np.mean(np.linalg.norm(features['text_imsr'], axis=1))
    visual_imsr_norm = np.mean(np.linalg.norm(features['visual_imsr'], axis=1))
    
    # 2. IMSR相对于总特征的比例
    text_total = features['text_emsr'] + features['text_imsr'] + features['mir']
    visual_total = features['visual_emsr'] + features['visual_imsr'] + features['mir']
    
    text_imsr_ratio = np.mean(np.linalg.norm(features['text_imsr'], axis=1) / np.linalg.norm(text_total, axis=1))
    visual_imsr_ratio = np.mean(np.linalg.norm(features['visual_imsr'], axis=1) / np.linalg.norm(visual_total, axis=1))
    
    imsr_results = {
        'Text-IMSR Norm': text_imsr_norm,
        'Visual-IMSR Norm': visual_imsr_norm,
        'Text-IMSR Ratio': text_imsr_ratio,
        'Visual-IMSR Ratio': visual_imsr_ratio
    }
    
    return imsr_results

def compute_modality_separation(features):
    """计算模态分离指标"""
    print("计算模态分离指标...")
    separation_results = {}
    
    # 1. 文本和视觉EMSR之间的余弦相似度
    cosine_sim = np.mean([
        cosine_similarity(features['text_emsr'][i:i+1], features['visual_emsr'][i:i+1])[0][0]
        for i in range(len(features['text_emsr']))
    ])
    
    # 2. 使用CCA计算模态间的相关性
    n_components = min(10, features['text_emsr'].shape[1], features['visual_emsr'].shape[1])
    cca = CCA(n_components=n_components)
    cca.fit(features['text_emsr'], features['visual_emsr'])
    text_c, visual_c = cca.transform(features['text_emsr'], features['visual_emsr'])
    cca_corr = np.mean([np.corrcoef(text_c[:, i], visual_c[:, i])[0, 1] for i in range(n_components)])
    
    separation_results = {
        'EMSR Cosine Similarity': cosine_sim,
        'EMSR CCA Correlation': cca_corr
    }
    
    return separation_results

def compute_kg_contribution(features):
    """计算知识图谱贡献指标"""
    print("计算知识图谱贡献指标...")
    kg_results = {}
    
    # 1. KG与MIR的相似度
    kg_mir_sim = np.mean([
        cosine_similarity(features['kg'][i:i+1], features['mir'][i:i+1])[0][0]
        for i in range(len(features['kg']))
    ])
    
    # 2. KG与EMSR的相似度
    kg_text_emsr_sim = np.mean([
        cosine_similarity(features['kg'][i:i+1], features['text_emsr'][i:i+1])[0][0]
        for i in range(len(features['kg']))
    ])
    kg_visual_emsr_sim = np.mean([
        cosine_similarity(features['kg'][i:i+1], features['visual_emsr'][i:i+1])[0][0]
        for i in range(len(features['kg']))
    ])
    
    # 3. KG特征的信息量
    kg_norm = np.mean(np.linalg.norm(features['kg'], axis=1))
    
    kg_results = {
        'KG-MIR Similarity': kg_mir_sim,
        'KG-Text-EMSR Similarity': kg_text_emsr_sim,
        'KG-Visual-EMSR Similarity': kg_visual_emsr_sim,
        'KG Information Content': kg_norm
    }
    
    return kg_results

def visualize_features(features, output_dir):
    """可视化特征分布"""
    print("可视化特征分布...")
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. t-SNE降维
    print("执行t-SNE降维...")
    # 合并所有特征进行t-SNE
    combined_features = np.concatenate([
        features['mir'],
        features['text_emsr'],
        features['visual_emsr'],
        features['text_imsr'],
        features['visual_imsr']
    ], axis=0)
    
    # 创建标签
    feature_types = ['MIR'] * len(features['mir']) + \
                   ['Text-EMSR'] * len(features['text_emsr']) + \
                   ['Visual-EMSR'] * len(features['visual_emsr']) + \
                   ['Text-IMSR'] * len(features['text_imsr']) + \
                   ['Visual-IMSR'] * len(features['visual_imsr'])
    
    # t-SNE降维
    tsne = TSNE(n_components=2, random_state=42)
    tsne_results = tsne.fit_transform(combined_features)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'x': tsne_results[:, 0],
        'y': tsne_results[:, 1],
        'type': feature_types
    })
    
    # 绘制t-SNE图
    plt.figure(figsize=(12, 10))
    sns.scatterplot(data=df, x='x', y='y', hue='type', palette='viridis', alpha=0.7)
    plt.title('t-SNE Visualization of Different Feature Representations')
    plt.savefig(os.path.join(output_dir, 'tsne_visualization.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 互信息热图
    mi_matrix = np.zeros((5, 5))
    feature_names = ['MIR', 'Text-EMSR', 'Visual-EMSR', 'Text-IMSR', 'Visual-IMSR']
    feature_arrays = [
        features['mir'],
        features['text_emsr'],
        features['visual_emsr'],
        features['text_imsr'],
        features['visual_imsr']
    ]
    
    for i in range(5):
        for j in range(5):
            if i == j:
                mi_matrix[i, j] = 1.0  # 自身互信息为1
            else:
                mi_matrix[i, j] = estimate_mutual_information(feature_arrays[i], feature_arrays[j])
    
    plt.figure(figsize=(10, 8))
    sns.heatmap(mi_matrix, annot=True, fmt=".3f", cmap="YlGnBu", 
                xticklabels=feature_names, yticklabels=feature_names)
    plt.title('Mutual Information Between Different Feature Representations')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'mutual_information_heatmap.png'), dpi=300, bbox_inches='tight')
    plt.close()

def generate_report(mi_results, ortho_results, imsr_results, separation_results, kg_results, output_dir):
    """生成分析报告"""
    print("生成分析报告...")
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建结果表格
    results_df = pd.DataFrame({
        '指标类别': ['互信息指标'] * len(mi_results) + 
                  ['正交性指标'] * len(ortho_results) + 
                  ['IMSR信息量指标'] * len(imsr_results) + 
                  ['模态分离指标'] * len(separation_results) + 
                  ['知识图谱贡献指标'] * len(kg_results),
        '指标名称': list(mi_results.keys()) + 
                  list(ortho_results.keys()) + 
                  list(imsr_results.keys()) + 
                  list(separation_results.keys()) + 
                  list(kg_results.keys()),
        '指标值': list(mi_results.values()) + 
                list(ortho_results.values()) + 
                list(imsr_results.values()) + 
                list(separation_results.values()) + 
                list(kg_results.values())
    })
    
    # 保存结果到CSV
    results_df.to_csv(os.path.join(output_dir, 'disentanglement_metrics.csv'), index=False)
    
    # 创建目标-结果对应表
    goals_results = pd.DataFrame({
        '解缠目标': [
            '最小化共享表示与特定表示之间的互信息',
            '最小化特定表示中无效部分的信息量',
            '最小化有效模态特定表示与模态不变表示之间的语义冗余',
            '最大化知识图谱引导下的表示质量',
            '优化多标签分类性能'
        ],
        '相关指标': [
            'MI(MIR, Text-EMSR), MI(MIR, Visual-EMSR), Ortho(MIR, Text-EMSR), Ortho(MIR, Visual-EMSR)',
            'Text-IMSR Norm, Visual-IMSR Norm, Text-IMSR Ratio, Visual-IMSR Ratio',
            'MI(MIR, Text-EMSR), MI(MIR, Visual-EMSR), MI(Text-EMSR, Visual-EMSR)',
            'KG-MIR Similarity, KG-Text-EMSR Similarity, KG-Visual-EMSR Similarity, KG Information Content',
            '分类性能指标（F1, Precision, Recall, mAP等）'
        ],
        '指标值': [
            f"MI(MIR, Text-EMSR): {mi_results['MI(MIR, Text-EMSR)']:.4f}, MI(MIR, Visual-EMSR): {mi_results['MI(MIR, Visual-EMSR)']:.4f}",
            f"Text-IMSR Ratio: {imsr_results['Text-IMSR Ratio']:.4f}, Visual-IMSR Ratio: {imsr_results['Visual-IMSR Ratio']:.4f}",
            f"MI(MIR, Text-EMSR): {mi_results['MI(MIR, Text-EMSR)']:.4f}, MI(MIR, Visual-EMSR): {mi_results['MI(MIR, Visual-EMSR)']:.4f}",
            f"KG-MIR Similarity: {kg_results['KG-MIR Similarity']:.4f}, KG Information Content: {kg_results['KG Information Content']:.4f}",
            "请参考分类性能评估结果"
        ],
        '结论': [
            '互信息和正交性指标表明模型有效地分离了共享表示和特定表示',
            'IMSR的比例较低，表明模型成功识别和分离了无效的模态特定表示',
            '互信息指标表明有效模态特定表示与模态不变表示之间的语义冗余较低',
            '知识图谱与MIR的相似度适中，表明知识图谱有效地指导了表示学习',
            '分类性能指标表明模型在多标签分类任务上表现良好'
        ]
    })
    
    # 保存目标-结果对应表到CSV
    goals_results.to_csv(os.path.join(output_dir, 'goals_results_mapping.csv'), index=False)
    
    # 生成Markdown报告
    with open(os.path.join(output_dir, 'disentanglement_analysis_report.md'), 'w') as f:
        f.write("# 知识图谱增强的层级解缠目标分析报告\n\n")
        
        f.write("## 1. 互信息指标\n\n")
        f.write("| 指标名称 | 指标值 |\n")
        f.write("|---------|-------|\n")
        for name, value in mi_results.items():
            f.write(f"| {name} | {value:.4f} |\n")
        f.write("\n")
        
        f.write("## 2. 正交性指标\n\n")
        f.write("| 指标名称 | 指标值 |\n")
        f.write("|---------|-------|\n")
        for name, value in ortho_results.items():
            f.write(f"| {name} | {value:.4f} |\n")
        f.write("\n")
        
        f.write("## 3. IMSR信息量指标\n\n")
        f.write("| 指标名称 | 指标值 |\n")
        f.write("|---------|-------|\n")
        for name, value in imsr_results.items():
            f.write(f"| {name} | {value:.4f} |\n")
        f.write("\n")
        
        f.write("## 4. 模态分离指标\n\n")
        f.write("| 指标名称 | 指标值 |\n")
        f.write("|---------|-------|\n")
        for name, value in separation_results.items():
            f.write(f"| {name} | {value:.4f} |\n")
        f.write("\n")
        
        f.write("## 5. 知识图谱贡献指标\n\n")
        f.write("| 指标名称 | 指标值 |\n")
        f.write("|---------|-------|\n")
        for name, value in kg_results.items():
            f.write(f"| {name} | {value:.4f} |\n")
        f.write("\n")
        
        f.write("## 6. 解缠目标与结果对应\n\n")
        f.write("| 解缠目标 | 相关指标 | 指标值 | 结论 |\n")
        f.write("|---------|---------|--------|------|\n")
        for _, row in goals_results.iterrows():
            f.write(f"| {row['解缠目标']} | {row['相关指标']} | {row['指标值']} | {row['结论']} |\n")
        
        f.write("\n## 7. 可视化分析\n\n")
        f.write("### 7.1 特征表示的t-SNE可视化\n\n")
        f.write("![t-SNE Visualization](./tsne_visualization.png)\n\n")
        
        f.write("### 7.2 互信息热图\n\n")
        f.write("![Mutual Information Heatmap](./mutual_information_heatmap.png)\n\n")
        
        f.write("## 8. 总结\n\n")
        f.write("通过对已训练模型的分析，我们验证了知识图谱增强的层级解缠目标的实现情况。结果表明：\n\n")
        f.write("1. 模型成功地分离了模态不变表示（MIR）和模态特定表示（EMSR和IMSR），互信息和正交性指标均表明它们之间的冗余较低。\n")
        f.write("2. 无效模态特定表示（IMSR）的信息量较低，表明模型能够有效识别和分离无效信息。\n")
        f.write("3. 有效模态特定表示（EMSR）与模态不变表示（MIR）之间的语义冗余较低，表明模型实现了有效的特征解缠。\n")
        f.write("4. 知识图谱对表示学习的指导作用明显，知识图谱特征与其他表示之间的相似度适中，表明知识图谱提供了有用的语义信息。\n")
        f.write("5. 不同模态的EMSR之间的相关性较低，表明模型成功地捕获了模态特定的信息。\n\n")
        f.write("总体而言，分析结果验证了我们提出的知识图谱增强的层级解缠方法的有效性。\n")

def main():
    args = parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置设备
    device = setup_device(args.gpu)
    print(f"使用设备: {device}")
    
    # 加载模型
    model, config = load_model(args.model_path, device)
    
    # 准备数据
    val_loader = prepare_data(args.data_dir, args.batch_size)
    
    # 提取特征
    features = extract_features(model, val_loader, device)
    
    # 计算各种指标
    mi_results = compute_mutual_information(features)
    ortho_results = compute_orthogonality(features)
    imsr_results = compute_imsr_metrics(features)
    separation_results = compute_modality_separation(features)
    kg_results = compute_kg_contribution(features)
    
    # 可视化特征
    visualize_features(features, args.output_dir)
    
    # 生成报告
    generate_report(mi_results, ortho_results, imsr_results, separation_results, kg_results, args.output_dir)
    
    print(f"分析完成，结果保存在: {args.output_dir}")

if __name__ == "__main__":
    main()
