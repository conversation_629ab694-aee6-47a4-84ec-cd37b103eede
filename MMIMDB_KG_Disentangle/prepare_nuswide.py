"""
<PERSON><PERSON><PERSON> to download and prepare the NUS-WIDE dataset for the KG-Disentangle-Net model.
"""

import os
import argparse
import urllib.request
import zipfile
import tarfile
import shutil
import logging
from tqdm import tqdm
import numpy as np
import scipy.io as sio

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# URLs for NUS-WIDE dataset
NUSWIDE_URLS = {
    'images': 'https://lms.comp.nus.edu.sg/wp-content/uploads/2019/research/nuswide/NUS-WIDE-images.zip',
    'tags': 'https://lms.comp.nus.edu.sg/wp-content/uploads/2019/research/nuswide/NUS-WIDE-tags.zip',
    'concepts': 'https://lms.comp.nus.edu.sg/wp-content/uploads/2019/research/nuswide/Groundtruth.zip'
}

class DownloadProgressBar(tqdm):
    def update_to(self, b=1, bsize=1, tsize=None):
        if tsize is not None:
            self.total = tsize
        self.update(b * bsize - self.n)

def download_url(url, output_path):
    """Download a file from a URL."""
    with DownloadProgressBar(unit='B', unit_scale=True, miniters=1, desc=url.split('/')[-1]) as t:
        urllib.request.urlretrieve(url, filename=output_path, reporthook=t.update_to)

def extract_zip(zip_path, extract_path):
    """Extract a zip file."""
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(extract_path)

def extract_tar(tar_path, extract_path):
    """Extract a tar file."""
    with tarfile.open(tar_path, 'r:gz') as tar_ref:
        tar_ref.extractall(extract_path)

def download_nuswide(output_dir):
    """Download the NUS-WIDE dataset."""
    os.makedirs(output_dir, exist_ok=True)
    
    # Download and extract each component
    for name, url in NUSWIDE_URLS.items():
        zip_path = os.path.join(output_dir, f"{name}.zip")
        
        # Download if not already downloaded
        if not os.path.exists(zip_path):
            logger.info(f"Downloading {name} from {url}")
            download_url(url, zip_path)
        else:
            logger.info(f"{name} already downloaded")
        
        # Extract if not already extracted
        extract_path = os.path.join(output_dir, name)
        if not os.path.exists(extract_path):
            logger.info(f"Extracting {name}")
            os.makedirs(extract_path, exist_ok=True)
            extract_zip(zip_path, extract_path)
        else:
            logger.info(f"{name} already extracted")

def organize_nuswide(data_dir):
    """Organize the NUS-WIDE dataset into a standard structure."""
    logger.info("Organizing NUS-WIDE dataset")
    
    # Create directories
    os.makedirs(os.path.join(data_dir, 'images'), exist_ok=True)
    os.makedirs(os.path.join(data_dir, 'ImageList'), exist_ok=True)
    os.makedirs(os.path.join(data_dir, 'Tags'), exist_ok=True)
    os.makedirs(os.path.join(data_dir, 'AllLabels'), exist_ok=True)
    
    # Move image list
    image_list_src = os.path.join(data_dir, 'images', 'ImageList.txt')
    image_list_dst = os.path.join(data_dir, 'ImageList', 'ImageList.txt')
    if os.path.exists(image_list_src) and not os.path.exists(image_list_dst):
        shutil.copy(image_list_src, image_list_dst)
    
    # Move tags
    tags_src = os.path.join(data_dir, 'tags', 'AllTags81.txt')
    tags_dst = os.path.join(data_dir, 'Tags', 'AllTags81.txt')
    if os.path.exists(tags_src) and not os.path.exists(tags_dst):
        shutil.copy(tags_src, tags_dst)
    
    # Process concept labels
    concepts_dir = os.path.join(data_dir, 'concepts', 'Groundtruth')
    if os.path.exists(concepts_dir):
        # Get all concept files
        concept_files = [f for f in os.listdir(concepts_dir) if f.endswith('.txt')]
        
        # Read image list
        with open(image_list_dst, 'r') as f:
            image_list = [line.strip() for line in f.readlines()]
        
        # Initialize labels matrix
        num_images = len(image_list)
        num_concepts = 81
        labels = np.zeros((num_images, num_concepts), dtype=int)
        
        # Read concept labels
        for i, concept_file in enumerate(concept_files):
            if i >= num_concepts:
                break
                
            concept_path = os.path.join(concepts_dir, concept_file)
            with open(concept_path, 'r') as f:
                concept_labels = [int(line.strip()) for line in f.readlines()]
                
            if len(concept_labels) == num_images:
                labels[:, i] = concept_labels
        
        # Save labels
        labels_path = os.path.join(data_dir, 'AllLabels', 'AllLabels.txt')
        with open(labels_path, 'w') as f:
            for i in range(num_images):
                f.write(' '.join(map(str, labels[i])) + '\n')
        
        logger.info(f"Saved labels for {num_images} images with {num_concepts} concepts")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Download and prepare NUS-WIDE dataset")
    parser.add_argument('--output_dir', type=str, default='/home/<USER>/workplace/dwb/data/nuswide',
                        help='Output directory for the dataset')
    args = parser.parse_args()
    
    # Download dataset
    download_nuswide(args.output_dir)
    
    # Organize dataset
    organize_nuswide(args.output_dir)
    
    logger.info(f"NUS-WIDE dataset prepared at {args.output_dir}")

if __name__ == '__main__':
    main()
