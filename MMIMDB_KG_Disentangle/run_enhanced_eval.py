"""
<PERSON>ript to run enhanced evaluation on the best model.
"""

import os
import argparse
import logging
from enhanced_evaluate import main as enhanced_evaluate_main

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run enhanced evaluation on the best model")

    # Experiment arguments
    parser.add_argument('--exp_name', type=str, required=True,
                        help='Experiment name (folder name in output directory)')
    parser.add_argument('--output_dir', type=str, default='./output',
                        help='Output directory containing the experiment folder')

    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Evaluation arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')

    return parser.parse_args()

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Construct model path
    experiment_dir = os.path.join(args.output_dir, args.exp_name)
    model_path = os.path.join(experiment_dir, 'best_model.pth')

    # Check if model exists
    if not os.path.exists(model_path):
        logger.error(f"Model not found at {model_path}")
        return

    logger.info(f"Running enhanced evaluation on model: {model_path}")

    # Prepare arguments for enhanced_evaluate
    import sys
    sys.argv = [
        'enhanced_evaluate.py',
        f'--model_path={model_path}',
        f'--output_dir={experiment_dir}',
        f'--data_path={args.data_path}',
        f'--kg_path={args.kg_path}',
        f'--text_dim={args.text_dim}',
        f'--visual_dim={args.visual_dim}',
        f'--kg_dim={args.kg_dim}',
        f'--hidden_dim={args.hidden_dim}',
        f'--num_classes={args.num_classes}',
        f'--batch_size={args.batch_size}',
        f'--device={args.device}',
        f'--threshold={args.threshold}'
    ]

    # Run enhanced evaluation
    enhanced_evaluate_main()

    logger.info(f"Enhanced evaluation completed. Results saved to {experiment_dir}/enhanced_test_results.json")

if __name__ == '__main__':
    main()
