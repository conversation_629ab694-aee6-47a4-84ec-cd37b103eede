\documentclass[conference]{IEEEtran}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithmic}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{bm}

\begin{document}

\title{Knowledge Graph Enhanced Hierarchical Cross-Modal Semantic Disentanglement Network}

\maketitle

\section{Introduction}

One of the core challenges in multimodal learning is the phenomenon of Cross-Modal Semantic Entanglement. Through in-depth research, we have discovered that cross-modal semantic entanglement is a more complex hierarchical structural problem: in the hierarchical decoupling process of Modality-Invariant Representations (MIR), Effective Modality-Specific Representations (EMSR), and Invalid Modality-Specific Representations (IMSR), there remains semantic redundancy between EMSR and MIR that is not dynamically perceived. This phenomenon manifests as feature components in different modality-specific effective representation spaces that are highly coupled with modality-invariant semantics. Such entanglement severely constrains the model's unified semantic understanding of multimodal data and reduces the model's accuracy in capturing cross-modal consistent semantics.

Traditional multimodal fusion methods typically adopt simple feature concatenation or weighted averaging strategies, which implicitly assume that different modal features are independent or can be linearly combined, completely ignoring the complex semantic interactions and entanglement phenomena between modalities. Some recent works have attempted to separate modality-invariant and modality-specific representations through adversarial learning or variational inference, but these methods often lack explicit modeling of semantic structures and cannot effectively handle hierarchical semantic entanglement problems.

This paper proposes a Knowledge Graph Enhanced Hierarchical Cross-Modal Semantic Disentanglement Network (KG-HierDisNet), which for the first time combines external knowledge structures with a hierarchical semantic disentanglement framework, introducing structured knowledge to guide the fine-grained disentanglement process of multimodal features. Unlike existing methods, we utilize the semantic topological structure of knowledge graphs to provide fine-grained semantic guidance, helping the model identify and separate MIR, EMSR, and IMSR, thereby achieving more precise feature disentanglement and semantically-aware multimodal fusion.

\section{Problem Definition}

\subsection{Problem Formulation}

\subsubsection{Formal Definition of Multimodal Multi-label Classification}

Given a multimodal dataset $\mathcal{D} = \{(x_i^t, x_i^v, y_i)\}_{i=1}^N$, where $x_i^t \in\mathbb{R}^{d_t}$ represents the text feature of the $i$-th sample, $x_i^v \in\mathbb{R}^{d_v}$ represents the corresponding visual feature, and $y_i \in \{0,1\}^C$ is a multi-hot vector indicating which categories the sample belongs to. Our goal is to learn a mapping function $f: \mathbb{R}^{d_t} \times\mathbb{R}^{d_v} \rightarrow \{0,1\}^C$, such that the predicted label $\hat{y}_i = f(x_i^t, x_i^v)$ is as close as possible to the true label $y_i$.

From a probabilistic perspective, this problem can be formulated as maximizing the conditional probability $P(y_i|x_i^t, x_i^v)$, i.e., the probability of correctly predicting the label given multimodal input. For multi-label classification, we need to model the conditional probability of each category label:

\begin{equation}
P(y_i|x_i^t, x_i^v) = \prod_{c=1}^C P(y_{i,c}|x_i^t, x_i^v)
\end{equation}

where $y_{i,c}$ is the $c$-th element of $y_i$, indicating whether sample $i$ belongs to category $c$, assuming conditional independence between categories.

\subsubsection{Definition of Cross-Modal Semantic Entanglement}

In multimodal learning, we aim to learn disentangled representation spaces, decomposing the features of each modality into modality-shared and modality-specific parts:

\begin{equation}
z_i^t = [z_i^{t,s}, z_i^{t,p}]
\end{equation}
\begin{equation}
z_i^v = [z_i^{v,s}, z_i^{v,p}]
\end{equation}

where $z_i^{t,s}, z_i^{v,s} \in\mathbb{R}^{d_s}$ represent the shared representations (modality-invariant representations) extracted from text and visual modalities, and $z_i^{t,p} \in\mathbb{R}^{d_{tp}}$ and $z_i^{v,p} \in\mathbb{R}^{d_{vp}}$ represent modality-specific representations.

In actual multimodal representation learning, we observe a key challenge: cross-modal semantic entanglement. This phenomenon refers to the semantic redundancy between effective modality-specific representations and modality-invariant representations that remains undetected during the hierarchical decoupling process of Modality-Invariant Representations (MIR), Effective Modality-Specific Representations (EMSR), and Invalid Modality-Specific Representations (IMSR). This entanglement manifests as feature components in different modality-specific effective representation spaces that are highly coupled with modality-invariant semantics.

Formally, we can further decompose modality-specific representations into effective and invalid parts:

\begin{equation}
z_{i}^{t,p}=z_{i}^{t,p,eff}+z_{i}^{t,p,inv}
\end{equation}
\begin{equation}
z_{i}^{v,p}=z_{i}^{v,p,eff}+z_{i}^{v,p,inv}
\end{equation}

where $z_{i}^{t,p,eff}$ and $z_{i}^{v,p,eff}$ are effective representations that truly contain modality-specific information, while $z_{i}^{t,p,inv}$ and $z_{i}^{v,p,inv}$ are invalid representations that have semantic overlap with modality-invariant representations.

Cross-modal semantic entanglement also involves semantic redundancy between effective modality-specific representations and modality-invariant representations, which can be formally represented as:

\begin{equation}
I(z_{i}^{t,p,eff};z_i^{t,s}) \text{ and } I(z_{i}^{v,p,eff};z_i^{v,s})
\end{equation}

The core of the cross-modal semantic entanglement problem is how to minimize the semantic redundancy between effective modality-specific representations and modality-invariant representations, while reducing the influence of invalid modality-specific representations.

\subsubsection{Knowledge Graph Enhanced Hierarchical Disentanglement Objectives}

From an information theory perspective, our knowledge graph enhanced hierarchical disentanglement task should satisfy the following conditions:

\begin{enumerate}
\item \textbf{Minimize mutual information between shared and specific representations}: $I(z_i^{t,s};z_i^{t,p})$ and $I(z_i^{v,s};z_i^{v,p})$ should be minimized to ensure orthogonality of disentanglement, directly corresponding to reducing cross-modal semantic entanglement.

\item \textbf{Minimize information content in the invalid part of specific representations}: $I(z_i^{t,p,inv};z_i^{t,s})$ and $I(z_i^{v,p,inv};z_i^{v,s})$ should approach zero.

\item \textbf{Minimize semantic redundancy between effective modality-specific representations and modality-invariant representations}: $I(z_{i}^{t,p,eff};z_i^{t,s})$ and $I(z_{i}^{v,p,eff};z_i^{v,s})$ should approach zero.

\item \textbf{Maximize representation quality guided by knowledge graphs}: Introduce knowledge graph $\mathcal{G} = (\mathcal{V}, \mathcal{E}, \mathcal{R}, \mathcal{H})$ as external semantic prior, maximize $I(z_i^{t,s}, z_i^{v,s}; \mathcal{G})$ to enhance semantic consistency of modality-invariant representations, while minimizing $I(z_i^{t,p,eff}, z_i^{v,p,eff}; \mathcal{G})$ to ensure uniqueness of modality-specific representations.

\item \textbf{Optimize multi-label classification performance}: While ensuring disentanglement quality, maximize conditional probability $P(y_i|z_i^{t,s}, z_i^{v,s}, z_i^{t,p,eff}, z_i^{v,p,eff}, \mathcal{G})$ to improve multi-label classification accuracy.
\end{enumerate}

Our KG-HierDisNet approximates these objectives through specific network architectures and loss functions, with special focus on how to reduce cross-modal semantic entanglement through knowledge graph enhanced dynamic awareness mechanisms while improving multi-label classification performance.

\section{Method Overview and Theoretical Framework}

The core concept of KG-HierDisNet is to construct a hierarchical semantic disentanglement framework, using the topological structure and semantic associations of knowledge graphs to guide the fine-grained disentanglement process of multimodal features. Our method is based on the following theoretical assumptions:

\textbf{Assumption 1}: There are three hierarchical levels of semantic representations in multimodal data: Modality-Invariant Representations (MIR), Effective Modality-Specific Representations (EMSR), and Invalid Modality-Specific Representations (IMSR).

\textbf{Assumption 2}: The entity and relationship structures in knowledge graphs can serve as external semantic priors, guiding the model to identify and separate these three hierarchical levels of semantic representations.

\textbf{Assumption 3}: Cross-modal semantic entanglement mainly occurs between MIR and EMSR, manifesting as semantic redundancy and information overlap.

Based on these assumptions, we propose an end-to-end hierarchical disentanglement framework that includes the following core modules:

\begin{enumerate}
\item \textbf{Semantic-Enhanced Knowledge Graph Construction and Representation Learning (SKGCRL)}: Construct a semantically enhanced domain knowledge graph from multimodal data, and generate semantic embedding representations of entities and relationships through complex relationship-aware graph representation learning methods.

\item \textbf{Knowledge-Guided Hierarchical Semantic Disentanglement (KGHSD)}: Utilize the semantic structure of knowledge graphs to guide the hierarchical disentanglement of multimodal features, precisely separating MIR, EMSR, and IMSR, and ensuring the independence of representations through multi-level orthogonal constraints and mutual information minimization.

\item \textbf{Dynamic Semantic-Aware Fusion (DSAF)}: Based on knowledge graph structure and hierarchical disentangled representations, dynamically perceive the semantic importance of different modalities and different hierarchical representations, adaptively fuse these representations, and minimize semantic redundancy.

\item \textbf{Hierarchical Contrastive Optimization (HCO)}: Through multi-granularity contrastive learning objectives, optimize the semantic consistency and discriminability of hierarchical disentangled representations, further enhancing the model's understanding of cross-modal semantics.
\end{enumerate}

Figure 1 shows the overall architecture of KG-HierDisNet and the interactions between various modules.

\section{Semantic-Enhanced Knowledge Graph Construction and Representation Learning (SKGCRL)}

\subsection{Multi-level Semantic Knowledge Graph Construction}

Given a multimodal dataset $\mathcal{D} = \{(x_i^t, x_i^v, y_i)\}_{i=1}^N$, where $x_i^t \in \mathbb{R}^{d_t}$ and $x_i^v \in \mathbb{R}^{d_v}$ represent the text and visual features of the $i$-th sample, respectively, and $y_i \in \{0,1\}^C$ represents the corresponding multi-label vector. We first construct a multi-level semantic knowledge graph $\mathcal{G} = (\mathcal{V}, \mathcal{E}, \mathcal{R}, \mathcal{H})$, where $\mathcal{V}$ is the set of entities, $\mathcal{E}$ is the set of edges, $\mathcal{R}$ is the set of relationship types, and $\mathcal{H}$ is the set of hierarchical structures.

Unlike traditional knowledge graphs, our multi-level semantic knowledge graph includes the following innovative designs:

\begin{enumerate}
\item \textbf{Multi-granularity Entity Hierarchy}: Entities are divided into three levels according to semantic granularity:
   \begin{itemize}
   \item Level 1 entities ($\mathcal{V}_1$): Basic semantic units, such as movies ($v_m$), directors ($v_d$), actors ($v_a$), categories ($v_g$)
   \item Level 2 entities ($\mathcal{V}_2$): Compound semantic units, such as movie-director pairs ($v_{md}$), movie-category pairs ($v_{mg}$)
   \item Level 3 entities ($\mathcal{V}_3$): Higher-order semantic units, such as movie-director-category triplets ($v_{mdg}$)
   \end{itemize}

\item \textbf{Semantically Enhanced Relationship Types}: Relationship types are divided into four categories according to semantic function:
   \begin{itemize}
   \item Structural relationships ($\mathcal{R}_s$): Describe structural connections between entities, such as movie-director ($r_{md}$), movie-actor ($r_{ma}$)
   \item Attribute relationships ($\mathcal{R}_a$): Describe attribute features of entities, such as movie-category ($r_{mg}$), director-style ($r_{ds}$)
   \item Semantic relationships ($\mathcal{R}_e$): Describe semantic connections between entities, such as director-category preference ($r_{dg}$), actor-category association ($r_{ag}$)
   \item Hierarchical relationships ($\mathcal{R}_h$): Describe inclusion relationships between entities at different levels, such as $v_{md}$ containing $v_m$ and $v_d$
   \end{itemize}
\end{enumerate}

Formally, the multi-level semantic knowledge graph can be represented as a set of quadruples $(h, r, t, l)$, where $h, t \in \mathcal{V}$ are the head and tail entities, respectively, $r \in \mathcal{R}$ is the relationship type, and $l \in \{1,2,3\}$ indicates the level to which the relationship belongs. For example, the quadruple $(v_m, r_{mg}, v_g, 1)$ indicates that at level 1, movie $v_m$ belongs to category $v_g$.

\subsection{Complex Relationship-Aware Graph Representation Learning}

To capture the complex relationships and hierarchical structures in multi-level semantic knowledge graphs, we propose a complex relationship-aware graph representation learning method that combines relationship projection, hierarchical attention, and semantic preservation constraints.

\subsubsection{Relation-Specific Projection Transformation}

Unlike the traditional TransE model, we define a specific projection transformation for each relationship type to capture the semantic characteristics of the relationship:

\begin{equation}
\phi_r(h) = W_r \mathbf{h} + b_r
\end{equation}

where $W_r \in \mathbb{R}^{d \times d}$ and $b_r \in \mathbb{R}^d$ are relation-specific projection parameters, and $\mathbf{h} \in \mathbb{R}^d$ is the embedding vector of the head entity.

Based on this, we define the scoring function for relation triplets as:

\begin{equation}
s(h, r, t) = -\|\phi_r(h) - \mathbf{t}\|_2^2 - \lambda_r \cdot \text{KL}(\mathcal{N}(\phi_r(h), \Sigma_r) \| \mathcal{N}(\mathbf{t}, \Sigma_t))
\end{equation}

where $\lambda_r$ is a relation-specific weight parameter, and the second term is the KL divergence between two Gaussian distributions, used to capture the uncertainty and semantic distribution of entity representations.

\subsubsection{Hierarchy-Aware Multi-Relation Aggregation}

To integrate information from different levels and different types of relationships, we design a hierarchy-aware multi-relation aggregation mechanism:

\begin{equation}
\mathbf{v}_i = \sum_{l=1}^3 \alpha_l \cdot \sum_{j \in \mathcal{N}_i^l} \sum_{r \in \mathcal{R}_{ij}} \beta_{ijr} \cdot \phi_r(\mathbf{v}_j)
\end{equation}

where $\mathcal{N}_i^l$ is the set of neighbors of entity $i$ at level $l$, $\mathcal{R}_{ij}$ is the set of relationships between entities $i$ and $j$, $\alpha_l$ is the importance weight of level $l$, and $\beta_{ijr}$ is the importance weight of relationship $r$ when connecting entities $i$ and $j$.

The level weights $\alpha_l$ and relationship weights $\beta_{ijr}$ are calculated through attention mechanisms:

\begin{equation}
\alpha_l = \frac{\exp(w_l^T \tanh(W_l \bar{\mathbf{v}}^l + b_l))}{\sum_{l'=1}^3 \exp(w_{l'}^T \tanh(W_{l'} \bar{\mathbf{v}}^{l'} + b_{l'}))}
\end{equation}

\begin{equation}
\beta_{ijr} = \frac{\exp(a_r^T \tanh(A_r [\mathbf{v}_i \| \mathbf{v}_j \| \mathbf{r}] + c_r))}{\sum_{r' \in \mathcal{R}_{ij}} \exp(a_{r'}^T \tanh(A_{r'} [\mathbf{v}_i \| \mathbf{v}_j \| \mathbf{r'}] + c_{r'}))}
\end{equation}

where $\bar{\mathbf{v}}^l$ is the average of all entity embeddings at level $l$, and $\|$ denotes vector concatenation operation.

\subsubsection{Semantic Preservation Optimization Objective}

To learn high-quality knowledge graph representations, we design a multi-objective optimization function:

\begin{equation}
\mathcal{L}_{SKGCRL} = \mathcal{L}_{struct} + \lambda_1 \mathcal{L}_{sem} + \lambda_2 \mathcal{L}_{hier} + \lambda_3 \mathcal{L}_{reg}
\end{equation}

where the various loss terms are defined as follows:

\begin{itemize}
\item Structure preservation loss:
\begin{equation}
\mathcal{L}_{struct} = \sum_{(h,r,t,l) \in \mathcal{G}} \sum_{(h',r,t',l) \in \mathcal{G}'} [\gamma_l + s(h', r, t', l) - s(h, r, t, l)]_+
\end{equation}

\item Semantic preservation loss:
\begin{equation}
\mathcal{L}_{sem} = \sum_{v \in \mathcal{V}} \|f_{sem}(\mathbf{v}) - \mathbf{s}_v\|_2^2
\end{equation}
where $f_{sem}$ is a semantic mapping function, and $\mathbf{s}_v$ is the predefined semantic vector of entity $v$ (e.g., extracted from a pre-trained language model).

\item Hierarchical consistency loss:
\begin{equation}
\mathcal{L}_{hier} = \sum_{(v_i, r_h, v_j) \in \mathcal{E}_h} \|f_{hier}(\mathbf{v}_i) - \mathbf{v}_j\|_2^2
\end{equation}
where $\mathcal{E}_h$ is the set of hierarchical relationship edges, and $f_{hier}$ is a hierarchical mapping function.

\item Regularization loss:
\begin{equation}
\mathcal{L}_{reg} = \sum_{v \in \mathcal{V}} \|\mathbf{v}\|_2^2 + \sum_{r \in \mathcal{R}} \|\mathbf{r}\|_2^2 + \sum_{r \in \mathcal{R}} (\|W_r\|_F^2 + \|b_r\|_2^2)
\end{equation}
\end{itemize}

By minimizing $\mathcal{L}_{SKGCRL}$, we can obtain a semantically enhanced embedding vector $\mathbf{v} \in \mathbb{R}^d$ for each entity $v \in \mathcal{V}$ and an embedding vector $\mathbf{r} \in \mathbb{R}^d$ for each relationship $r \in \mathcal{R}$. These embedding vectors not only capture the semantic information of entities and relationships but also preserve the structural characteristics of the multi-level knowledge graph.

\section{Knowledge-Guided Hierarchical Semantic Disentanglement (KGHSD)}

\subsection{Multimodal Feature Extraction and Semantic Enhancement}

Given text features $x^t \in \mathbb{R}^{d_t}$ and visual features $x^v \in \mathbb{R}^{d_v}$, we first map them to a high-dimensional semantic space through a series of semantically enhanced feature extractors:

\begin{equation}
\mathbf{h}^t = \mathcal{F}_t(x^t; \Theta_t) = \text{MSA}_t(\text{LN}(\text{FFN}_t(x^t))) + \text{Res}_t(x^t)
\end{equation}
\begin{equation}
\mathbf{h}^v = \mathcal{F}_v(x^v; \Theta_v) = \text{MSA}_v(\text{LN}(\text{FFN}_v(x^v))) + \text{Res}_v(x^v)
\end{equation}

where $\mathcal{F}_t$ and $\mathcal{F}_v$ are feature extractors for text and vision, respectively, $\text{MSA}$ represents multi-head self-attention mechanism, $\text{LN}$ represents layer normalization, $\text{FFN}$ represents feed-forward neural network, $\text{Res}$ represents residual connection, $\Theta_t$ and $\Theta_v$ are the corresponding parameter sets, and $\mathbf{h}^t \in \mathbb{R}^{d_h}$ and $\mathbf{h}^v \in \mathbb{R}^{d_h}$ are the extracted high-dimensional features.

To further enhance the semantic expressiveness of the features, we introduce a knowledge graph-guided semantic enhancement mechanism:

\begin{equation}
\mathbf{z}^t = \mathcal{E}_t(\mathbf{h}^t, \mathcal{G}; \Phi_t) = \mathbf{h}^t + \sum_{v \in \mathcal{V}_t} \gamma_v^t \cdot \mathbf{v}
\end{equation}
\begin{equation}
\mathbf{z}^v = \mathcal{E}_v(\mathbf{h}^v, \mathcal{G}; \Phi_v) = \mathbf{h}^v + \sum_{v \in \mathcal{V}_v} \gamma_v^v \cdot \mathbf{v}
\end{equation}

where $\mathcal{E}_t$ and $\mathcal{E}_v$ are knowledge enhancement functions, $\mathcal{V}_t$ and $\mathcal{V}_v$ are the sets of knowledge graph entities related to text and visual features, respectively, $\mathbf{v}$ is the embedding vector of entity $v$, and $\gamma_v^t$ and $\gamma_v^v$ are semantic relevance weights, calculated through an attention mechanism:

\begin{equation}
\gamma_v^m = \frac{\exp(\psi_m(\mathbf{h}^m, \mathbf{v}))}{\sum_{v' \in \mathcal{V}_m} \exp(\psi_m(\mathbf{h}^m, \mathbf{v}'))}
\end{equation}

where $m \in \{t, v\}$ represents the modality, and $\psi_m$ is a modality-specific relevance function:

\begin{equation}
\psi_m(\mathbf{h}^m, \mathbf{v}) = \frac{(\mathbf{W}_m^Q \mathbf{h}^m)^T (\mathbf{W}_m^K \mathbf{v})}{\sqrt{d_k}} + \mathbf{b}_m^T \tanh(\mathbf{W}_m^G [\mathbf{h}^m \| \mathbf{v}])
\end{equation}

where $\mathbf{W}_m^Q, \mathbf{W}_m^K, \mathbf{W}_m^G$ and $\mathbf{b}_m$ are learnable parameters, and $d_k$ is a scaling factor.

\subsection{Knowledge Graph Guided Hierarchical Disentanglement}

To achieve precise hierarchical semantic disentanglement, we design a knowledge graph guided hierarchical disentanglement framework that can separate Modality-Invariant Representations (MIR), Effective Modality-Specific Representations (EMSR), and Invalid Modality-Specific Representations (IMSR).

\subsubsection{Semantic Relevance Analysis}

First, we compute a semantic relevance matrix between different modal features through the knowledge graph:

\begin{equation}
\mathbf{S}_{tv} = \mathcal{R}(\mathbf{z}^t, \mathbf{z}^v, \mathcal{G}) = \sigma\left(\frac{\mathbf{z}^t (\mathbf{z}^v)^T}{\sqrt{d_h}} + \sum_{(v_i, r, v_j) \in \mathcal{G}_{tv}} \omega_{ij} \cdot \mathbf{M}_{ij}\right)
\end{equation}

where $\mathcal{G}_{tv}$ is a knowledge graph subgraph connecting text and visual semantics, $\omega_{ij}$ is the importance weight of relationship $(v_i, r, v_j)$, $\mathbf{M}_{ij} \in \mathbb{R}^{d_h \times d_h}$ is a semantic mapping matrix, and $\sigma$ is a normalization function.

\subsubsection{Hierarchical Representation Decomposition}

Based on the semantic relevance matrix, we decompose the modal features into three hierarchical representations:

1. \textbf{Modality-Invariant Representations (MIR)}:

\begin{equation}
\mathbf{z}^{mir} = \mathcal{D}_{mir}(\mathbf{z}^t, \mathbf{z}^v, \mathbf{S}_{tv}) = \mathbf{S}_{tv} \odot \mathbf{z}^t \odot \mathbf{z}^v
\end{equation}

2. \textbf{Effective Modality-Specific Representations (EMSR)}:

\begin{equation}
\mathbf{z}^{t,emsr} = \mathcal{D}_{emsr}^t(\mathbf{z}^t, \mathbf{z}^{mir}, \mathbf{S}_{tv}) = \mathbf{z}^t - \mathbf{z}^{mir} - \mathbf{z}^{t,imsr}
\end{equation}
\begin{equation}
\mathbf{z}^{v,emsr} = \mathcal{D}_{emsr}^v(\mathbf{z}^v, \mathbf{z}^{mir}, \mathbf{S}_{tv}) = \mathbf{z}^v - \mathbf{z}^{mir} - \mathbf{z}^{v,imsr}
\end{equation}

3. \textbf{Invalid Modality-Specific Representations (IMSR)}:

\begin{equation}
\mathbf{z}^{t,imsr} = \mathcal{D}_{imsr}^t(\mathbf{z}^t, \mathbf{S}_{tv}) = (1 - \mathbf{S}_{tv}) \odot \mathbf{z}^t \odot \mathbf{N}_t
\end{equation}
\begin{equation}
\mathbf{z}^{v,imsr} = \mathcal{D}_{imsr}^v(\mathbf{z}^v, \mathbf{S}_{tv}) = (1 - \mathbf{S}_{tv}) \odot \mathbf{z}^v \odot \mathbf{N}_v
\end{equation}

where $\mathbf{N}_t$ and $\mathbf{N}_v$ are noise masks used to identify invalid modality-specific representations:

\begin{equation}
\mathbf{N}_m = \sigma(\mathbf{W}_n^m \mathbf{z}^m + \mathbf{b}_n^m) \cdot \mathbb{I}(\text{KL}(\mathbf{z}^m \| \mathbf{z}^{kg}) > \tau_m)
\end{equation}

where $m \in \{t, v\}$, $\mathbf{W}_n^m$ and $\mathbf{b}_n^m$ are learnable parameters, $\mathbb{I}$ is an indicator function, $\text{KL}$ is KL divergence, $\mathbf{z}^{kg}$ is the knowledge graph feature, and $\tau_m$ is a threshold parameter.

\subsubsection{Multi-level Orthogonal Constraints}

To ensure independence between different hierarchical representations, we introduce multi-level orthogonal constraints:

\begin{equation}
\mathcal{L}_{ortho} = \lambda_1 \cdot \mathcal{L}_{ortho}^{mir-emsr} + \lambda_2 \cdot \mathcal{L}_{ortho}^{mir-imsr} + \lambda_3 \cdot \mathcal{L}_{ortho}^{emsr-imsr}
\end{equation}

where the various orthogonal constraints are defined as follows:

\begin{equation}
\mathcal{L}_{ortho}^{mir-emsr} = \|\mathbf{z}^{mir} \cdot (\mathbf{z}^{t,emsr})^T\|_F^2 + \|\mathbf{z}^{mir} \cdot (\mathbf{z}^{v,emsr})^T\|_F^2
\end{equation}

\begin{equation}
\mathcal{L}_{ortho}^{mir-imsr} = \|\mathbf{z}^{mir} \cdot (\mathbf{z}^{t,imsr})^T\|_F^2 + \|\mathbf{z}^{mir} \cdot (\mathbf{z}^{v,imsr})^T\|_F^2
\end{equation}

\begin{equation}
\mathcal{L}_{ortho}^{emsr-imsr} = \|\mathbf{z}^{t,emsr} \cdot (\mathbf{z}^{t,imsr})^T\|_F^2 + \|\mathbf{z}^{v,emsr} \cdot (\mathbf{z}^{v,imsr})^T\|_F^2
\end{equation}

\subsubsection{Mutual Information Minimization}

To further reduce information overlap between different hierarchical representations, we introduce mutual information-based constraints:

\begin{equation}
\mathcal{L}_{mi} = \lambda_4 \cdot I(\mathbf{z}^{mir}; \mathbf{z}^{t,emsr}, \mathbf{z}^{v,emsr}) + \lambda_5 \cdot I(\mathbf{z}^{t,emsr}; \mathbf{z}^{v,emsr})
\end{equation}

where $I(\cdot;\cdot)$ represents mutual information. Since mutual information is difficult to compute directly, we adopt a neural network-based mutual information estimator:

\begin{equation}
I(\mathbf{x}; \mathbf{y}) \approx \mathbb{E}_{p(\mathbf{x},\mathbf{y})}[T_\omega(\mathbf{x}, \mathbf{y})] - \log \mathbb{E}_{p(\mathbf{x})p(\mathbf{y})}[e^{T_\omega(\mathbf{x}, \mathbf{y})}]
\end{equation}

where $T_\omega$ is a neural network with parameters $\omega$, used to estimate mutual information.

\section{Dynamic Semantic-Aware Fusion (DSAF)}

\subsection{Hierarchical Semantic-Aware Adaptive Fusion}

To effectively integrate semantic representations at different hierarchical levels, we design a dynamic semantic-aware fusion module that can adaptively adjust the importance of different representations according to the characteristics of the input sample and task requirements. Unlike traditional fixed-weight fusion methods, our method considers the semantic relationships and complementarity between representations, optimizing the fusion process through complex attention mechanisms and semantic gating networks.

Given modality-invariant representations $\mathbf{z}^{mir}$, effective modality-specific representations $\mathbf{z}^{t,emsr}$ and $\mathbf{z}^{v,emsr}$, and knowledge graph features $\mathbf{z}^{kg}$, we first compute the semantic associations between them through a multi-head cross-attention mechanism:

\begin{equation}
\mathbf{A} = \text{MultiHead}(\mathbf{Q}, \mathbf{K}, \mathbf{V})
\end{equation}

where $\mathbf{Q} = \mathbf{W}_Q [\mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg}]$, $\mathbf{K} = \mathbf{W}_K [\mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg}]$, $\mathbf{V} = \mathbf{W}_V [\mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg}]$, and $\mathbf{W}_Q, \mathbf{W}_K, \mathbf{W}_V$ are learnable parameter matrices.

The multi-head attention mechanism is defined as:

\begin{equation}
\text{MultiHead}(\mathbf{Q}, \mathbf{K}, \mathbf{V}) = \mathbf{W}_O [\text{head}_1 \| \text{head}_2 \| \cdots \| \text{head}_h]
\end{equation}

where $\text{head}_i = \text{Attention}(\mathbf{Q}\mathbf{W}_i^Q, \mathbf{K}\mathbf{W}_i^K, \mathbf{V}\mathbf{W}_i^V)$, $\mathbf{W}_i^Q, \mathbf{W}_i^K, \mathbf{W}_i^V$ are parameter matrices for the $i$-th attention head, and $\mathbf{W}_O$ is an output projection matrix.

The attention function is defined as:

\begin{equation}
\text{Attention}(\mathbf{Q}, \mathbf{K}, \mathbf{V}) = \text{softmax}\left(\frac{\mathbf{Q}\mathbf{K}^T}{\sqrt{d_k}} + \mathbf{M}\right) \mathbf{V}
\end{equation}

where $\mathbf{M}$ is a semantic association matrix based on the knowledge graph, defined as:

\begin{equation}
\mathbf{M}_{ij} = \sum_{(v_p, r, v_q) \in \mathcal{G}} \eta_{pq} \cdot \text{sim}(\mathbf{z}_i, \mathbf{v}_p) \cdot \text{sim}(\mathbf{z}_j, \mathbf{v}_q)
\end{equation}

where $\eta_{pq}$ is the importance weight of relationship $(v_p, r, v_q)$, and $\text{sim}$ is a cosine similarity function.

Based on the attention output, we design a semantic-aware gated fusion network:

\begin{equation}
\mathbf{g} = \sigma_g(\mathbf{W}_g [\mathbf{A} \| \mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg}] + \mathbf{b}_g)
\end{equation}

where $\mathbf{W}_g$ and $\mathbf{b}_g$ are learnable parameters, $\sigma_g$ is a Sigmoid function, and $\mathbf{g} \in \mathbb{R}^{4 \times d}$ is a fusion gating matrix.

The final fused representation is computed through the gating mechanism:

\begin{equation}
\mathbf{z}^{fused} = \mathbf{g}_1 \odot \mathbf{z}^{mir} + \mathbf{g}_2 \odot \mathbf{z}^{t,emsr} + \mathbf{g}_3 \odot \mathbf{z}^{v,emsr} + \mathbf{g}_4 \odot \mathbf{z}^{kg}
\end{equation}

where $\mathbf{g}_i$ is the $i$-th row of the gating matrix $\mathbf{g}$, and $\odot$ represents element-wise multiplication.

\subsection{Semantic-Enhanced Hierarchical Classifier}

To fully utilize the semantic information of hierarchical disentangled representations, we design a semantic-enhanced hierarchical classifier that can adaptively adjust classification decisions according to the semantic characteristics of different hierarchical representations.

First, we design an expert classifier for each hierarchical representation:

\begin{equation}
\hat{y}^{mir} = \sigma(\mathbf{W}_c^{mir} \mathbf{z}^{mir} + \mathbf{b}_c^{mir})
\end{equation}
\begin{equation}
\hat{y}^{t,emsr} = \sigma(\mathbf{W}_c^{t,emsr} \mathbf{z}^{t,emsr} + \mathbf{b}_c^{t,emsr})
\end{equation}
\begin{equation}
\hat{y}^{v,emsr} = \sigma(\mathbf{W}_c^{v,emsr} \mathbf{z}^{v,emsr} + \mathbf{b}_c^{v,emsr})
\end{equation}
\begin{equation}
\hat{y}^{kg} = \sigma(\mathbf{W}_c^{kg} \mathbf{z}^{kg} + \mathbf{b}_c^{kg})
\end{equation}
\begin{equation}
\hat{y}^{fused} = \sigma(\mathbf{W}_c^{fused} \mathbf{z}^{fused} + \mathbf{b}_c^{fused})
\end{equation}

where $\mathbf{W}_c^*$ and $\mathbf{b}_c^*$ are parameters of various expert classifiers, and $\sigma$ is a Sigmoid function.

Then, we integrate the prediction results of various experts through a semantic-aware gating mechanism:

\begin{equation}
\boldsymbol{\alpha} = \text{softmax}(\mathbf{W}_{\alpha} [\mathbf{z}^{mir} \| \mathbf{z}^{t,emsr} \| \mathbf{z}^{v,emsr} \| \mathbf{z}^{kg} \| \mathbf{z}^{fused}] + \mathbf{b}_{\alpha})
\end{equation}

\begin{equation}
\hat{y} = \alpha_1 \hat{y}^{mir} + \alpha_2 \hat{y}^{t,emsr} + \alpha_3 \hat{y}^{v,emsr} + \alpha_4 \hat{y}^{kg} + \alpha_5 \hat{y}^{fused}
\end{equation}

where $\mathbf{W}_{\alpha}$ and $\mathbf{b}_{\alpha}$ are learnable parameters, $\boldsymbol{\alpha} = [\alpha_1, \alpha_2, \alpha_3, \alpha_4, \alpha_5]^T$ is an expert weight vector, and $\hat{y} \in [0, 1]^C$ is the final prediction probability.

To further enhance the semantic understanding ability of the classifier, we introduce a knowledge graph-based semantic correction mechanism:

\begin{equation}
\hat{y}^{corrected} = \hat{y} + \Delta \hat{y}^{kg}
\end{equation}

where $\Delta \hat{y}^{kg}$ is a semantic correction term based on the knowledge graph, defined as:

\begin{equation}
\Delta \hat{y}^{kg}_c = \beta_c \cdot \sum_{(v_i, r, v_j) \in \mathcal{G}_c} \phi(v_i, v_j) \cdot (\hat{y}_i - \hat{y}_j)
\end{equation}

where $\mathcal{G}_c$ is a knowledge graph subgraph related to category $c$, $\phi(v_i, v_j)$ is the semantic similarity between entities $v_i$ and $v_j$, and $\beta_c$ is a category-specific correction weight.

\section{Hierarchical Contrastive Optimization (HCO)}

To further enhance the model's understanding of cross-modal semantics, we design a hierarchical contrastive optimization module that optimizes the semantic consistency and discriminability of hierarchical disentangled representations through multi-granularity contrastive learning objectives.

\subsection{Multi-granularity Contrastive Learning Objectives}

We design three levels of contrastive learning objectives, targeting modality-invariant representations, effective modality-specific representations, and fused representations, respectively:

\subsubsection{Modality-Invariant Representation Contrastive Learning}

For modality-invariant representations, we expect that modality-invariant representations from different modalities of the same sample should be similar, while modality-invariant representations from different samples should be different:

\begin{equation}
\mathcal{L}_{cl}^{mir} = -\frac{1}{N} \sum_{i=1}^N \log \frac{\exp(\text{sim}(\mathbf{z}_i^{mir}, \mathbf{z}_i^{mir,aug}) / \tau)}{\sum_{j=1}^N \exp(\text{sim}(\mathbf{z}_i^{mir}, \mathbf{z}_j^{mir}) / \tau)}
\end{equation}

where $\mathbf{z}_i^{mir,aug}$ is an augmented version of $\mathbf{z}_i^{mir}$, obtained by slightly perturbing the original features, $\text{sim}$ is a cosine similarity function, and $\tau$ is a temperature parameter.

\subsubsection{Effective Modality-Specific Representation Contrastive Learning}

For effective modality-specific representations, we expect that effective specific representations within the same modality should cluster according to semantic similarity, while effective specific representations from different modalities should be separated:

\begin{equation}
\mathcal{L}_{cl}^{emsr} = -\frac{1}{N} \sum_{i=1}^N \left[ \log \frac{\exp(\text{sim}(\mathbf{z}_i^{t,emsr}, \mathbf{z}_i^{t,emsr,aug}) / \tau)}{\sum_{j=1}^N \exp(\text{sim}(\mathbf{z}_i^{t,emsr}, \mathbf{z}_j^{t,emsr}) / \tau)} + \log \frac{\exp(\text{sim}(\mathbf{z}_i^{v,emsr}, \mathbf{z}_i^{v,emsr,aug}) / \tau)}{\sum_{j=1}^N \exp(\text{sim}(\mathbf{z}_i^{v,emsr}, \mathbf{z}_j^{v,emsr}) / \tau)} \right]

\subsubsection{Fused Representation Contrastive Learning}

For fused representations, we expect that fused representations of samples with similar labels should be similar, while fused representations of samples with different labels should be different:

\begin{equation}
\mathcal{L}_{cl}^{fused} = -\frac{1}{N} \sum_{i=1}^N \log \frac{\sum_{j \in \mathcal{P}_i} \exp(\text{sim}(\mathbf{z}_i^{fused}, \mathbf{z}_j^{fused}) / \tau)}{\sum_{j=1}^N \exp(\text{sim}(\mathbf{z}_i^{fused}, \mathbf{z}_j^{fused}) / \tau)}
\end{equation}

where $\mathcal{P}_i$ is the set of samples with labels similar to sample $i$, defined as $\mathcal{P}_i = \{j | \text{sim}(y_i, y_j) > \delta\}$, and $\delta$ is a similarity threshold.

\subsection{Knowledge Graph Guided Contrastive Learning}

To integrate knowledge graph information into contrastive learning, we design a knowledge graph guided contrastive learning objective:

\begin{equation}
\mathcal{L}_{cl}^{kg} = -\frac{1}{N} \sum_{i=1}^N \log \frac{\sum_{j \in \mathcal{K}_i} \exp(\text{sim}(\mathbf{z}_i^{fused}, \mathbf{z}_j^{fused}) / \tau)}{\sum_{j=1}^N \exp(\text{sim}(\mathbf{z}_i^{fused}, \mathbf{z}_j^{fused}) / \tau)}
\end{equation}

where $\mathcal{K}_i$ is the set of samples semantically related to sample $i$ in the knowledge graph, defined as $\mathcal{K}_i = \{j | \exists (v_p, r, v_q) \in \mathcal{G}, v_p \in \mathcal{V}_i, v_q \in \mathcal{V}_j\}$, and $\mathcal{V}_i$ is the set of entities related to sample $i$.

\subsection{Hierarchical Contrastive Learning Total Loss}

The total loss of hierarchical contrastive learning is:

\begin{equation}
\mathcal{L}_{HCO} = \lambda_{mir} \mathcal{L}_{cl}^{mir} + \lambda_{emsr} \mathcal{L}_{cl}^{emsr} + \lambda_{fused} \mathcal{L}_{cl}^{fused} + \lambda_{kg} \mathcal{L}_{cl}^{kg}
\end{equation}

where $\lambda_{mir}$, $\lambda_{emsr}$, $\lambda_{fused}$, and $\lambda_{kg}$ are hyperparameters that balance different contrastive learning objectives.

\section{Joint Optimization Framework}

\subsection{Multi-objective Optimization}

The overall optimization objective of KG-HierDisNet is a multi-objective optimization problem, including classification loss, hierarchical disentanglement loss, contrastive learning loss, and knowledge graph consistency loss:

\begin{equation}
\mathcal{L}_{total} = \mathcal{L}_{cls} + \mathcal{L}_{disent} + \mathcal{L}_{HCO} + \mathcal{L}_{kg\_cons}
\end{equation}

where the various loss terms are defined as follows:

\begin{itemize}
\item Classification loss:
\begin{equation}
\mathcal{L}_{cls} = -\frac{1}{N} \sum_{i=1}^N \sum_{j=1}^C [y_{ij} \log(\hat{y}_{ij}^{corrected}) + (1 - y_{ij}) \log(1 - \hat{y}_{ij}^{corrected})] + \lambda_{focal} \cdot \mathcal{L}_{focal}
\end{equation}

where $\mathcal{L}_{focal}$ is a focal loss, used to handle class imbalance:
\begin{equation}
\mathcal{L}_{focal} = -\frac{1}{N} \sum_{i=1}^N \sum_{j=1}^C [y_{ij} (1 - \hat{y}_{ij}^{corrected})^\gamma \log(\hat{y}_{ij}^{corrected}) + (1 - y_{ij}) (\hat{y}_{ij}^{corrected})^\gamma \log(1 - \hat{y}_{ij}^{corrected})]
\end{equation}

\item Hierarchical disentanglement loss:
\begin{equation}
\mathcal{L}_{disent} = \mathcal{L}_{ortho} + \mathcal{L}_{mi} + \mathcal{L}_{recon}
\end{equation}

where $\mathcal{L}_{recon}$ is a reconstruction loss, used to ensure that disentangled representations can reconstruct the original features:
\begin{equation}
\mathcal{L}_{recon} = \frac{1}{N} \sum_{i=1}^N [\|\mathbf{z}_i^t - (\mathbf{z}_i^{mir} + \mathbf{z}_i^{t,emsr} + \mathbf{z}_i^{t,imsr})\|_2^2 + \|\mathbf{z}_i^v - (\mathbf{z}_i^{mir} + \mathbf{z}_i^{v,emsr} + \mathbf{z}_i^{v,imsr})\|_2^2]
\end{equation}

\item Knowledge graph consistency loss:
\begin{equation}
\mathcal{L}_{kg\_cons} = \frac{1}{N} \sum_{i=1}^N \|\mathbf{z}_i^{fused} - \sum_{v \in \mathcal{V}_i} \omega_v \cdot \mathbf{v}\|_2^2 + \lambda_{struct} \cdot \mathcal{L}_{struct}
\end{equation}

where $\omega_v$ is the importance weight of entity $v$, and $\mathcal{L}_{struct}$ is a structure preservation loss:
\begin{equation}
\mathcal{L}_{struct} = \frac{1}{|\mathcal{E}|} \sum_{(v_i, r, v_j) \in \mathcal{E}} \|\phi_r(\mathbf{z}_i^{fused}) - \mathbf{z}_j^{fused}\|_2^2
\end{equation}
\end{itemize}

\subsection{Optimization Strategy}

To effectively optimize this complex multi-objective problem, we adopt a phased optimization strategy:

\begin{enumerate}
\item \textbf{Pre-training phase}: First pre-train the knowledge graph representation learning module, minimizing $\mathcal{L}_{SKGCRL}$.
\item \textbf{Joint training phase}: Then jointly train the entire model, minimizing $\mathcal{L}_{total}$.
\item \textbf{Fine-tuning phase}: Finally fine-tune the model for specific tasks, focusing on optimizing $\mathcal{L}_{cls}$.
\end{enumerate}

During the optimization process, we adopt a dynamic weight adjustment strategy, adaptively adjusting the weights of different loss terms according to their gradient magnitudes:

\begin{equation}
\lambda_i^{(t+1)} = \lambda_i^{(t)} \cdot \exp\left(\alpha \cdot \frac{\|\nabla_{\theta} \mathcal{L}_i\|_2}{\sum_j \|\nabla_{\theta} \mathcal{L}_j\|_2}\right)
\end{equation}

where $\lambda_i^{(t)}$ is the weight of loss term $\mathcal{L}_i$ in the $t$-th iteration, $\nabla_{\theta} \mathcal{L}_i$ is the gradient of loss term $\mathcal{L}_i$ with respect to model parameters $\theta$, and $\alpha$ is an adjustment rate.

\end{document}
