"""
Script for creating beautiful visualizations of feature spaces and cross-modal redundancy.
This script focuses on:
1. Creating high-quality 2D and 3D visualizations of feature spaces
2. Visualizing cross-modal redundancy in feature spaces
3. Comparing original and refined features
4. Generating publication-ready figures
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
import argparse
from matplotlib.ticker import MaxNLocator
import matplotlib.patches as mpatches
from matplotlib.lines import Line2D
import matplotlib.cm as cm
from matplotlib.animation import FuncAnimation
from matplotlib import animation
import plotly.graph_objects as go
import plotly.express as px
import plotly.io as pio
from plotly.subplots import make_subplots

from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet

# Set up beautiful visualization style
plt.style.use('seaborn-v0_8-whitegrid')
custom_params = {
    'axes.spines.right': False,
    'axes.spines.top': False,
    'axes.grid': True,
    'grid.linestyle': '--',
    'grid.alpha': 0.7,
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif'],
    'figure.figsize': (12, 8),
    'figure.dpi': 150,
}
plt.rcParams.update(custom_params)

# Custom color palettes
main_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
redundancy_cmap = LinearSegmentedColormap.from_list('redundancy', ['#f0f9e8', '#7bccc4', '#43a2ca', '#0868ac'])
feature_cmap = LinearSegmentedColormap.from_list('features', ['#feebe2', '#fbb4b9', '#f768a1', '#c51b8a', '#7a0177'])

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Feature visualization for KG-Disentangle-Net")
    
    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/visualizations/features',
                        help='Output directory for saving visualizations')
    
    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')
    
    # Visualization arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--interactive', action='store_true',
                        help='Generate interactive visualizations')
    parser.add_argument('--num_samples', type=int, default=1000,
                        help='Number of samples to visualize')
    parser.add_argument('--save_features', action='store_true',
                        help='Save extracted features to disk')
    parser.add_argument('--load_features', type=str, default=None,
                        help='Path to load pre-extracted features')
    
    return parser.parse_args()

def collect_features(model, data_loader, device, max_samples=1000):
    """Collect features from the model."""
    model.eval()
    all_text_encoded = []
    all_visual_encoded = []
    all_text_refined = []
    all_visual_refined = []
    all_redundancy_scores = []
    all_labels = []
    
    sample_count = 0
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Collecting features"):
            if sample_count >= max_samples:
                break
                
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None
            
            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features
            
            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image
            
            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            
            # Collect features
            all_text_encoded.append(outputs['text_encoded'].cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].cpu().numpy())
            all_labels.append(labels.cpu().numpy())
            
            # Collect refined features if available
            if 'text_refined' in outputs:
                all_text_refined.append(outputs['text_refined'].cpu().numpy())
            if 'visual_refined' in outputs:
                all_visual_refined.append(outputs['visual_refined'].cpu().numpy())
            
            sample_count += len(text)
    
    # Concatenate all features
    features = {
        'text_encoded': np.vstack(all_text_encoded)[:max_samples],
        'visual_encoded': np.vstack(all_visual_encoded)[:max_samples],
        'redundancy_scores': np.vstack(all_redundancy_scores)[:max_samples],
        'labels': np.vstack(all_labels)[:max_samples]
    }
    
    if all_text_refined and all_visual_refined:
        features['text_refined'] = np.vstack(all_text_refined)[:max_samples]
        features['visual_refined'] = np.vstack(all_visual_refined)[:max_samples]
    
    return features

def visualize_feature_space_2d(features, output_dir, method='tsne'):
    """Visualize feature space in 2D."""
    # Reduce dimensionality for visualization
    if method == 'tsne':
        reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        text_2d = reducer.fit_transform(features['text_encoded'])
        reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        visual_2d = reducer.fit_transform(features['visual_encoded'])
    elif method == 'umap':
        reducer = umap.UMAP(n_components=2, random_state=42)
        text_2d = reducer.fit_transform(features['text_encoded'])
        reducer = umap.UMAP(n_components=2, random_state=42)
        visual_2d = reducer.fit_transform(features['visual_encoded'])
    else:  # PCA
        reducer = PCA(n_components=2, random_state=42)
        text_2d = reducer.fit_transform(features['text_encoded'])
        reducer = PCA(n_components=2, random_state=42)
        visual_2d = reducer.fit_transform(features['visual_encoded'])
    
    # Create a figure with subplots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Color by redundancy score
    redundancy_scores = features['redundancy_scores'].flatten()
    
    # Text features
    scatter1 = ax1.scatter(text_2d[:, 0], text_2d[:, 1], c=redundancy_scores, 
                          cmap=redundancy_cmap, alpha=0.8, s=50, edgecolor='none')
    ax1.set_title('Text Feature Space', fontsize=18, pad=20)
    ax1.set_xlabel(f'{method.upper()} Component 1', fontsize=14, labelpad=10)
    ax1.set_ylabel(f'{method.upper()} Component 2', fontsize=14, labelpad=10)
    ax1.grid(True, alpha=0.3)
    
    # Visual features
    scatter2 = ax2.scatter(visual_2d[:, 0], visual_2d[:, 1], c=redundancy_scores, 
                          cmap=redundancy_cmap, alpha=0.8, s=50, edgecolor='none')
    ax2.set_title('Visual Feature Space', fontsize=18, pad=20)
    ax2.set_xlabel(f'{method.upper()} Component 1', fontsize=14, labelpad=10)
    ax2.set_ylabel(f'{method.upper()} Component 2', fontsize=14, labelpad=10)
    ax2.grid(True, alpha=0.3)
    
    # Add colorbar
    cbar = fig.colorbar(scatter1, ax=[ax1, ax2], orientation='horizontal', pad=0.05)
    cbar.set_label('Redundancy Score', fontsize=14, labelpad=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'feature_space_2d_{method}.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # If refined features are available, visualize them too
    if 'text_refined' in features and 'visual_refined' in features:
        # Reduce dimensionality for visualization
        if method == 'tsne':
            reducer = TSNE(n_components=2, random_state=42, perplexity=30)
            text_refined_2d = reducer.fit_transform(features['text_refined'])
            reducer = TSNE(n_components=2, random_state=42, perplexity=30)
            visual_refined_2d = reducer.fit_transform(features['visual_refined'])
        elif method == 'umap':
            reducer = umap.UMAP(n_components=2, random_state=42)
            text_refined_2d = reducer.fit_transform(features['text_refined'])
            reducer = umap.UMAP(n_components=2, random_state=42)
            visual_refined_2d = reducer.fit_transform(features['visual_refined'])
        else:  # PCA
            reducer = PCA(n_components=2, random_state=42)
            text_refined_2d = reducer.fit_transform(features['text_refined'])
            reducer = PCA(n_components=2, random_state=42)
            visual_refined_2d = reducer.fit_transform(features['visual_refined'])
        
        # Create a figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
        
        # Text features
        scatter1 = ax1.scatter(text_refined_2d[:, 0], text_refined_2d[:, 1], c=redundancy_scores, 
                              cmap=redundancy_cmap, alpha=0.8, s=50, edgecolor='none')
        ax1.set_title('Refined Text Feature Space', fontsize=18, pad=20)
        ax1.set_xlabel(f'{method.upper()} Component 1', fontsize=14, labelpad=10)
        ax1.set_ylabel(f'{method.upper()} Component 2', fontsize=14, labelpad=10)
        ax1.grid(True, alpha=0.3)
        
        # Visual features
        scatter2 = ax2.scatter(visual_refined_2d[:, 0], visual_refined_2d[:, 1], c=redundancy_scores, 
                              cmap=redundancy_cmap, alpha=0.8, s=50, edgecolor='none')
        ax2.set_title('Refined Visual Feature Space', fontsize=18, pad=20)
        ax2.set_xlabel(f'{method.upper()} Component 1', fontsize=14, labelpad=10)
        ax2.set_ylabel(f'{method.upper()} Component 2', fontsize=14, labelpad=10)
        ax2.grid(True, alpha=0.3)
        
        # Add colorbar
        cbar = fig.colorbar(scatter1, ax=[ax1, ax2], orientation='horizontal', pad=0.05)
        cbar.set_label('Redundancy Score', fontsize=14, labelpad=10)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'refined_feature_space_2d_{method}.png'), dpi=300, bbox_inches='tight')
        plt.close()

def visualize_feature_space_3d(features, output_dir, method='tsne'):
    """Visualize feature space in 3D."""
    # Reduce dimensionality for visualization
    if method == 'tsne':
        reducer = TSNE(n_components=3, random_state=42, perplexity=30)
        text_3d = reducer.fit_transform(features['text_encoded'])
        reducer = TSNE(n_components=3, random_state=42, perplexity=30)
        visual_3d = reducer.fit_transform(features['visual_encoded'])
    elif method == 'umap':
        reducer = umap.UMAP(n_components=3, random_state=42)
        text_3d = reducer.fit_transform(features['text_encoded'])
        reducer = umap.UMAP(n_components=3, random_state=42)
        visual_3d = reducer.fit_transform(features['visual_encoded'])
    else:  # PCA
        reducer = PCA(n_components=3, random_state=42)
        text_3d = reducer.fit_transform(features['text_encoded'])
        reducer = PCA(n_components=3, random_state=42)
        visual_3d = reducer.fit_transform(features['visual_encoded'])
    
    # Create a figure with 3D subplots
    fig = plt.figure(figsize=(20, 10))
    ax1 = fig.add_subplot(121, projection='3d')
    ax2 = fig.add_subplot(122, projection='3d')
    
    # Color by redundancy score
    redundancy_scores = features['redundancy_scores'].flatten()
    
    # Text features
    scatter1 = ax1.scatter(text_3d[:, 0], text_3d[:, 1], text_3d[:, 2], c=redundancy_scores, 
                          cmap=redundancy_cmap, alpha=0.8, s=50)
    ax1.set_title('Text Feature Space (3D)', fontsize=18, pad=20)
    ax1.set_xlabel(f'Component 1', fontsize=12)
    ax1.set_ylabel(f'Component 2', fontsize=12)
    ax1.set_zlabel(f'Component 3', fontsize=12)
    
    # Visual features
    scatter2 = ax2.scatter(visual_3d[:, 0], visual_3d[:, 1], visual_3d[:, 2], c=redundancy_scores, 
                          cmap=redundancy_cmap, alpha=0.8, s=50)
    ax2.set_title('Visual Feature Space (3D)', fontsize=18, pad=20)
    ax2.set_xlabel(f'Component 1', fontsize=12)
    ax2.set_ylabel(f'Component 2', fontsize=12)
    ax2.set_zlabel(f'Component 3', fontsize=12)
    
    # Add colorbar
    cbar = fig.colorbar(scatter1, ax=[ax1, ax2], orientation='horizontal', pad=0.05, shrink=0.8)
    cbar.set_label('Redundancy Score', fontsize=14, labelpad=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'feature_space_3d_{method}.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # If refined features are available, visualize them too
    if 'text_refined' in features and 'visual_refined' in features:
        # Reduce dimensionality for visualization
        if method == 'tsne':
            reducer = TSNE(n_components=3, random_state=42, perplexity=30)
            text_refined_3d = reducer.fit_transform(features['text_refined'])
            reducer = TSNE(n_components=3, random_state=42, perplexity=30)
            visual_refined_3d = reducer.fit_transform(features['visual_refined'])
        elif method == 'umap':
            reducer = umap.UMAP(n_components=3, random_state=42)
            text_refined_3d = reducer.fit_transform(features['text_refined'])
            reducer = umap.UMAP(n_components=3, random_state=42)
            visual_refined_3d = reducer.fit_transform(features['visual_refined'])
        else:  # PCA
            reducer = PCA(n_components=3, random_state=42)
            text_refined_3d = reducer.fit_transform(features['text_refined'])
            reducer = PCA(n_components=3, random_state=42)
            visual_refined_3d = reducer.fit_transform(features['visual_refined'])
        
        # Create a figure with 3D subplots
        fig = plt.figure(figsize=(20, 10))
        ax1 = fig.add_subplot(121, projection='3d')
        ax2 = fig.add_subplot(122, projection='3d')
        
        # Text features
        scatter1 = ax1.scatter(text_refined_3d[:, 0], text_refined_3d[:, 1], text_refined_3d[:, 2], c=redundancy_scores, 
                              cmap=redundancy_cmap, alpha=0.8, s=50)
        ax1.set_title('Refined Text Feature Space (3D)', fontsize=18, pad=20)
        ax1.set_xlabel(f'Component 1', fontsize=12)
        ax1.set_ylabel(f'Component 2', fontsize=12)
        ax1.set_zlabel(f'Component 3', fontsize=12)
        
        # Visual features
        scatter2 = ax2.scatter(visual_refined_3d[:, 0], visual_refined_3d[:, 1], visual_refined_3d[:, 2], c=redundancy_scores, 
                              cmap=redundancy_cmap, alpha=0.8, s=50)
        ax2.set_title('Refined Visual Feature Space (3D)', fontsize=18, pad=20)
        ax2.set_xlabel(f'Component 1', fontsize=12)
        ax2.set_ylabel(f'Component 2', fontsize=12)
        ax2.set_zlabel(f'Component 3', fontsize=12)
        
        # Add colorbar
        cbar = fig.colorbar(scatter1, ax=[ax1, ax2], orientation='horizontal', pad=0.05, shrink=0.8)
        cbar.set_label('Redundancy Score', fontsize=14, labelpad=10)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'refined_feature_space_3d_{method}.png'), dpi=300, bbox_inches='tight')
        plt.close()

def create_interactive_feature_visualization(features, output_dir):
    """Create interactive feature visualizations using Plotly."""
    # Reduce dimensionality for visualization
    reducer = PCA(n_components=3, random_state=42)
    text_3d = reducer.fit_transform(features['text_encoded'])
    reducer = PCA(n_components=3, random_state=42)
    visual_3d = reducer.fit_transform(features['visual_encoded'])
    
    # Color by redundancy score
    redundancy_scores = features['redundancy_scores'].flatten()
    
    # Create interactive 3D scatter plots
    # Text features
    fig = px.scatter_3d(
        x=text_3d[:, 0], y=text_3d[:, 1], z=text_3d[:, 2],
        color=redundancy_scores,
        color_continuous_scale='Viridis',
        opacity=0.8,
        title='Text Feature Space (3D)',
        labels={'color': 'Redundancy Score'}
    )
    
    fig.update_layout(
        scene=dict(
            xaxis_title='Component 1',
            yaxis_title='Component 2',
            zaxis_title='Component 3'
        ),
        title_font_size=24,
        scene_camera=dict(
            up=dict(x=0, y=0, z=1),
            center=dict(x=0, y=0, z=0),
            eye=dict(x=1.5, y=1.5, z=1.5)
        )
    )
    
    # Save as HTML
    pio.write_html(fig, os.path.join(output_dir, 'text_feature_space_3d_interactive.html'))
    
    # Visual features
    fig = px.scatter_3d(
        x=visual_3d[:, 0], y=visual_3d[:, 1], z=visual_3d[:, 2],
        color=redundancy_scores,
        color_continuous_scale='Viridis',
        opacity=0.8,
        title='Visual Feature Space (3D)',
        labels={'color': 'Redundancy Score'}
    )
    
    fig.update_layout(
        scene=dict(
            xaxis_title='Component 1',
            yaxis_title='Component 2',
            zaxis_title='Component 3'
        ),
        title_font_size=24,
        scene_camera=dict(
            up=dict(x=0, y=0, z=1),
            center=dict(x=0, y=0, z=0),
            eye=dict(x=1.5, y=1.5, z=1.5)
        )
    )
    
    # Save as HTML
    pio.write_html(fig, os.path.join(output_dir, 'visual_feature_space_3d_interactive.html'))
    
    # If refined features are available, visualize them too
    if 'text_refined' in features and 'visual_refined' in features:
        # Reduce dimensionality for visualization
        reducer = PCA(n_components=3, random_state=42)
        text_refined_3d = reducer.fit_transform(features['text_refined'])
        reducer = PCA(n_components=3, random_state=42)
        visual_refined_3d = reducer.fit_transform(features['visual_refined'])
        
        # Text refined features
        fig = px.scatter_3d(
            x=text_refined_3d[:, 0], y=text_refined_3d[:, 1], z=text_refined_3d[:, 2],
            color=redundancy_scores,
            color_continuous_scale='Viridis',
            opacity=0.8,
            title='Refined Text Feature Space (3D)',
            labels={'color': 'Redundancy Score'}
        )
        
        fig.update_layout(
            scene=dict(
                xaxis_title='Component 1',
                yaxis_title='Component 2',
                zaxis_title='Component 3'
            ),
            title_font_size=24,
            scene_camera=dict(
                up=dict(x=0, y=0, z=1),
                center=dict(x=0, y=0, z=0),
                eye=dict(x=1.5, y=1.5, z=1.5)
            )
        )
        
        # Save as HTML
        pio.write_html(fig, os.path.join(output_dir, 'refined_text_feature_space_3d_interactive.html'))
        
        # Visual refined features
        fig = px.scatter_3d(
            x=visual_refined_3d[:, 0], y=visual_refined_3d[:, 1], z=visual_refined_3d[:, 2],
            color=redundancy_scores,
            color_continuous_scale='Viridis',
            opacity=0.8,
            title='Refined Visual Feature Space (3D)',
            labels={'color': 'Redundancy Score'}
        )
        
        fig.update_layout(
            scene=dict(
                xaxis_title='Component 1',
                yaxis_title='Component 2',
                zaxis_title='Component 3'
            ),
            title_font_size=24,
            scene_camera=dict(
                up=dict(x=0, y=0, z=1),
                center=dict(x=0, y=0, z=0),
                eye=dict(x=1.5, y=1.5, z=1.5)
            )
        )
        
        # Save as HTML
        pio.write_html(fig, os.path.join(output_dir, 'refined_visual_feature_space_3d_interactive.html'))

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load pre-extracted features if provided
    if args.load_features:
        print(f"Loading features from {args.load_features}...")
        with open(args.load_features, 'rb') as f:
            features = np.load(f, allow_pickle=True).item()
    else:
        # Set device
        device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {device}")
        
        # Create dataset
        print("Creating dataset...")
        test_dataset = MMIMDBDataset(
            data_path=args.data_path,
            kg_path=args.kg_path,
            mode='test'
        )
        
        # Create data loader
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Create model
        print("Creating model...")
        model = KGDisentangleNet(
            text_dim=args.text_dim,
            visual_dim=args.visual_dim,
            kg_dim=args.kg_dim,
            hidden_dim=args.hidden_dim,
            num_classes=args.num_classes
        ).to(device)
        
        # Load model weights
        print(f"Loading model from {args.model_path}...")
        model.load_state_dict(torch.load(args.model_path, map_location=device))
        model.eval()
        
        # Collect features
        print("Collecting features...")
        features = collect_features(model, test_loader, device, max_samples=args.num_samples)
        
        # Save features if requested
        if args.save_features:
            features_path = os.path.join(args.output_dir, 'features.npy')
            print(f"Saving features to {features_path}...")
            with open(features_path, 'wb') as f:
                np.save(f, features)
    
    # Create visualizations
    print("Creating visualizations...")
    
    # 2D visualizations
    print("Creating 2D visualizations...")
    visualize_feature_space_2d(features, args.output_dir, method='tsne')
    visualize_feature_space_2d(features, args.output_dir, method='umap')
    visualize_feature_space_2d(features, args.output_dir, method='pca')
    
    # 3D visualizations
    print("Creating 3D visualizations...")
    visualize_feature_space_3d(features, args.output_dir, method='tsne')
    visualize_feature_space_3d(features, args.output_dir, method='umap')
    visualize_feature_space_3d(features, args.output_dir, method='pca')
    
    # Interactive visualizations
    if args.interactive:
        print("Creating interactive visualizations...")
        create_interactive_feature_visualization(features, args.output_dir)
    
    print(f"Visualizations saved to {args.output_dir}")

if __name__ == '__main__':
    main()
