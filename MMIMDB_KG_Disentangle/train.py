"""
Training script for the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network.
"""

import os
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import ReduceLROnPlateau
import numpy as np
from tqdm import tqdm
from sklearn.metrics import precision_recall_fscore_support, average_precision_score
import logging
import json
from tensorboardX import SummaryWriter

from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
from utils.kg_constructor import KnowledgeGraphConstructor

# Set up logging
logging.basicConfig(
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train KG-Disentangle-Net on MM-IMDB")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='/home/<USER>/workplace/dwb/data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output',
                        help='Output directory for saving models and logs')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Training arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--num_epochs', type=int, default=30,
                        help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='Weight decay')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--build_kg', action='store_true',
                        help='Whether to build the knowledge graph')
    parser.add_argument('--sample_ratio', type=float, default=1.0,
                        help='Ratio of data to sample (for low-resource experiments)')

    return parser.parse_args()

def set_seed(seed):
    """Set random seed for reproducibility."""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    np.random.seed(seed)

def compute_metrics(y_true, y_pred, threshold=0.5):
    """Compute evaluation metrics for multi-label classification."""
    # Convert predictions to binary using threshold
    y_pred_binary = (y_pred > threshold).astype(int)

    # Compute precision, recall, F1 score
    precision, recall, f1, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average='samples'
    )

    # Compute mean average precision
    mAP = average_precision_score(y_true, y_pred, average='samples')

    # Compute per-class metrics
    per_class_precision, per_class_recall, per_class_f1, _ = precision_recall_fscore_support(
        y_true, y_pred_binary, average=None
    )

    return {
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'mAP': mAP,
        'per_class_precision': per_class_precision.tolist(),
        'per_class_recall': per_class_recall.tolist(),
        'per_class_f1': per_class_f1.tolist()
    }

def train(args):
    """Train the model."""
    # Set random seed
    set_seed(args.seed)

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Build knowledge graph if needed
    if args.build_kg:
        logger.info("Building knowledge graph...")
        kg_constructor = KnowledgeGraphConstructor(
            data_path=args.data_path,
            output_path=args.kg_path
        )
        kg_constructor.construct()

    # Create datasets
    logger.info("Creating datasets...")
    train_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='train',
        sample_ratio=args.sample_ratio
    )

    val_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='val'
    )

    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)

    # Create optimizer and scheduler
    optimizer = optim.Adam(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay
    )

    scheduler = ReduceLROnPlateau(
        optimizer,
        mode='max',
        factor=0.5,
        patience=3,
        verbose=True
    )

    # Create loss function
    criterion = nn.BCEWithLogitsLoss()

    # Create TensorBoard writer
    writer = SummaryWriter(log_dir=os.path.join(args.output_dir, 'logs'))

    # Training loop
    logger.info("Starting training...")
    best_val_f1 = 0.0

    for epoch in range(args.num_epochs):
        # Training
        model.train()
        train_loss = 0.0
        train_preds = []
        train_labels = []

        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{args.num_epochs} [Train]"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors (simple bag of words for now)
            # This is a temporary solution - a more sophisticated text feature extractor should be used
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D (batch_size, channels, height, width)
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)  # Flatten to (batch_size, channels*height*width)
            else:
                image_features = image

            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            logits = outputs['logits']

            # Compute loss
            loss = criterion(logits, labels)

            # Add redundancy loss if available
            if 'redundancy_score' in outputs:
                redundancy_loss = torch.mean(outputs['redundancy_score'])
                loss += 0.1 * redundancy_loss  # Weight for redundancy loss

            # Backward pass and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # Update statistics
            train_loss += loss.item()
            train_preds.append(torch.sigmoid(logits).detach().cpu().numpy())
            train_labels.append(labels.detach().cpu().numpy())

        # Compute training metrics
        train_preds = np.vstack(train_preds)
        train_labels = np.vstack(train_labels)
        train_metrics = compute_metrics(train_labels, train_preds)

        # Validation
        model.eval()
        val_loss = 0.0
        val_preds = []
        val_labels = []

        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{args.num_epochs} [Val]"):
                # Move batch to device
                image = batch['image'].to(device)
                text = batch['text']
                labels = batch['labels'].to(device)
                kg_features = batch['kg_features'].to(device)
                label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

                # Convert text to feature vectors (simple bag of words for now)
                # This is a temporary solution - a more sophisticated text feature extractor should be used
                text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

                # Flatten image features if they are 4D (batch_size, channels, height, width)
                if len(image.shape) == 4:
                    batch_size, channels, height, width = image.shape
                    image_features = image.view(batch_size, -1)  # Flatten to (batch_size, channels*height*width)
                else:
                    image_features = image

                # Forward pass
                outputs = model(text_features, image_features, kg_features, label_embeddings)
                logits = outputs['logits']

                # Compute loss
                loss = criterion(logits, labels)

                # Update statistics
                val_loss += loss.item()
                val_preds.append(torch.sigmoid(logits).detach().cpu().numpy())
                val_labels.append(labels.detach().cpu().numpy())

        # Compute validation metrics
        val_preds = np.vstack(val_preds)
        val_labels = np.vstack(val_labels)
        val_metrics = compute_metrics(val_labels, val_preds)

        # Update learning rate
        scheduler.step(val_metrics['f1'])

        # Log metrics
        logger.info(f"Epoch {epoch+1}/{args.num_epochs}")
        logger.info(f"Train Loss: {train_loss/len(train_loader):.4f}, "
                   f"F1: {train_metrics['f1']:.4f}, "
                   f"mAP: {train_metrics['mAP']:.4f}")
        logger.info(f"Val Loss: {val_loss/len(val_loader):.4f}, "
                   f"F1: {val_metrics['f1']:.4f}, "
                   f"mAP: {val_metrics['mAP']:.4f}")

        # Write to TensorBoard
        writer.add_scalar('Loss/train', train_loss/len(train_loader), epoch)
        writer.add_scalar('Loss/val', val_loss/len(val_loader), epoch)
        writer.add_scalar('F1/train', train_metrics['f1'], epoch)
        writer.add_scalar('F1/val', val_metrics['f1'], epoch)
        writer.add_scalar('mAP/train', train_metrics['mAP'], epoch)
        writer.add_scalar('mAP/val', val_metrics['mAP'], epoch)

        # Save best model
        if val_metrics['f1'] > best_val_f1:
            best_val_f1 = val_metrics['f1']
            torch.save(model.state_dict(), os.path.join(args.output_dir, 'best_model.pth'))
            logger.info(f"Saved new best model with F1: {best_val_f1:.4f}")

    # Test best model
    logger.info("Testing best model...")
    model.load_state_dict(torch.load(os.path.join(args.output_dir, 'best_model.pth')))
    model.eval()

    test_preds = []
    test_labels = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors (simple bag of words for now)
            # This is a temporary solution - a more sophisticated text feature extractor should be used
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D (batch_size, channels, height, width)
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)  # Flatten to (batch_size, channels*height*width)
            else:
                image_features = image

            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            logits = outputs['logits']

            # Update statistics
            test_preds.append(torch.sigmoid(logits).detach().cpu().numpy())
            test_labels.append(labels.detach().cpu().numpy())

    # Compute test metrics
    test_preds = np.vstack(test_preds)
    test_labels = np.vstack(test_labels)
    test_metrics = compute_metrics(test_labels, test_preds)

    # Log test metrics
    logger.info(f"Test Results:")
    logger.info(f"F1: {test_metrics['f1']:.4f}, "
               f"Precision: {test_metrics['precision']:.4f}, "
               f"Recall: {test_metrics['recall']:.4f}, "
               f"mAP: {test_metrics['mAP']:.4f}")

    # Save test results
    with open(os.path.join(args.output_dir, 'test_results.json'), 'w') as f:
        json.dump(test_metrics, f, indent=2)

    # Close TensorBoard writer
    writer.close()

if __name__ == '__main__':
    args = parse_args()
    train(args)
