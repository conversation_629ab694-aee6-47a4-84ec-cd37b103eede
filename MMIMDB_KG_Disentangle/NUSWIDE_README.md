# NUS-WIDE Experiment for KG-Disentangle-Net

This document describes how to run the Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network (KG-Disentangle-Net) on the NUS-WIDE dataset.

## Dataset

The NUS-WIDE dataset is a web image dataset that includes:
- 269,648 images
- 5,018 unique tags
- 81 concept labels (multi-label classification)

The dataset provides a different domain from MM-IMDB (general images vs. movies) but maintains the same multi-label classification task structure, making it ideal for validating the generalizability of our approach.

## Setup

### Prerequisites

- Python 3.6+
- PyTorch 1.7+
- CUDA-enabled GPU (recommended)

### Directory Structure

The NUS-WIDE dataset should be organized as follows:

```
/home/<USER>/workplace/dwb/data/nuswide/
├── images/                # Image files
├── ImageList/             # List of image paths
│   └── ImageList.txt
├── Tags/                  # Image tags
│   └── AllTags81.txt
└── AllLabels/             # Concept labels
    └── AllLabels.txt
```

## Running the Experiment

### Automatic Setup and Training

To automatically download, prepare the dataset, and run the experiment, use the provided shell script:

```bash
./run_nuswide_experiment.sh
```

This script will:
1. Download and prepare the NUS-WIDE dataset if needed
2. Build the knowledge graph for NUS-WIDE
3. Train the KG-Disentangle-Net model on NUS-WIDE
4. Save results to the output directory

The experiment will run in the background, and logs will be saved to the `logs/` directory.

### Manual Steps

If you prefer to run the steps manually:

1. **Prepare the dataset**:
   ```bash
   python prepare_nuswide.py --output_dir /home/<USER>/workplace/dwb/data/nuswide
   ```

2. **Build the knowledge graph**:
   ```bash
   python run_nuswide.py --mode build_kg --data_path /home/<USER>/workplace/dwb/data/nuswide --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data
   ```

3. **Train the model**:
   ```bash
   python run_nuswide.py --mode train --data_path /home/<USER>/workplace/dwb/data/nuswide --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data --output_dir ./output_nuswide
   ```

4. **Test the model**:
   ```bash
   python run_nuswide.py --mode test --data_path /home/<USER>/workplace/dwb/data/nuswide --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data --output_dir ./output_nuswide/your_experiment_name
   ```

## Comparing Results with MM-IMDB

To compare the results between MM-IMDB and NUS-WIDE, use the comparison script:

```bash
python compare_results.py --mmimdb_metrics ./output/your_mmimdb_experiment/test_metrics.json --nuswide_metrics ./output_nuswide/your_nuswide_experiment/test_metrics.json --output_dir ./comparison_results
```

This will generate:
- Comparison plots for classification metrics
- Comparison plots for disentanglement metrics
- A markdown table with all metrics
- A CSV file with all metrics

## Key Differences from MM-IMDB

The NUS-WIDE implementation differs from MM-IMDB in the following ways:

1. **Dataset Structure**:
   - NUS-WIDE uses tags instead of plot summaries for text features
   - NUS-WIDE has 81 concept classes (vs. 23 genres in MM-IMDB)
   - Images in NUS-WIDE are more diverse (general web images vs. movie posters)

2. **Knowledge Graph**:
   - Different entity types (images, tags, concepts vs. movies, directors, actors, genres)
   - Different relation types (image-tag, image-concept, tag-concept, co-occurrence)

3. **Model Configuration**:
   - Same model architecture but with 81 output classes
   - Same disentanglement approach but applied to a different domain

## Expected Results

The experiment should demonstrate:
1. The effectiveness of our approach on a different domain
2. The generalizability of the knowledge graph-enhanced disentanglement method
3. Comparable disentanglement metrics between MM-IMDB and NUS-WIDE
4. Domain-specific differences in classification performance

## Troubleshooting

- If you encounter memory issues, try reducing the batch size
- If the dataset download fails, you may need to manually download from the NUS-WIDE website
- Check the logs in the `logs/` directory for detailed error messages
