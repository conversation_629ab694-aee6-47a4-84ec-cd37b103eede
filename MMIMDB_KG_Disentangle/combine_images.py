#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import matplotlib.image as mpimg
import numpy as np
import os

def combine_images(label_img_path, text_length_img_path, output_path):
    """
    将标签分布图和文本长度分布图合并到一张图中
    
    参数:
    label_img_path: 标签分布图路径
    text_length_img_path: 文本长度分布图路径
    output_path: 输出图片路径
    """
    # 读取两张图片
    label_img = mpimg.imread(label_img_path)
    text_length_img = mpimg.imread(text_length_img_path)
    
    # 创建一个新的图形，设置大小为宽度足够容纳两张图片
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    # 在左侧子图显示标签分布图
    axes[0].imshow(label_img)
    axes[0].set_title('Label Distribution', fontsize=16)
    axes[0].axis('off')  # 关闭坐标轴
    
    # 在右侧子图显示文本长度分布图
    axes[1].imshow(text_length_img)
    axes[1].set_title('Text Length Distribution', fontsize=16)
    axes[1].axis('off')  # 关闭坐标轴
    
    # 添加总标题
    plt.suptitle('MM-IMDB Dataset Statistics', fontsize=20, y=0.98)
    
    # 调整子图之间的间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    # 保存合并后的图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Combined image saved to {output_path}")
    
    # 显示图片（可选）
    # plt.show()

def combine_images_vertical(label_img_path, text_length_img_path, output_path):
    """
    将标签分布图和文本长度分布图垂直排列合并到一张图中
    
    参数:
    label_img_path: 标签分布图路径
    text_length_img_path: 文本长度分布图路径
    output_path: 输出图片路径
    """
    # 读取两张图片
    label_img = mpimg.imread(label_img_path)
    text_length_img = mpimg.imread(text_length_img_path)
    
    # 创建一个新的图形，设置大小为垂直排列两张图片
    fig, axes = plt.subplots(2, 1, figsize=(12, 16))
    
    # 在上方子图显示标签分布图
    axes[0].imshow(label_img)
    axes[0].set_title('Label Distribution', fontsize=16)
    axes[0].axis('off')  # 关闭坐标轴
    
    # 在下方子图显示文本长度分布图
    axes[1].imshow(text_length_img)
    axes[1].set_title('Text Length Distribution', fontsize=16)
    axes[1].axis('off')  # 关闭坐标轴
    
    # 添加总标题
    plt.suptitle('MM-IMDB Dataset Statistics', fontsize=20, y=0.98)
    
    # 调整子图之间的间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)
    
    # 保存合并后的图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Combined vertical image saved to {output_path}")

def create_combined_plot(label_img_path, text_length_img_path, output_path):
    """
    重新绘制标签分布和文本长度分布图，并合并到一张图中
    
    参数:
    label_img_path: 标签分布图路径（仅用于参考，实际上会重新绘制）
    text_length_img_path: 文本长度分布图路径（仅用于参考，实际上会重新绘制）
    output_path: 输出图片路径
    """
    # 模拟数据 - 实际应用中应该从数据源获取
    # 标签分布数据
    labels = ['Drama', 'Comedy', 'Romance', 'Thriller', 'Action', 'Crime', 
              'Horror', 'Documentary', 'Adventure', 'Sci-Fi', 'Family', 
              'Mystery', 'Fantasy', 'War', 'Animation', 'Music', 'Biography', 
              'History', 'Sport', 'Musical', 'Western', 'Film-Noir', 'News']
    
    label_counts = [14890, 9475, 7478, 6267, 5548, 5236, 4265, 3799, 3729, 
                   2912, 2438, 2310, 1894, 1640, 1444, 1365, 1329, 1135, 
                   922, 651, 513, 330, 110]
    
    # 文本长度分布数据
    text_lengths = np.random.normal(500, 200, 1000)  # 模拟文本长度分布
    
    # 创建一个新的图形
    fig, axes = plt.subplots(1, 2, figsize=(20, 8))
    
    # 绘制标签分布图
    bars = axes[0].bar(range(len(labels)), label_counts, color='skyblue')
    axes[0].set_title('Label Distribution in MM-IMDB Dataset', fontsize=16)
    axes[0].set_xlabel('Categories', fontsize=14)
    axes[0].set_ylabel('Number of Movies', fontsize=14)
    axes[0].set_xticks(range(len(labels)))
    axes[0].set_xticklabels(labels, rotation=90, fontsize=10)
    
    # 在柱状图上添加数值标签
    for bar in bars:
        height = bar.get_height()
        axes[0].text(bar.get_x() + bar.get_width()/2., height + 100,
                    f'{int(height)}', ha='center', va='bottom', fontsize=8)
    
    # 绘制文本长度分布图
    axes[1].hist(text_lengths, bins=30, color='lightgreen', alpha=0.7)
    axes[1].set_title('Text Length Distribution in MM-IMDB Dataset', fontsize=16)
    axes[1].set_xlabel('Text Length (characters)', fontsize=14)
    axes[1].set_ylabel('Frequency', fontsize=14)
    
    # 添加总标题
    plt.suptitle('MM-IMDB Dataset Statistics', fontsize=20, y=0.98)
    
    # 调整子图之间的间距
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    # 保存合并后的图片
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Combined plot saved to {output_path}")

if __name__ == "__main__":
    # 图片路径
    label_img_path = "/home/<USER>/workplace/label_distribution.png"
    text_length_img_path = "/home/<USER>/workplace/text_length_distribution.png"
    
    # 输出路径
    output_dir = "/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/visualizations"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 水平合并图片
    horizontal_output_path = os.path.join(output_dir, "combined_horizontal.png")
    combine_images(label_img_path, text_length_img_path, horizontal_output_path)
    
    # 垂直合并图片
    vertical_output_path = os.path.join(output_dir, "combined_vertical.png")
    combine_images_vertical(label_img_path, text_length_img_path, vertical_output_path)
    
    # 重新绘制并合并图表（如果需要的话）
    # redrawn_output_path = os.path.join(output_dir, "redrawn_combined.png")
    # create_combined_plot(label_img_path, text_length_img_path, redrawn_output_path)
