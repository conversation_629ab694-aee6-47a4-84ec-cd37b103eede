#!/bin/bash

# 运行模型结构分析脚本

# 设置环境变量
export PYTHONPATH=/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle:$PYTHONPATH

# 设置参数
MODEL_PATH="/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/kg_disentangle_v1/best_model.pth"

# 运行分析脚本
echo "开始分析模型结构..."
python /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/analyze_model_structure.py --model_path $MODEL_PATH

# 检查运行结果
if [ $? -eq 0 ]; then
    echo "模型结构分析完成"
    echo "请根据分析结果创建特征提取脚本"
else
    echo "分析失败，请检查错误信息"
fi
