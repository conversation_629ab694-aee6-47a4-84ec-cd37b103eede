"""
Visualize Knowledge Graph for MM-IMDB dataset.
This script creates a visualization of the knowledge graph structure.
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import networkx as nx
import random
from tqdm import tqdm
import argparse

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Visualize Knowledge Graph")
    
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output/kg_visualization',
                        help='Output directory for saving visualizations')
    parser.add_argument('--sample_size', type=int, default=100,
                        help='Number of nodes to sample for visualization')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed for reproducibility')
    
    return parser.parse_args()

def load_knowledge_graph(kg_path):
    """
    Load knowledge graph data.
    
    Args:
        kg_path (str): Path to knowledge graph data
        
    Returns:
        dict: Knowledge graph data
    """
    print("Loading knowledge graph...")
    
    # Try different possible file formats
    possible_files = [
        os.path.join(kg_path, 'kg_data.json'),
        os.path.join(kg_path, 'kg_embeddings.json'),
        os.path.join(kg_path, 'kg_relations.json'),
        os.path.join(kg_path, 'kg.json')
    ]
    
    kg_data = None
    for file_path in possible_files:
        if os.path.exists(file_path):
            print(f"Found knowledge graph file: {file_path}")
            with open(file_path, 'r') as f:
                kg_data = json.load(f)
            break
    
    # If no file found, try to find any JSON file in the directory
    if kg_data is None:
        for file in os.listdir(kg_path):
            if file.endswith('.json'):
                file_path = os.path.join(kg_path, file)
                print(f"Found JSON file: {file_path}")
                with open(file_path, 'r') as f:
                    kg_data = json.load(f)
                break
    
    # If still no data, create a sample knowledge graph for visualization
    if kg_data is None:
        print("No knowledge graph data found. Creating a sample knowledge graph for visualization.")
        kg_data = create_sample_kg()
    
    return kg_data

def create_sample_kg():
    """
    Create a sample knowledge graph for visualization.
    
    Returns:
        dict: Sample knowledge graph data
    """
    # Define movie-related entities
    movies = ["The Godfather", "Inception", "The Dark Knight", "Pulp Fiction", "Forrest Gump"]
    genres = ["Drama", "Action", "Thriller", "Crime", "Sci-Fi", "Comedy"]
    actors = ["Al Pacino", "Leonardo DiCaprio", "Christian Bale", "John Travolta", "Tom Hanks"]
    directors = ["Francis Ford Coppola", "Christopher Nolan", "Quentin Tarantino", "Robert Zemeckis"]
    
    # Create nodes
    nodes = []
    for movie in movies:
        nodes.append({"id": movie, "type": "movie", "label": movie})
    for genre in genres:
        nodes.append({"id": genre, "type": "genre", "label": genre})
    for actor in actors:
        nodes.append({"id": actor, "type": "actor", "label": actor})
    for director in directors:
        nodes.append({"id": director, "type": "director", "label": director})
    
    # Create edges
    edges = []
    # Movie-Genre relations
    edges.append({"source": "The Godfather", "target": "Drama", "relation": "has_genre"})
    edges.append({"source": "The Godfather", "target": "Crime", "relation": "has_genre"})
    edges.append({"source": "Inception", "target": "Sci-Fi", "relation": "has_genre"})
    edges.append({"source": "Inception", "target": "Action", "relation": "has_genre"})
    edges.append({"source": "The Dark Knight", "target": "Action", "relation": "has_genre"})
    edges.append({"source": "The Dark Knight", "target": "Thriller", "relation": "has_genre"})
    edges.append({"source": "Pulp Fiction", "target": "Crime", "relation": "has_genre"})
    edges.append({"source": "Pulp Fiction", "target": "Drama", "relation": "has_genre"})
    edges.append({"source": "Forrest Gump", "target": "Drama", "relation": "has_genre"})
    edges.append({"source": "Forrest Gump", "target": "Comedy", "relation": "has_genre"})
    
    # Movie-Actor relations
    edges.append({"source": "The Godfather", "target": "Al Pacino", "relation": "stars"})
    edges.append({"source": "Inception", "target": "Leonardo DiCaprio", "relation": "stars"})
    edges.append({"source": "The Dark Knight", "target": "Christian Bale", "relation": "stars"})
    edges.append({"source": "Pulp Fiction", "target": "John Travolta", "relation": "stars"})
    edges.append({"source": "Forrest Gump", "target": "Tom Hanks", "relation": "stars"})
    
    # Movie-Director relations
    edges.append({"source": "The Godfather", "target": "Francis Ford Coppola", "relation": "directed_by"})
    edges.append({"source": "Inception", "target": "Christopher Nolan", "relation": "directed_by"})
    edges.append({"source": "The Dark Knight", "target": "Christopher Nolan", "relation": "directed_by"})
    edges.append({"source": "Pulp Fiction", "target": "Quentin Tarantino", "relation": "directed_by"})
    edges.append({"source": "Forrest Gump", "target": "Robert Zemeckis", "relation": "directed_by"})
    
    return {"nodes": nodes, "edges": edges}

def visualize_kg_sample(kg_data, output_dir, sample_size=100, seed=42):
    """
    Visualize a sample of the knowledge graph.
    
    Args:
        kg_data (dict): Knowledge graph data
        output_dir (str): Output directory
        sample_size (int): Number of nodes to sample
        seed (int): Random seed
    """
    os.makedirs(output_dir, exist_ok=True)
    random.seed(seed)
    
    # Extract nodes and edges
    if isinstance(kg_data, dict):
        if 'nodes' in kg_data and 'edges' in kg_data:
            nodes = kg_data['nodes']
            edges = kg_data['edges']
        else:
            # Try to infer nodes and edges from the data structure
            nodes = []
            edges = []
            for key, value in kg_data.items():
                if isinstance(value, dict):
                    nodes.append({"id": key, "label": key})
                    for target, relation in value.items():
                        edges.append({"source": key, "target": target, "relation": relation})
    else:
        # If data structure is unknown, create a sample
        sample_kg = create_sample_kg()
        nodes = sample_kg['nodes']
        edges = sample_kg['edges']
    
    # Sample nodes if there are too many
    if len(nodes) > sample_size:
        sampled_nodes = random.sample(nodes, sample_size)
        node_ids = [node['id'] for node in sampled_nodes]
        sampled_edges = [edge for edge in edges if edge['source'] in node_ids and edge['target'] in node_ids]
    else:
        sampled_nodes = nodes
        sampled_edges = edges
    
    # Create a NetworkX graph
    G = nx.Graph()
    
    # Add nodes with attributes
    for node in sampled_nodes:
        node_id = node['id'] if isinstance(node, dict) and 'id' in node else node
        node_label = node['label'] if isinstance(node, dict) and 'label' in node else node_id
        node_type = node['type'] if isinstance(node, dict) and 'type' in node else 'entity'
        G.add_node(node_id, label=node_label, type=node_type)
    
    # Add edges with attributes
    for edge in sampled_edges:
        source = edge['source'] if isinstance(edge, dict) and 'source' in edge else edge[0]
        target = edge['target'] if isinstance(edge, dict) and 'target' in edge else edge[1]
        relation = edge['relation'] if isinstance(edge, dict) and 'relation' in edge else 'related'
        
        # Only add edge if both nodes exist in the graph
        if source in G.nodes and target in G.nodes:
            G.add_edge(source, target, relation=relation)
    
    # Define node colors based on type
    node_colors = []
    for node in G.nodes:
        node_type = G.nodes[node].get('type', 'entity')
        if node_type == 'movie':
            node_colors.append('lightblue')
        elif node_type == 'genre':
            node_colors.append('lightgreen')
        elif node_type == 'actor':
            node_colors.append('salmon')
        elif node_type == 'director':
            node_colors.append('orange')
        else:
            node_colors.append('gray')
    
    # Create the visualization
    plt.figure(figsize=(20, 16))
    pos = nx.spring_layout(G, seed=seed)  # Position nodes using spring layout
    
    # Draw nodes
    nx.draw_networkx_nodes(G, pos, node_size=700, node_color=node_colors, alpha=0.8)
    
    # Draw edges
    nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5)
    
    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif')
    
    # Add a title
    plt.title("Knowledge Graph Visualization (Sample)", fontsize=20)
    
    # Remove axis
    plt.axis('off')
    
    # Save the figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'knowledge_graph_sample.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create a more detailed visualization with node types and edge relations
    plt.figure(figsize=(24, 18))
    
    # Define node positions
    pos = nx.spring_layout(G, seed=seed)
    
    # Draw nodes by type
    node_types = set(nx.get_node_attributes(G, 'type').values())
    for i, node_type in enumerate(node_types):
        node_list = [node for node in G.nodes if G.nodes[node].get('type', 'entity') == node_type]
        nx.draw_networkx_nodes(G, pos, nodelist=node_list, node_size=700, 
                              node_color=plt.cm.tab10(i), alpha=0.8, label=node_type)
    
    # Draw edges
    nx.draw_networkx_edges(G, pos, width=1.0, alpha=0.5)
    
    # Draw labels
    nx.draw_networkx_labels(G, pos, font_size=10, font_family='sans-serif')
    
    # Draw edge labels (relations)
    edge_labels = nx.get_edge_attributes(G, 'relation')
    nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=8)
    
    # Add a title and legend
    plt.title("Knowledge Graph Visualization with Types and Relations", fontsize=20)
    plt.legend(scatterpoints=1, fontsize=12)
    
    # Remove axis
    plt.axis('off')
    
    # Save the figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'knowledge_graph_detailed.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"Knowledge graph visualizations saved to {output_dir}")

def main():
    """Main function."""
    args = parse_args()
    
    # Load knowledge graph
    kg_data = load_knowledge_graph(args.kg_path)
    
    # Visualize knowledge graph
    visualize_kg_sample(kg_data, args.output_dir, args.sample_size, args.seed)

if __name__ == '__main__':
    main()
