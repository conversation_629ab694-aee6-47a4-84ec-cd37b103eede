"""
<PERSON><PERSON><PERSON> for creating beautiful visualizations of model results and disentanglement metrics.
This script focuses on:
1. Creating high-quality 2D and 3D visualizations of feature spaces
2. Visualizing disentanglement metrics and their relationships
3. Comparing different model configurations
4. Generating publication-ready figures
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import umap
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.gridspec as gridspec
from mpl_toolkits.mplot3d import Axes3D
import pandas as pd
import argparse
from matplotlib.ticker import MaxNLocator
import matplotlib.patches as mpatches
from matplotlib.lines import Line2D
import matplotlib.cm as cm
from matplotlib.animation import FuncAnimation
from matplotlib import animation
import plotly.graph_objects as go
import plotly.express as px
import plotly.io as pio
from plotly.subplots import make_subplots

# Set up beautiful visualization style
plt.style.use('seaborn-v0_8-whitegrid')
custom_params = {
    'axes.spines.right': False,
    'axes.spines.top': False,
    'axes.grid': True,
    'grid.linestyle': '--',
    'grid.alpha': 0.7,
    'font.family': 'sans-serif',
    'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif'],
    'figure.figsize': (12, 8),
    'figure.dpi': 150,
}
plt.rcParams.update(custom_params)

# Custom color palettes
main_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
redundancy_cmap = LinearSegmentedColormap.from_list('redundancy', ['#f0f9e8', '#7bccc4', '#43a2ca', '#0868ac'])
feature_cmap = LinearSegmentedColormap.from_list('features', ['#feebe2', '#fbb4b9', '#f768a1', '#c51b8a', '#7a0177'])

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Visualization for KG-Disentangle-Net results")
    
    parser.add_argument('--results_dir', type=str, required=True,
                        help='Directory containing results JSON files')
    parser.add_argument('--output_dir', type=str, default='./output/visualizations/results',
                        help='Output directory for saving visualizations')
    parser.add_argument('--compare_dirs', type=str, nargs='+', default=None,
                        help='Directories containing results for comparison')
    parser.add_argument('--compare_names', type=str, nargs='+', default=None,
                        help='Names for the comparison models')
    parser.add_argument('--features_path', type=str, default=None,
                        help='Path to saved features for visualization')
    parser.add_argument('--interactive', action='store_true',
                        help='Generate interactive visualizations')
    
    return parser.parse_args()

def load_results(results_path):
    """Load results from a JSON file."""
    with open(results_path, 'r') as f:
        results = json.load(f)
    return results

def visualize_disentanglement_metrics(results, output_dir, name='model'):
    """Visualize disentanglement metrics."""
    # Extract disentanglement metrics
    metrics = {}
    for key, value in results.items():
        if key in ['modality_disentanglement_score', 'cross_modal_redundancy', 
                  'feature_independence', 'shared_information_preservation',
                  'mutual_information', 'redundancy_min', 'redundancy_max', 
                  'redundancy_std']:
            metrics[key] = value
    
    # Create a radar chart
    categories = list(metrics.keys())
    values = list(metrics.values())
    
    # Normalize values to [0, 1] range
    normalized_values = []
    for value in values:
        if value is None:
            normalized_values.append(0)
        else:
            # Most metrics are already in [0, 1] range
            normalized_values.append(min(max(value, 0), 1))
    
    # Create radar chart
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, polar=True)
    
    # Plot metrics
    angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
    values = normalized_values + [normalized_values[0]]
    angles = angles + [angles[0]]
    categories = categories + [categories[0]]
    
    ax.plot(angles, values, 'o-', linewidth=2, color=main_colors[0], label=name)
    ax.fill(angles, values, alpha=0.25, color=main_colors[0])
    
    # Set category labels
    plt.xticks(angles[:-1], categories[:-1], size=12)
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    
    # Add title
    plt.title('Disentanglement Metrics', size=20, pad=20)
    
    # Add legend
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{name}_disentanglement_radar.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create a bar chart for easier comparison
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Plot metrics
    x = np.arange(len(categories[:-1]))
    ax.bar(x, normalized_values, width=0.6, color=main_colors[0], alpha=0.7)
    
    # Set category labels
    plt.xticks(x, categories[:-1], rotation=45, ha='right', size=12)
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    
    # Add title
    plt.title('Disentanglement Metrics', size=20, pad=20)
    
    # Add value labels
    for i, v in enumerate(normalized_values):
        ax.text(i, v + 0.05, f'{v:.2f}', ha='center', va='bottom', size=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{name}_disentanglement_bar.png'), dpi=300, bbox_inches='tight')
    plt.close()

def visualize_classification_metrics(results, output_dir, name='model'):
    """Visualize classification metrics."""
    # Extract classification metrics
    metrics = {}
    for key, value in results.items():
        if key in ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP', 'hamming_accuracy']:
            metrics[key] = value
    
    # Create a bar chart
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # Plot metrics
    categories = list(metrics.keys())
    values = list(metrics.values())
    x = np.arange(len(categories))
    ax.bar(x, values, width=0.6, color=main_colors[1], alpha=0.7)
    
    # Set category labels
    plt.xticks(x, categories, rotation=45, ha='right', size=12)
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    
    # Add title
    plt.title('Classification Metrics', size=20, pad=20)
    
    # Add value labels
    for i, v in enumerate(values):
        ax.text(i, v + 0.05, f'{v:.2f}', ha='center', va='bottom', size=10)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{name}_classification_bar.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Create a radar chart
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, polar=True)
    
    # Plot metrics
    angles = np.linspace(0, 2*np.pi, len(categories), endpoint=False).tolist()
    values = values + [values[0]]
    angles = angles + [angles[0]]
    categories = categories + [categories[0]]
    
    ax.plot(angles, values, 'o-', linewidth=2, color=main_colors[1], label=name)
    ax.fill(angles, values, alpha=0.25, color=main_colors[1])
    
    # Set category labels
    plt.xticks(angles[:-1], categories[:-1], size=12)
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    
    # Add title
    plt.title('Classification Metrics', size=20, pad=20)
    
    # Add legend
    plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f'{name}_classification_radar.png'), dpi=300, bbox_inches='tight')
    plt.close()

def visualize_metrics_comparison(results_list, names, output_dir):
    """Visualize comparison of metrics between different models."""
    # Extract disentanglement metrics
    disentanglement_metrics = ['modality_disentanglement_score', 'cross_modal_redundancy', 
                              'feature_independence', 'shared_information_preservation',
                              'mutual_information']
    
    # Extract classification metrics
    classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP', 'hamming_accuracy']
    
    # Create dataframes for plotting
    disentanglement_data = []
    classification_data = []
    
    for i, (results, name) in enumerate(zip(results_list, names)):
        # Disentanglement metrics
        for metric in disentanglement_metrics:
            if metric in results:
                disentanglement_data.append({
                    'Model': name,
                    'Metric': metric,
                    'Value': results[metric]
                })
        
        # Classification metrics
        for metric in classification_metrics:
            if metric in results:
                classification_data.append({
                    'Model': name,
                    'Metric': metric,
                    'Value': results[metric]
                })
    
    disentanglement_df = pd.DataFrame(disentanglement_data)
    classification_df = pd.DataFrame(classification_data)
    
    # Create grouped bar charts
    # Disentanglement metrics
    plt.figure(figsize=(16, 10))
    ax = sns.barplot(x='Metric', y='Value', hue='Model', data=disentanglement_df, palette=main_colors[:len(names)])
    
    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Disentanglement Metrics Comparison', fontsize=18, pad=20)
    
    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)
    
    # Add legend
    plt.legend(title='Model', fontsize=12, title_fontsize=14)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'disentanglement_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Classification metrics
    plt.figure(figsize=(16, 10))
    ax = sns.barplot(x='Metric', y='Value', hue='Model', data=classification_df, palette=main_colors[:len(names)])
    
    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Classification Metrics Comparison', fontsize=18, pad=20)
    
    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)
    
    # Add legend
    plt.legend(title='Model', fontsize=12, title_fontsize=14)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'classification_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

def create_interactive_visualizations(results_list, names, output_dir):
    """Create interactive visualizations using Plotly."""
    # Extract disentanglement metrics
    disentanglement_metrics = ['modality_disentanglement_score', 'cross_modal_redundancy', 
                              'feature_independence', 'shared_information_preservation',
                              'mutual_information']
    
    # Extract classification metrics
    classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP', 'hamming_accuracy']
    
    # Create dataframes for plotting
    disentanglement_data = []
    classification_data = []
    
    for i, (results, name) in enumerate(zip(results_list, names)):
        # Disentanglement metrics
        for metric in disentanglement_metrics:
            if metric in results:
                disentanglement_data.append({
                    'Model': name,
                    'Metric': metric,
                    'Value': results[metric]
                })
        
        # Classification metrics
        for metric in classification_metrics:
            if metric in results:
                classification_data.append({
                    'Model': name,
                    'Metric': metric,
                    'Value': results[metric]
                })
    
    disentanglement_df = pd.DataFrame(disentanglement_data)
    classification_df = pd.DataFrame(classification_data)
    
    # Create interactive bar charts
    # Disentanglement metrics
    fig = px.bar(disentanglement_df, x='Metric', y='Value', color='Model', barmode='group',
                title='Disentanglement Metrics Comparison',
                labels={'Value': 'Metric Value', 'Metric': 'Metric Name'},
                template='plotly_white')
    
    fig.update_layout(
        title_font_size=24,
        xaxis_title_font_size=18,
        yaxis_title_font_size=18,
        legend_title_font_size=18,
        xaxis_tickfont_size=14,
        yaxis_tickfont_size=14,
        legend_font_size=14
    )
    
    # Save as HTML
    pio.write_html(fig, os.path.join(output_dir, 'disentanglement_comparison_interactive.html'))
    
    # Classification metrics
    fig = px.bar(classification_df, x='Metric', y='Value', color='Model', barmode='group',
                title='Classification Metrics Comparison',
                labels={'Value': 'Metric Value', 'Metric': 'Metric Name'},
                template='plotly_white')
    
    fig.update_layout(
        title_font_size=24,
        xaxis_title_font_size=18,
        yaxis_title_font_size=18,
        legend_title_font_size=18,
        xaxis_tickfont_size=14,
        yaxis_tickfont_size=14,
        legend_font_size=14
    )
    
    # Save as HTML
    pio.write_html(fig, os.path.join(output_dir, 'classification_comparison_interactive.html'))
    
    # Create radar charts
    for i, (results, name) in enumerate(zip(results_list, names)):
        # Disentanglement metrics
        disentanglement_values = []
        for metric in disentanglement_metrics:
            if metric in results:
                disentanglement_values.append(results[metric])
            else:
                disentanglement_values.append(0)
        
        # Add first value to close the loop
        disentanglement_values.append(disentanglement_values[0])
        metrics = disentanglement_metrics + [disentanglement_metrics[0]]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=disentanglement_values,
            theta=metrics,
            fill='toself',
            name=name
        ))
        
        fig.update_layout(
            title=f'Disentanglement Metrics - {name}',
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )
            ),
            showlegend=True
        )
        
        # Save as HTML
        pio.write_html(fig, os.path.join(output_dir, f'{name}_disentanglement_radar_interactive.html'))
        
        # Classification metrics
        classification_values = []
        for metric in classification_metrics:
            if metric in results:
                classification_values.append(results[metric])
            else:
                classification_values.append(0)
        
        # Add first value to close the loop
        classification_values.append(classification_values[0])
        metrics = classification_metrics + [classification_metrics[0]]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=classification_values,
            theta=metrics,
            fill='toself',
            name=name
        ))
        
        fig.update_layout(
            title=f'Classification Metrics - {name}',
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )
            ),
            showlegend=True
        )
        
        # Save as HTML
        pio.write_html(fig, os.path.join(output_dir, f'{name}_classification_radar_interactive.html'))

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load main results
    print(f"Loading results from {args.results_dir}...")
    results_path = os.path.join(args.results_dir, 'enhanced_test_results.json')
    if os.path.exists(results_path):
        main_results = load_results(results_path)
    else:
        # Try to find any JSON file in the directory
        json_files = [f for f in os.listdir(args.results_dir) if f.endswith('.json')]
        if json_files:
            results_path = os.path.join(args.results_dir, json_files[0])
            main_results = load_results(results_path)
        else:
            print(f"No results found in {args.results_dir}")
            return
    
    # Visualize main results
    print("Visualizing main results...")
    visualize_disentanglement_metrics(main_results, args.output_dir, name='main_model')
    visualize_classification_metrics(main_results, args.output_dir, name='main_model')
    
    # Load comparison results if provided
    if args.compare_dirs:
        print("Loading comparison results...")
        comparison_results = []
        
        for compare_dir in args.compare_dirs:
            results_path = os.path.join(compare_dir, 'enhanced_test_results.json')
            if os.path.exists(results_path):
                results = load_results(results_path)
                comparison_results.append(results)
            else:
                # Try to find any JSON file in the directory
                json_files = [f for f in os.listdir(compare_dir) if f.endswith('.json')]
                if json_files:
                    results_path = os.path.join(compare_dir, json_files[0])
                    results = load_results(results_path)
                    comparison_results.append(results)
                else:
                    print(f"No results found in {compare_dir}")
                    comparison_results.append({})
        
        # Set comparison names
        if args.compare_names and len(args.compare_names) == len(args.compare_dirs):
            comparison_names = args.compare_names
        else:
            comparison_names = [f'Model {i+1}' for i in range(len(args.compare_dirs))]
        
        # Visualize comparison results
        print("Visualizing comparison results...")
        for i, (results, name) in enumerate(zip(comparison_results, comparison_names)):
            if results:
                visualize_disentanglement_metrics(results, args.output_dir, name=name)
                visualize_classification_metrics(results, args.output_dir, name=name)
        
        # Visualize metrics comparison
        print("Visualizing metrics comparison...")
        all_results = [main_results] + comparison_results
        all_names = ['Main Model'] + comparison_names
        visualize_metrics_comparison(all_results, all_names, args.output_dir)
        
        # Create interactive visualizations if requested
        if args.interactive:
            print("Creating interactive visualizations...")
            create_interactive_visualizations(all_results, all_names, args.output_dir)
    
    print(f"Visualizations saved to {args.output_dir}")

if __name__ == '__main__':
    main()
