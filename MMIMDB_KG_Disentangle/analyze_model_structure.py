#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析脚本：直接基于最优模型评估知识图谱增强的层级解缠目标
第一步：分析模型结构
"""

import os
import torch
import numpy as np
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='分析模型结构')
    parser.add_argument('--model_path', type=str, 
                        default='/home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/output/kg_disentangle_v1/best_model.pth',
                        help='已训练模型的路径')
    return parser.parse_args()

def load_model(model_path):
    """加载模型并分析其结构"""
    print(f"加载模型: {model_path}")
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return None
    
    try:
        # 直接加载模型状态字典
        state_dict = torch.load(model_path, map_location='cpu')
        
        # 检查加载的对象类型
        if isinstance(state_dict, dict):
            if 'state_dict' in state_dict:
                # 有些模型保存时会将状态字典放在'state_dict'键下
                state_dict = state_dict['state_dict']
                
            # 打印模型结构信息
            print("\n模型包含以下层:")
            for key in state_dict.keys():
                print(f"  - {key}: {state_dict[key].shape}")
            
            # 分析模型结构
            analyze_model_structure(state_dict)
            
            return state_dict
        else:
            print(f"警告: 加载的对象不是字典，而是 {type(state_dict)}")
            return state_dict
    except Exception as e:
        print(f"错误: 加载模型时出现异常: {e}")
        return None

def analyze_model_structure(state_dict):
    """分析模型结构，识别可能的特征提取层"""
    print("\n模型结构分析:")
    
    # 查找可能的编码器层
    encoder_layers = [key for key in state_dict.keys() if 'encoder' in key.lower()]
    if encoder_layers:
        print("\n可能的编码器层:")
        for layer in encoder_layers:
            print(f"  - {layer}: {state_dict[layer].shape}")
    
    # 查找可能的解缠相关层
    disentangle_layers = [key for key in state_dict.keys() if any(term in key.lower() for term in ['disentangle', 'mir', 'emsr', 'imsr', 'specific', 'invariant'])]
    if disentangle_layers:
        print("\n可能的解缠相关层:")
        for layer in disentangle_layers:
            print(f"  - {layer}: {state_dict[layer].shape}")
    
    # 查找可能的知识图谱相关层
    kg_layers = [key for key in state_dict.keys() if any(term in key.lower() for term in ['kg', 'knowledge', 'graph'])]
    if kg_layers:
        print("\n可能的知识图谱相关层:")
        for layer in kg_layers:
            print(f"  - {layer}: {state_dict[layer].shape}")
    
    # 查找可能的融合层
    fusion_layers = [key for key in state_dict.keys() if any(term in key.lower() for term in ['fusion', 'combine', 'merge'])]
    if fusion_layers:
        print("\n可能的融合层:")
        for layer in fusion_layers:
            print(f"  - {layer}: {state_dict[layer].shape}")
    
    # 查找可能的分类器层
    classifier_layers = [key for key in state_dict.keys() if any(term in key.lower() for term in ['classifier', 'fc', 'linear', 'output'])]
    if classifier_layers:
        print("\n可能的分类器层:")
        for layer in classifier_layers:
            print(f"  - {layer}: {state_dict[layer].shape}")
    
    # 推断模型的基本结构
    print("\n推断的模型结构:")
    
    # 推断输入维度
    input_layers = [key for key in state_dict.keys() if 'weight' in key and key.split('.')[0] in ['text_encoder', 'visual_encoder']]
    if input_layers:
        for layer in input_layers:
            print(f"  - 输入层 {layer}: 输入维度 {state_dict[layer].shape[1]}")
    
    # 推断隐藏维度
    hidden_layers = [key for key in state_dict.keys() if 'weight' in key and not key.endswith('output.weight')]
    if hidden_layers:
        hidden_dims = [state_dict[layer].shape[0] for layer in hidden_layers]
        most_common_dim = max(set(hidden_dims), key=hidden_dims.count)
        print(f"  - 最常见的隐藏维度: {most_common_dim}")
    
    # 推断输出维度
    output_layers = [key for key in state_dict.keys() if key.endswith('output.weight') or key.endswith('classifier.weight')]
    if output_layers:
        for layer in output_layers:
            print(f"  - 输出层 {layer}: 输出维度 {state_dict[layer].shape[0]}")
    
    print("\n基于以上分析，请根据您的模型结构调整特征提取方法。")

def main():
    args = parse_args()
    state_dict = load_model(args.model_path)
    
    if state_dict is not None:
        print("\n模型加载成功。请根据上述模型结构信息，创建适当的特征提取脚本。")
        print("下一步: 创建特征提取脚本，提取MIR、EMSR、IMSR等特征，并计算互信息和正交性指标。")
    else:
        print("\n模型加载失败。请检查模型路径是否正确。")

if __name__ == "__main__":
    main()
