# Knowledge Graph Enhanced Cross-Modal Semantic Disentanglement Network

This project addresses cross-modal semantic entanglement in multimodal multi-label classification by incorporating knowledge graphs. It builds upon the approach in the paper "Incorporating Domain Knowledge Graph into Multimodal Movie Genre Classification with Self-Supervised Attention and Contrastive Learning" but focuses specifically on addressing cross-modal redundancy.

## Problem Statement

Cross-modal semantic entanglement is defined as the semantic redundancy that remains undetected during the hierarchical decoupling process of modality-invariant representations, effective modality-specific representations, and invalid modality-specific representations. This entanglement manifests as feature components in different modality-specific effective representation spaces that are highly coupled with modality-invariant semantics. This phenomenon severely constrains the model's unified semantic understanding of multimodal data and reduces the accuracy of capturing cross-modal consistent semantics.

## Key Innovations

Our approach addresses three key challenges:

1. **Cross-Modal Semantic Redundancy Detection**: We design a mechanism to dynamically identify and quantify semantic redundancy between modality-invariant and modality-specific representations using attention-based redundancy detection.

2. **Knowledge Graph-Enhanced Disentanglement**: We develop a knowledge graph integration method that helps separate redundant information through a graph-based reasoning module that provides structural guidance for disentanglement.

3. **Adaptive Modality Fusion with Redundancy Suppression**: We implement an adaptive fusion mechanism that can suppress redundant information during multimodal integration using a gating mechanism that controls information flow based on redundancy scores.

## Project Structure

```
MMIMDB_KG_Disentangle/
├── configs/
│   └── config.py              # Configuration parameters
├── data/                      # Data directory (symlink to actual data)
├── kg_data/                   # Knowledge graph data
├── models/
│   ├── base_model.py          # Base model architecture
│   └── kg_disentangle_net.py  # Main model implementation
├── utils/
│   ├── dataset.py             # Dataset loader
│   ├── kg_constructor.py      # Knowledge graph constructor
│   └── losses.py              # Loss functions and metrics
├── train.py                   # Training script
├── run.py                     # Main script to run experiments
└── README.md                  # This file
```

## Dataset

We use the MM-IMDB dataset, which contains multimodal information (text and images) about movies along with their genres (multi-label classification). The dataset is located at `/home/<USER>/workplace/dwb/data/imdb`.

## Knowledge Graph Construction

We construct a knowledge graph from the MM-IMDB dataset metadata, similar to the approach in the IDKG paper but with enhancements for cross-modal redundancy analysis. The knowledge graph includes entities such as movies, directors, actors, and genres, and relations between them.

## Model Architecture

Our model, KGDisentangleNet, consists of the following components:

1. **Feature Encoders**: Encode text, visual, and knowledge graph features into a common representation space.

2. **Redundancy Detection Module**: Detects semantic redundancy between modalities using cross-attention and a redundancy estimator.

3. **Graph Reasoning Module**: Uses knowledge graph embeddings to guide the disentanglement process through graph attention.

4. **Adaptive Fusion Module**: Fuses modality-specific features with redundancy suppression using a gating mechanism.

5. **Enhanced Classifier**: Classifies the fused features into movie genres with label-aware attention.

## Training

To train the model, run:

```bash
python run.py --mode train --data_path /home/<USER>/workplace/dwb/data/imdb --output_dir ./output
```

## Building Knowledge Graph

To build the knowledge graph separately, run:

```bash
python run.py --mode build_kg --data_path /home/<USER>/workplace/dwb/data/imdb --kg_path ./kg_data
```

## Testing

To test a trained model, run:

```bash
python run.py --mode test --exp_name your_experiment_name
```

## Requirements

- Python 3.7+
- PyTorch 1.7+
- torchvision
- numpy
- scikit-learn
- tqdm
- tensorboardX
- PIL

## Acknowledgements

This project builds upon the approach in the paper "Incorporating Domain Knowledge Graph into Multimodal Movie Genre Classification with Self-Supervised Attention and Contrastive Learning" but focuses specifically on addressing cross-modal redundancy.
