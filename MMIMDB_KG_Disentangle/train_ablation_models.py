"""
Script to train ablation models for the KGDisentangleNet.
This script trains different model variants to evaluate the contribution of each component.
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import argparse
from torch.utils.data import DataLoader
import logging
from tabulate import tabulate
import pandas as pd
import sklearn.metrics

# Import project modules
from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
from models.kg_disentangle_ablation import (
    KGDisentangleNetAblation,
    KGDisentangleNetNoRedundancy,
    KGDisentangleNetNoGraphReasoning
)
from enhanced_evaluate import compute_disentanglement_metrics

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Train Ablation Models for KG-Disentangle-Net")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--output_dir', type=str, default='./output/ablation_models',
                        help='Output directory for saving results')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Training arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for training and evaluation')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5,
                        help='Weight decay')
    parser.add_argument('--num_epochs', type=int, default=10,
                        help='Number of epochs')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')

    # Experiment arguments
    parser.add_argument('--model_variants', type=str, nargs='+',
                        default=['full', 'combined', 'no_redundancy', 'no_graph_reasoning'],
                        help='Model variants to train and evaluate')
    parser.add_argument('--skip_training', action='store_true',
                        help='Skip training and only evaluate existing models')
    parser.add_argument('--skip_variants', type=str, nargs='+', default=[],
                        help='Skip these variants (useful if some are already trained)')

    return parser.parse_args()

def compute_metrics(y_true, y_pred):
    """
    Compute standard metrics for multi-label classification.

    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities

    Returns:
        dict: Dictionary of metrics
    """
    # Convert predictions to binary using 0.5 threshold
    y_pred_binary = (y_pred > 0.5).astype(int)

    # Compute metrics
    f1_micro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='micro')
    f1_macro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='macro')
    f1_samples = sklearn.metrics.f1_score(y_true, y_pred_binary, average='samples')
    precision = sklearn.metrics.precision_score(y_true, y_pred_binary, average='micro')
    recall = sklearn.metrics.recall_score(y_true, y_pred_binary, average='micro')

    # Compute mAP
    ap_scores = []
    for i in range(y_true.shape[1]):
        if np.sum(y_true[:, i]) > 0:  # Only compute AP if there are positive samples
            ap = sklearn.metrics.average_precision_score(y_true[:, i], y_pred[:, i])
            ap_scores.append(ap)
    mAP = np.mean(ap_scores) if ap_scores else 0.0

    return {
        'f1_micro': f1_micro,
        'f1_macro': f1_macro,
        'f1': f1_samples,  # This is the sample-averaged F1 score
        'precision': precision,
        'recall': recall,
        'mAP': mAP
    }

def create_model(variant, args):
    """
    Create a model variant based on the specified name.

    Args:
        variant (str): Model variant name
        args (argparse.Namespace): Command line arguments

    Returns:
        torch.nn.Module: Model instance
    """
    if variant == 'full':
        return KGDisentangleNet(
            text_dim=args.text_dim,
            visual_dim=args.visual_dim,
            kg_dim=args.kg_dim,
            hidden_dim=args.hidden_dim,
            num_classes=args.num_classes
        )
    elif variant == 'combined':
        return KGDisentangleNetAblation(
            text_dim=args.text_dim,
            visual_dim=args.visual_dim,
            kg_dim=args.kg_dim,
            hidden_dim=args.hidden_dim,
            num_classes=args.num_classes
        )
    elif variant == 'no_redundancy':
        return KGDisentangleNetNoRedundancy(
            text_dim=args.text_dim,
            visual_dim=args.visual_dim,
            kg_dim=args.kg_dim,
            hidden_dim=args.hidden_dim,
            num_classes=args.num_classes
        )
    elif variant == 'no_graph_reasoning':
        return KGDisentangleNetNoGraphReasoning(
            text_dim=args.text_dim,
            visual_dim=args.visual_dim,
            kg_dim=args.kg_dim,
            hidden_dim=args.hidden_dim,
            num_classes=args.num_classes
        )
    else:
        raise ValueError(f"Unknown model variant: {variant}")

def train_model(model, train_loader, val_loader, args, variant_name):
    """
    Train a model variant.

    Args:
        model (torch.nn.Module): Model to train
        train_loader (torch.utils.data.DataLoader): Training data loader
        val_loader (torch.utils.data.DataLoader): Validation data loader
        args (argparse.Namespace): Command line arguments
        variant_name (str): Name of the model variant

    Returns:
        torch.nn.Module: Trained model
    """
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    # Create output directory
    output_dir = os.path.join(args.output_dir, variant_name)
    os.makedirs(output_dir, exist_ok=True)

    # Set up optimizer
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=args.learning_rate,
        weight_decay=args.weight_decay
    )

    # Set up loss function
    criterion = torch.nn.BCEWithLogitsLoss()

    # Training loop
    best_val_f1 = 0.0
    best_model_state = None

    for epoch in range(args.num_epochs):
        # Training
        model.train()
        train_loss = 0.0

        for batch in tqdm(train_loader, desc=f"Epoch {epoch+1}/{args.num_epochs} - Training"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image

            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            logits = outputs['logits']

            # Compute loss
            loss = criterion(logits, labels)

            # Backward pass and optimize
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            train_loss += loss.item()

        train_loss /= len(train_loader)

        # Validation
        model.eval()
        val_preds = []
        val_labels = []

        with torch.no_grad():
            for batch in tqdm(val_loader, desc=f"Epoch {epoch+1}/{args.num_epochs} - Validation"):
                # Move batch to device
                image = batch['image'].to(device)
                text = batch['text']
                labels = batch['labels'].to(device)
                kg_features = batch['kg_features'].to(device)
                label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

                # Convert text to feature vectors
                text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

                # Flatten image features if they are 4D
                if len(image.shape) == 4:
                    batch_size, channels, height, width = image.shape
                    image_features = image.view(batch_size, -1)
                else:
                    image_features = image

                # Forward pass
                outputs = model(text_features, image_features, kg_features, label_embeddings)
                logits = outputs['logits']

                # Collect predictions and labels
                val_preds.append(torch.sigmoid(logits).cpu().numpy())
                val_labels.append(labels.cpu().numpy())

        # Compute metrics
        val_preds = np.vstack(val_preds)
        val_labels = np.vstack(val_labels)

        val_metrics = compute_metrics(val_labels, val_preds)
        val_f1 = val_metrics['f1']

        # Log metrics
        logger.info(f"Epoch {epoch+1}/{args.num_epochs} - Train Loss: {train_loss:.4f}, Val F1: {val_f1:.4f}")

        # Save best model
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_model_state = model.state_dict()
            torch.save(best_model_state, os.path.join(output_dir, 'best_model.pth'))
            logger.info(f"Saved new best model with F1: {best_val_f1:.4f}")

    # Load best model
    model.load_state_dict(best_model_state)

    # Save final model
    torch.save(model.state_dict(), os.path.join(output_dir, 'final_model.pth'))

    return model

def evaluate_model(model, test_loader, args, variant_name):
    """
    Evaluate a model variant.

    Args:
        model (torch.nn.Module): Model to evaluate
        test_loader (torch.utils.data.DataLoader): Test data loader
        args (argparse.Namespace): Command line arguments
        variant_name (str): Name of the model variant

    Returns:
        dict: Evaluation metrics
    """
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    model.eval()

    # Create output directory
    output_dir = os.path.join(args.output_dir, variant_name)
    os.makedirs(output_dir, exist_ok=True)

    # Evaluation
    test_preds = []
    test_labels = []
    all_text_encoded = []
    all_visual_encoded = []
    all_text_refined = []
    all_visual_refined = []
    all_redundancy_scores = []

    with torch.no_grad():
        for batch in tqdm(test_loader, desc=f"Evaluating {variant_name}"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image

            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            logits = outputs['logits']

            # Collect predictions and labels
            test_preds.append(torch.sigmoid(logits).cpu().numpy())
            test_labels.append(labels.cpu().numpy())

            # Collect features for disentanglement metrics
            all_text_encoded.append(outputs['text_encoded'].cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].cpu().numpy())

            # Collect refined features if available
            if 'text_refined' in outputs:
                all_text_refined.append(outputs['text_refined'].cpu().numpy())
            if 'visual_refined' in outputs:
                all_visual_refined.append(outputs['visual_refined'].cpu().numpy())

    # Stack all predictions and labels
    test_preds = np.vstack(test_preds)
    test_labels = np.vstack(test_labels)

    # Stack all features
    all_text_encoded = np.vstack(all_text_encoded)
    all_visual_encoded = np.vstack(all_visual_encoded)
    all_redundancy_scores = np.vstack(all_redundancy_scores)

    # Stack refined features if available
    text_refined = None
    visual_refined = None
    if all_text_refined and all_visual_refined:
        text_refined = np.vstack(all_text_refined)
        visual_refined = np.vstack(all_visual_refined)

    # Compute standard metrics
    standard_metrics = compute_metrics(test_labels, test_preds)

    # Compute disentanglement metrics with refined features if available
    if text_refined is not None and visual_refined is not None:
        disentanglement_metrics = compute_disentanglement_metrics(
            all_text_encoded, all_visual_encoded, all_redundancy_scores,
            text_refined, visual_refined
        )
    else:
        disentanglement_metrics = compute_disentanglement_metrics(
            all_text_encoded, all_visual_encoded, all_redundancy_scores
        )

    # Combine all metrics
    all_metrics = {**standard_metrics, **disentanglement_metrics}

    # Log metrics
    logger.info(f"\nTest Results for {variant_name}:")
    logger.info(f"F1 (Samples): {all_metrics['f1']:.4f}")
    logger.info(f"F1-Micro: {all_metrics['f1_micro']:.4f}")
    logger.info(f"F1-Macro: {all_metrics['f1_macro']:.4f}")
    logger.info(f"Precision: {all_metrics['precision']:.4f}")
    logger.info(f"Recall: {all_metrics['recall']:.4f}")
    logger.info(f"mAP: {all_metrics['mAP']:.4f}")

    logger.info("\n=== Disentanglement Metrics ===")
    logger.info(f"Modality Disentanglement Score: {all_metrics['modality_disentanglement_score']:.4f}")
    logger.info(f"Cross-Modal Redundancy: {all_metrics['cross_modal_redundancy']:.4f}")
    logger.info(f"Feature Independence: {all_metrics['feature_independence']:.4f}")

    # Modality Specificity
    if 'modality_specificity' in all_metrics:
        logger.info(f"\nModality Specificity: {all_metrics['modality_specificity']:.4f}")
        logger.info(f"  - Text Specificity: {all_metrics['text_specificity']:.4f}")
        logger.info(f"  - Visual Specificity: {all_metrics['visual_specificity']:.4f}")

    # Information Metrics
    logger.info(f"\nInformation Metrics:")
    logger.info(f"Shared Information Preservation: {all_metrics['shared_information_preservation']:.4f}")
    logger.info(f"Mutual Information: {all_metrics['mutual_information']:.4f}")

    # Cross-Modal Transfer
    if 'text_to_image_transfer' in all_metrics:
        logger.info(f"\nCross-Modal Transfer:")
        logger.info(f"Text → Image Transfer: {all_metrics['text_to_image_transfer']:.4f}")
        logger.info(f"Image → Text Transfer: {all_metrics['image_to_text_transfer']:.4f}")

    # Redundancy Statistics
    logger.info(f"\nRedundancy Statistics:")
    logger.info(f"Min: {all_metrics['redundancy_min']:.4f}")
    logger.info(f"Max: {all_metrics['redundancy_max']:.4f}")
    logger.info(f"Std: {all_metrics['redundancy_std']:.4f}")

    # Print refinement metrics if available
    if 'text_refinement_magnitude' in all_metrics:
        logger.info("\n=== Refinement Effect Metrics ===")
        logger.info(f"Refinement Magnitude:")
        logger.info(f"  - Text: {all_metrics['text_refinement_magnitude']:.4f}")
        logger.info(f"  - Visual: {all_metrics['visual_refinement_magnitude']:.4f}")

        logger.info(f"\nRedundancy Effect:")
        logger.info(f"  - Text: {all_metrics['text_redundancy_effect']:.4f}")
        logger.info(f"  - Visual: {all_metrics['visual_redundancy_effect']:.4f}")

        logger.info(f"\nFeature Independence:")
        logger.info(f"  - Before Refinement: {all_metrics['feature_independence']:.4f}")
        logger.info(f"  - After Refinement: {all_metrics['refined_feature_independence']:.4f}")
        logger.info(f"  - Improvement: {all_metrics['independence_improvement']:.4f}")

        if 'refined_modality_specificity' in all_metrics:
            logger.info(f"\nModality Specificity After Refinement:")
            logger.info(f"  - Text: {all_metrics['refined_text_specificity']:.4f} (Δ: {all_metrics['text_specificity_improvement']:.4f})")
            logger.info(f"  - Visual: {all_metrics['refined_visual_specificity']:.4f} (Δ: {all_metrics['visual_specificity_improvement']:.4f})")
            logger.info(f"  - Overall: {all_metrics['refined_modality_specificity']:.4f}")

    # Convert NumPy values to Python native types for JSON serialization
    json_safe_metrics = {}
    for key, value in all_metrics.items():
        if isinstance(value, np.ndarray):
            json_safe_metrics[key] = value.tolist()
        elif isinstance(value, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                               np.uint8, np.uint16, np.uint32, np.uint64)):
            json_safe_metrics[key] = int(value)
        elif isinstance(value, (np.float_, np.float16, np.float32, np.float64)):
            json_safe_metrics[key] = float(value)
        elif value is np.nan:
            json_safe_metrics[key] = None
        else:
            json_safe_metrics[key] = value

    # Save metrics
    with open(os.path.join(output_dir, 'test_results.json'), 'w') as f:
        json.dump(json_safe_metrics, f, indent=2)

    return all_metrics

def visualize_comparison(results, output_dir):
    """
    Visualize comparison of different model variants.

    Args:
        results (dict): Dictionary of results for each variant
        output_dir (str): Output directory
    """
    os.makedirs(output_dir, exist_ok=True)

    # Define model variants and their descriptions
    variant_descriptions = {
        'full': 'Full model with all components',
        'combined': 'Model with combined redundancy detection and graph reasoning',
        'no_redundancy': 'Model without redundancy detection module',
        'no_graph_reasoning': 'Model without knowledge graph reasoning module'
    }

    # Extract classification metrics
    classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP']

    # Create dataframe for classification metrics
    classification_data = []
    for variant, metrics in results.items():
        for metric in classification_metrics:
            if metric in metrics:
                classification_data.append({
                    'Variant': variant_descriptions.get(variant, variant),
                    'Metric': metric,
                    'Value': metrics[metric]
                })

    classification_df = pd.DataFrame(classification_data)

    # Create bar chart for classification metrics
    plt.figure(figsize=(15, 10))
    ax = sns.barplot(x='Metric', y='Value', hue='Variant', data=classification_df)

    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Classification Metrics Comparison', fontsize=18, pad=20)

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)

    # Add legend
    plt.legend(title='Variant', fontsize=12, title_fontsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'classification_metrics_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # Extract disentanglement metrics
    disentanglement_metrics = [
        'modality_disentanglement_score',
        'cross_modal_redundancy',
        'feature_independence',
        'modality_specificity',
        'text_specificity',
        'visual_specificity',
        'shared_information_preservation',
        'mutual_information',
        'text_to_image_transfer',
        'image_to_text_transfer'
    ]

    # Create dataframe for disentanglement metrics
    disentanglement_data = []
    for variant, metrics in results.items():
        for metric in disentanglement_metrics:
            if metric in metrics:
                disentanglement_data.append({
                    'Variant': variant_descriptions.get(variant, variant),
                    'Metric': metric,
                    'Value': metrics[metric]
                })

    disentanglement_df = pd.DataFrame(disentanglement_data)

    # Create bar chart for disentanglement metrics
    plt.figure(figsize=(18, 12))
    ax = sns.barplot(x='Metric', y='Value', hue='Variant', data=disentanglement_df)

    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Disentanglement Metrics Comparison', fontsize=18, pad=20)

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)

    # Add legend
    plt.legend(title='Variant', fontsize=12, title_fontsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'disentanglement_metrics_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # Create comparison table
    table_data = []
    headers = ['Metric'] + [variant_descriptions.get(v, v) for v in results.keys()]

    # Classification metrics
    table_data.append(["=== Classification Metrics ==="])
    for metric in classification_metrics:
        row = [metric]
        for variant in results.keys():
            if metric in results[variant]:
                row.append(f"{results[variant][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)

    # Disentanglement metrics
    table_data.append([""])
    table_data.append(["=== Disentanglement Metrics ==="])
    for metric in disentanglement_metrics:
        row = [metric]
        for variant in results.keys():
            if metric in results[variant]:
                row.append(f"{results[variant][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)

    # Print comparison table
    logger.info("\nMetrics Comparison:")
    logger.info("\n" + tabulate(table_data, headers=headers, tablefmt='grid'))

    # Save comparison table
    with open(os.path.join(output_dir, 'metrics_comparison.txt'), 'w') as f:
        f.write(tabulate(table_data, headers=headers, tablefmt='grid'))

    # Save comparison table as CSV
    with open(os.path.join(output_dir, 'metrics_comparison.csv'), 'w') as f:
        f.write(','.join(['Metric'] + [variant_descriptions.get(v, v) for v in results.keys()]) + '\n')
        for row in table_data:
            if len(row) == 1:  # Section header
                f.write(f"{row[0]}\n")
            else:
                f.write(','.join([str(cell) for cell in row]) + '\n')

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Create datasets
    logger.info("Creating datasets...")
    train_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='train'
    )

    val_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='val'
    )

    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Define model variants and their descriptions
    variant_descriptions = {
        'full': 'Full model with all components',
        'combined': 'Model with combined redundancy detection and graph reasoning',
        'no_redundancy': 'Model without redundancy detection module',
        'no_graph_reasoning': 'Model without knowledge graph reasoning module'
    }

    # Train and evaluate models
    results = {}

    for variant in args.model_variants:
        if variant in args.skip_variants:
            logger.info(f"Skipping variant: {variant}")
            continue

        if variant not in variant_descriptions:
            logger.warning(f"Unknown model variant: {variant}. Skipping.")
            continue

        logger.info(f"\nProcessing model variant: {variant} - {variant_descriptions[variant]}")

        # Create model
        model = create_model(variant, args)

        # Train model if not skipping training
        if not args.skip_training:
            logger.info(f"Training {variant} model...")
            model = train_model(model, train_loader, val_loader, args, variant)
        else:
            # Load pre-trained model
            model_path = os.path.join(args.output_dir, variant, 'best_model.pth')
            if os.path.exists(model_path):
                logger.info(f"Loading pre-trained {variant} model from {model_path}...")
                model.load_state_dict(torch.load(model_path, map_location=device))
            else:
                logger.warning(f"Pre-trained model not found at {model_path}. Skipping evaluation.")
                continue

        # Evaluate model
        logger.info(f"Evaluating {variant} model...")
        metrics = evaluate_model(model, test_loader, args, variant)
        results[variant] = metrics

        # Add variant description to metrics
        results[variant]['description'] = variant_descriptions[variant]

    # Visualize comparison if multiple variants were evaluated
    if len(results) > 1:
        logger.info("Visualizing comparison...")
        visualize_comparison(results, os.path.join(args.output_dir, 'comparison'))

        # Print summary table
        logger.info("\n=== Ablation Experiment Summary ===")
        for variant, metrics in results.items():
            logger.info(f"\n{variant}: {metrics.get('description', '')}")
            logger.info(f"  F1-Micro: {metrics['f1_micro']:.4f}")
            logger.info(f"  F1-Macro: {metrics['f1_macro']:.4f}")
            logger.info(f"  Modality Disentanglement Score: {metrics['modality_disentanglement_score']:.4f}")
            logger.info(f"  Feature Independence: {metrics['feature_independence']:.4f}")
            if 'modality_specificity' in metrics:
                logger.info(f"  Modality Specificity: {metrics['modality_specificity']:.4f}")
            logger.info(f"  Shared Information Preservation: {metrics['shared_information_preservation']:.4f}")

    # Save results to a separate JSON file for easy access
    with open(os.path.join(args.output_dir, 'ablation_results.json'), 'w') as f:
        # Convert results to JSON-serializable format
        json_results = {}
        for variant, metrics in results.items():
            json_results[variant] = {}
            for key, value in metrics.items():
                if isinstance(value, np.ndarray):
                    json_results[variant][key] = value.tolist()
                elif isinstance(value, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                                       np.uint8, np.uint16, np.uint32, np.uint64)):
                    json_results[variant][key] = int(value)
                elif isinstance(value, (np.float_, np.float16, np.float32, np.float64)):
                    json_results[variant][key] = float(value)
                elif value is np.nan:
                    json_results[variant][key] = None
                else:
                    json_results[variant][key] = value

        json.dump(json_results, f, indent=2)

    logger.info("\nDone! Results saved to ablation_results.json")

if __name__ == '__main__':
    main()
