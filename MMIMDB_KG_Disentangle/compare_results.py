"""
Sc<PERSON>t to compare results between MM_IMDB and NUS-WIDE datasets.
"""

import os
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tabulate import tabulate
import pandas as pd

def load_metrics(metrics_file):
    """Load metrics from a JSON file."""
    with open(metrics_file, 'r') as f:
        return json.load(f)

def plot_metrics_comparison(mmimdb_metrics, nuswide_metrics, output_dir):
    """Plot comparison of metrics between MM_IMDB and NUS-WIDE."""
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Set style
    sns.set(style="whitegrid")
    plt.figure(figsize=(12, 8))
    
    # Extract common metrics
    common_metrics = ['precision_micro', 'precision_macro', 'recall_micro', 'recall_macro', 
                      'f1_micro', 'f1_macro', 'mAP']
    
    # Prepare data for plotting
    metrics_data = {
        'Metric': [],
        'Value': [],
        'Dataset': []
    }
    
    for metric in common_metrics:
        if metric in mmimdb_metrics:
            metrics_data['Metric'].append(metric)
            metrics_data['Value'].append(mmimdb_metrics[metric])
            metrics_data['Dataset'].append('MM_IMDB')
        
        if metric in nuswide_metrics:
            metrics_data['Metric'].append(metric)
            metrics_data['Value'].append(nuswide_metrics[metric])
            metrics_data['Dataset'].append('NUS-WIDE')
    
    # Create DataFrame
    df = pd.DataFrame(metrics_data)
    
    # Plot
    plt.figure(figsize=(12, 8))
    ax = sns.barplot(x='Metric', y='Value', hue='Dataset', data=df)
    plt.title('Performance Comparison: MM_IMDB vs NUS-WIDE', fontsize=16)
    plt.xlabel('Metric', fontsize=14)
    plt.ylabel('Value', fontsize=14)
    plt.xticks(rotation=45)
    plt.ylim(0, 1.0)
    plt.tight_layout()
    
    # Save plot
    plt.savefig(os.path.join(output_dir, 'metrics_comparison.png'), dpi=300)
    plt.close()
    
    # Plot disentanglement metrics if available
    disentanglement_metrics = [
        'modality_disentanglement_score', 'feature_independence', 
        'shared_information_preservation', 'modality_specificity_text',
        'modality_specificity_visual', 'text_to_image_transfer',
        'image_to_text_transfer'
    ]
    
    disentanglement_data = {
        'Metric': [],
        'Value': [],
        'Dataset': []
    }
    
    for metric in disentanglement_metrics:
        if metric in mmimdb_metrics:
            disentanglement_data['Metric'].append(metric)
            disentanglement_data['Value'].append(mmimdb_metrics[metric])
            disentanglement_data['Dataset'].append('MM_IMDB')
        
        if metric in nuswide_metrics:
            disentanglement_data['Metric'].append(metric)
            disentanglement_data['Value'].append(nuswide_metrics[metric])
            disentanglement_data['Dataset'].append('NUS-WIDE')
    
    if disentanglement_data['Metric']:
        # Create DataFrame
        df_disent = pd.DataFrame(disentanglement_data)
        
        # Plot
        plt.figure(figsize=(14, 8))
        ax = sns.barplot(x='Metric', y='Value', hue='Dataset', data=df_disent)
        plt.title('Disentanglement Metrics Comparison: MM_IMDB vs NUS-WIDE', fontsize=16)
        plt.xlabel('Metric', fontsize=14)
        plt.ylabel('Value', fontsize=14)
        plt.xticks(rotation=45)
        plt.ylim(0, 1.0)
        plt.tight_layout()
        
        # Save plot
        plt.savefig(os.path.join(output_dir, 'disentanglement_comparison.png'), dpi=300)
        plt.close()
    
    # Create comparison table
    table_data = []
    all_metrics = list(set(list(mmimdb_metrics.keys()) + list(nuswide_metrics.keys())))
    all_metrics.sort()
    
    for metric in all_metrics:
        mmimdb_value = mmimdb_metrics.get(metric, 'N/A')
        nuswide_value = nuswide_metrics.get(metric, 'N/A')
        
        if isinstance(mmimdb_value, float):
            mmimdb_value = f"{mmimdb_value:.4f}"
        if isinstance(nuswide_value, float):
            nuswide_value = f"{nuswide_value:.4f}"
        
        table_data.append([metric, mmimdb_value, nuswide_value])
    
    # Save table as markdown
    table_md = tabulate(table_data, headers=['Metric', 'MM_IMDB', 'NUS-WIDE'], tablefmt='pipe')
    with open(os.path.join(output_dir, 'metrics_comparison.md'), 'w') as f:
        f.write("# Metrics Comparison: MM_IMDB vs NUS-WIDE\n\n")
        f.write(table_md)
    
    # Save table as CSV
    df_table = pd.DataFrame(table_data, columns=['Metric', 'MM_IMDB', 'NUS-WIDE'])
    df_table.to_csv(os.path.join(output_dir, 'metrics_comparison.csv'), index=False)
    
    print(f"Comparison results saved to {output_dir}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Compare results between MM_IMDB and NUS-WIDE")
    parser.add_argument('--mmimdb_metrics', type=str, required=True,
                        help='Path to MM_IMDB test metrics JSON file')
    parser.add_argument('--nuswide_metrics', type=str, required=True,
                        help='Path to NUS-WIDE test metrics JSON file')
    parser.add_argument('--output_dir', type=str, default='./comparison_results',
                        help='Output directory for comparison results')
    args = parser.parse_args()
    
    # Load metrics
    mmimdb_metrics = load_metrics(args.mmimdb_metrics)
    nuswide_metrics = load_metrics(args.nuswide_metrics)
    
    # Plot comparison
    plot_metrics_comparison(mmimdb_metrics, nuswide_metrics, args.output_dir)

if __name__ == '__main__':
    main()
