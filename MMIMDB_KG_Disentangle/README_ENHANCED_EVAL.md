# Enhanced Evaluation for KG-Disentangle-Net

This README explains how to use the enhanced evaluation scripts to get additional metrics for the KG-Disentangle-Net model.

## Overview

The enhanced evaluation adds the following metrics to the standard evaluation:

1. **F1-Micro and F1-Macro**: Additional F1 score calculations that provide different perspectives on model performance:
   - F1-Micro: Calculates metrics globally by counting the total true positives, false negatives, and false positives
   - F1-Macro: Calculates metrics for each label, and finds their unweighted mean

2. **Feature Disentanglement Metrics**: Measures how well the model separates modality-specific and modality-invariant information:
   - Modality Disentanglement Score: How well the model separates information between modalities
   - Feature Independence: How independent the features from different modalities are
   - Shared Information Preservation: How well shared information is preserved across modalities
   - Modality Specificity: How well each modality preserves its unique information
   - Cross-Modal Transfer: How well one modality can predict the other

## Usage

### Option 1: Using the Shell Script

The easiest way to run the enhanced evaluation is to use the provided shell script:

```bash
./run_enhanced_eval.sh --exp_name <experiment_name> [--output_dir <output_dir>] [--device <cuda|cpu>]
```

For example:

```bash
./run_enhanced_eval.sh --exp_name kg_disentangle_20250518_112233 --device cuda
```

### Option 2: Using Python Directly

You can also run the enhanced evaluation directly using Python:

```bash
python run_enhanced_eval.py --exp_name <experiment_name> --output_dir <output_dir> --device <device>
```

For example:

```bash
python run_enhanced_eval.py --exp_name kg_disentangle_20250518_112233 --output_dir ./output --device cuda
```

### Option 3: Using the Enhanced Evaluation Module Directly

If you need more control over the evaluation parameters, you can use the enhanced evaluation module directly:

```bash
python enhanced_evaluate.py --model_path <path_to_model> --output_dir <output_dir> --data_path <data_path> --kg_path <kg_path>
```

For example:

```bash
python enhanced_evaluate.py --model_path ./output/kg_disentangle_20250518_112233/best_model.pth --output_dir ./output/kg_disentangle_20250518_112233 --data_path /home/<USER>/workplace/dwb/data/imdb --kg_path /home/<USER>/workplace/dwb/MMIMDB_KG_Disentangle/kg_data
```

## Output

The enhanced evaluation will output the results to the console and save them to a JSON file named `enhanced_test_results.json` in the experiment directory.

The JSON file contains all the standard metrics plus the additional F1-Micro, F1-Macro, and feature disentanglement metrics.

## Interpreting the Results

### Classification Metrics

- **F1 (Samples)**: The F1 score calculated for each sample and then averaged (standard metric for multi-label classification)
- **F1-Micro**: The F1 score calculated globally by counting the total true positives, false negatives, and false positives
- **F1-Macro**: The F1 score calculated for each label, and then averaged without weighting
- **Precision**: The precision score (true positives / (true positives + false positives))
- **Recall**: The recall score (true positives / (true positives + false negatives))
- **mAP**: Mean Average Precision (area under the precision-recall curve)

### Disentanglement Metrics

- **Modality Disentanglement Score**: Higher values (closer to 1.0) indicate better separation between modalities
- **Feature Independence**: Higher values indicate more independent features between modalities
- **Shared Information Preservation**: Higher values indicate better preservation of shared information
- **Text/Visual Modality Specificity**: Higher values indicate better preservation of modality-specific information
- **Text-to-Image/Image-to-Text Transfer**: Lower values indicate less information leakage between modalities
- **Redundancy Mean**: The average redundancy score (lower values indicate less redundancy)

## Comparison with SOTA

When comparing with state-of-the-art (SOTA) methods, focus on:

1. **F1-Micro and F1-Macro**: These are commonly reported in multi-label classification papers
2. **Modality Disentanglement Score**: This measures how well the model separates information between modalities
3. **Feature Independence**: This measures how independent the features from different modalities are
4. **Shared Information Preservation**: This measures how well shared information is preserved across modalities

Most SOTA papers on multimodal feature disentanglement report these or similar metrics.
