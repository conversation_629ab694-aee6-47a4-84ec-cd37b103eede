"""
Evaluate Component Effectiveness for Multimodal Feature Disentanglement Model.
This script evaluates the effectiveness of individual components in the model.
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import argparse
from torch.utils.data import DataLoader
import logging
from tabulate import tabulate
import pandas as pd

# Import project modules
from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
from component_effectiveness_metrics import (
    compute_graph_reasoning_effectiveness,
    compute_redundancy_detection_effectiveness,
    compute_adaptive_fusion_effectiveness
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Evaluate Component Effectiveness")
    
    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/component_effectiveness',
                        help='Output directory for saving results')
    
    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')
    
    # Experiment arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    
    return parser.parse_args()

def evaluate_component_effectiveness(model, data_loader, device):
    """
    Evaluate the effectiveness of individual components in the model.
    
    Args:
        model (torch.nn.Module): Model to evaluate
        data_loader (torch.utils.data.DataLoader): Data loader
        device (torch.device): Device to use
        
    Returns:
        dict: Dictionary of effectiveness metrics
    """
    model.eval()
    
    # Lists to store features and labels
    all_text_encoded = []
    all_visual_encoded = []
    all_kg_encoded = []
    all_graph_enhanced = []
    all_fused_features = []
    all_redundancy_scores = []
    all_labels = []
    
    with torch.no_grad():
        for batch in tqdm(data_loader, desc="Evaluating component effectiveness"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None
            
            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features
            
            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image
            
            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)
            
            # Collect features and labels
            all_text_encoded.append(outputs['text_encoded'].cpu().numpy())
            all_visual_encoded.append(outputs['visual_encoded'].cpu().numpy())
            all_kg_encoded.append(outputs['kg_encoded'].cpu().numpy())
            all_graph_enhanced.append(outputs.get('graph_enhanced', outputs['kg_encoded']).cpu().numpy())
            all_fused_features.append(outputs['fused'].cpu().numpy())
            all_redundancy_scores.append(outputs['redundancy_score'].cpu().numpy())
            all_labels.append(labels.cpu().numpy())
    
    # Stack all features and labels
    text_encoded = np.vstack(all_text_encoded)
    visual_encoded = np.vstack(all_visual_encoded)
    kg_encoded = np.vstack(all_kg_encoded)
    graph_enhanced = np.vstack(all_graph_enhanced)
    fused_features = np.vstack(all_fused_features)
    redundancy_scores = np.vstack(all_redundancy_scores)
    labels = np.vstack(all_labels)
    
    # Compute effectiveness metrics for each component
    graph_reasoning_metrics = compute_graph_reasoning_effectiveness(
        text_encoded, visual_encoded, kg_encoded, graph_enhanced, labels
    )
    
    redundancy_detection_metrics = compute_redundancy_detection_effectiveness(
        text_encoded, visual_encoded, redundancy_scores, labels
    )
    
    adaptive_fusion_metrics = compute_adaptive_fusion_effectiveness(
        text_encoded, visual_encoded, kg_encoded, fused_features, redundancy_scores, labels
    )
    
    # Combine all metrics
    all_metrics = {
        'graph_reasoning': graph_reasoning_metrics,
        'redundancy_detection': redundancy_detection_metrics,
        'adaptive_fusion': adaptive_fusion_metrics
    }
    
    return all_metrics

def visualize_component_effectiveness(metrics, output_dir):
    """
    Visualize component effectiveness metrics.
    
    Args:
        metrics (dict): Dictionary of effectiveness metrics
        output_dir (str): Output directory
    """
    os.makedirs(output_dir, exist_ok=True)
    
    # Create bar charts for each component
    for component, component_metrics in metrics.items():
        # Filter out metrics with values close to 0
        filtered_metrics = {k: v for k, v in component_metrics.items() if abs(v) > 1e-6}
        
        # Create dataframe for metrics
        metrics_data = []
        for metric, value in filtered_metrics.items():
            metrics_data.append({
                'Metric': metric,
                'Value': value
            })
        
        metrics_df = pd.DataFrame(metrics_data)
        
        # Create bar chart
        plt.figure(figsize=(15, 10))
        sns.barplot(x='Metric', y='Value', data=metrics_df)
        
        # Set labels and title
        plt.xlabel('Metric', fontsize=14, labelpad=10)
        plt.ylabel('Value', fontsize=14, labelpad=10)
        plt.title(f'{component.replace("_", " ").title()} Effectiveness Metrics', fontsize=18, pad=20)
        
        # Rotate x-axis labels
        plt.xticks(rotation=45, ha='right', fontsize=12)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'{component}_effectiveness.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    # Create comparison table
    table_data = []
    
    # Graph reasoning metrics
    table_data.append(["=== Graph Reasoning Effectiveness ==="])
    for metric, value in metrics['graph_reasoning'].items():
        table_data.append([metric, f"{value:.4f}"])
    
    # Redundancy detection metrics
    table_data.append([""])
    table_data.append(["=== Redundancy Detection Effectiveness ==="])
    for metric, value in metrics['redundancy_detection'].items():
        table_data.append([metric, f"{value:.4f}"])
    
    # Adaptive fusion metrics
    table_data.append([""])
    table_data.append(["=== Adaptive Fusion Effectiveness ==="])
    for metric, value in metrics['adaptive_fusion'].items():
        table_data.append([metric, f"{value:.4f}"])
    
    # Print comparison table
    logger.info("\nComponent Effectiveness Metrics:")
    logger.info("\n" + tabulate(table_data, headers=['Metric', 'Value'], tablefmt='grid'))
    
    # Save comparison table
    with open(os.path.join(output_dir, 'component_effectiveness.txt'), 'w') as f:
        f.write(tabulate(table_data, headers=['Metric', 'Value'], tablefmt='grid'))

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create test dataset
    logger.info("Creating test dataset...")
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )
    
    # Create test data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Load model
    logger.info(f"Loading model from {args.model_path}...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)
    
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model.eval()
    
    # Evaluate component effectiveness
    logger.info("Evaluating component effectiveness...")
    metrics = evaluate_component_effectiveness(model, test_loader, device)
    
    # Visualize component effectiveness
    logger.info("Visualizing component effectiveness...")
    visualize_component_effectiveness(metrics, args.output_dir)
    
    # Save metrics to JSON
    with open(os.path.join(args.output_dir, 'component_effectiveness.json'), 'w') as f:
        # Convert NumPy values to Python native types for JSON serialization
        json_safe_metrics = {}
        for component, component_metrics in metrics.items():
            json_safe_metrics[component] = {}
            for key, value in component_metrics.items():
                if isinstance(value, np.ndarray):
                    json_safe_metrics[component][key] = value.tolist()
                elif isinstance(value, (np.int_, np.intc, np.intp, np.int8, np.int16, np.int32, np.int64,
                                       np.uint8, np.uint16, np.uint32, np.uint64)):
                    json_safe_metrics[component][key] = int(value)
                elif isinstance(value, (np.float_, np.float16, np.float32, np.float64)):
                    json_safe_metrics[component][key] = float(value)
                elif value is np.nan:
                    json_safe_metrics[component][key] = None
                else:
                    json_safe_metrics[component][key] = value
        
        json.dump(json_safe_metrics, f, indent=2)
    
    logger.info(f"Results saved to {args.output_dir}")

if __name__ == '__main__':
    main()
