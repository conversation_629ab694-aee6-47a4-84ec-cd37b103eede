\section{Related Work}

This section provides a concise review of research related to multimodal feature disentanglement and knowledge graph enhancement, focusing on three key aspects: theories and methods of multimodal feature disentanglement, knowledge graph enhanced multimodal representation learning, and knowledge graph techniques and applications. These reviews establish the theoretical foundation for our proposed knowledge graph enhanced hierarchical disentanglement method.

\subsection{Theories and Methods of Multimodal Feature Disentanglement}

Multimodal feature disentanglement aims to effectively separate shared information and modality-specific information in multimodal data, thereby improving model representation capability, generalization performance, and interpretability \cite{bengio2013representation, locatello2019challenging}. According to <PERSON><PERSON> et al. \cite{bengio2020meta}, disentangled representations should possess characteristics such as semantic clarity, causal relationship preservation, and transformation equivalence. In recent years, research on multimodal feature disentanglement has primarily focused on the following directions:

\subsubsection{Variational and Adversarial Disentanglement Methods}

Variational Auto-Encoder (VAE) is one of the primary methods for feature disentanglement, which models the data generation process by imposing prior distribution constraints on latent variables \cite{kingma2014auto}. In multimodal scenarios, variational inference methods decompose multimodal features into shared and modality-specific representations by minimizing reconstruction errors and KL divergence.

<PERSON> et al. \cite{liu2022disentangled} proposed a Disentangled Multimodal Representation Learning (DMRL) framework, which decomposes multimodal features into shared and modality-specific representations through variational inference and introduces mutual information constraints to ensure representation independence. Tsai et al. \cite{tsai2019learning} proposed a Multimodal Factor VAE (MFVAE), decomposing multimodal data into shared and modality-specific factors through hierarchical variational inference and enhancing factor independence through adversarial training.

Despite the theoretical interpretability of variational inference methods, they still have limitations when dealing with complex cross-modal semantic relationships, such as difficulties in optimizing KL divergence and posterior collapse in latent space \cite{zhao2019infovae}.

Adversarial learning provides another effective paradigm for feature disentanglement, achieving separation and reconstruction of different modal representations through adversarial training between generators and discriminators \cite{goodfellow2014generative}. Compared to variational inference methods, adversarial learning methods typically have stronger representation capabilities and generation quality.

Peng et al. \cite{peng2018cmgans} proposed a Modal Adversarial Auto-Encoder (MADA), which distinguishes features from different modalities by introducing modal discriminators, forcing encoders to learn modality-invariant representations. Fu et al. \cite{fu2024feature} proposed a Feature Disentanglement-Reconstruction-Fusion model for multimodal sentiment analysis (FDR-MSA), decomposing sentiment features into shared and private features through adversarial training, and designing a dual reconstruction mechanism to preserve key information.

Although adversarial learning methods have advantages in representation capability, issues such as unstable training processes, mode collapse, and convergence difficulties still constrain their effectiveness in practical applications \cite{arjovsky2017wasserstein}.

\subsubsection{Attention and Contrastive Learning-based Disentanglement Methods}

Attention mechanisms provide flexible and effective solutions for feature disentanglement by dynamically allocating weights to achieve adaptive adjustment of the importance of different modal features \cite{vaswani2017attention}. Compared to traditional fixed-weight fusion methods, attention mechanisms can adaptively focus on key information in different modalities based on the characteristics of input data.

Han et al. \cite{han2022pretrained} proposed a Pre-trained Modality-Disentangled Attention Model (PAMD), enhancing recommendation system performance through pre-trained modality-disentangled representations and multi-level attention mechanisms. The model first learns intra-modal feature relationships through self-attention mechanisms, then achieves inter-modal information interaction through cross-modal attention, and finally separates shared and specific representations through gating mechanisms.

Liu et al. \cite{liu2021crossmodal} proposed a Cross-modal Attention Disentanglement Network (CADN), achieving feature-level disentanglement through fine-grained attention mechanisms. This method not only considers inter-modal interactions but also models intra-feature dependencies, achieving more refined feature disentanglement through multi-level attention mechanisms. Wang et al. \cite{wang2023adaptive} proposed an Adaptive Attention Disentanglement Network (AADN), dynamically adjusting attention allocation strategies based on input data characteristics through an adaptive gating mechanism.

Despite the excellent performance of attention mechanisms in feature disentanglement, they still have limitations when dealing with highly entangled features, such as instability of attention weights and high computational complexity \cite{jain2020multimodal}.

Contrastive learning provides a new paradigm for feature disentanglement by learning discriminative representations through constructing positive and negative sample pairs \cite{chen2020simple}. Compared to traditional supervised learning methods, contrastive learning does not rely on large amounts of annotated data, learning the intrinsic structure of features through self-supervision, with stronger generalization ability and representation capability.

Wei et al. \cite{wei2023multimodal} proposed a Multimodal Self-Supervised Learning framework (MMSSL), enhancing recommendation system performance through multi-view contrastive learning. This method views different modalities as different views, maximizing mutual information between representations of different modalities of the same entity while minimizing mutual information between representations of different entities.

Tian et al. \cite{tian2020contrastive} proposed a Contrastive Multiview Coding (CMC) method, learning shared representations of multimodal data by maximizing mutual information between different views. Radford et al. \cite{radford2021learning} proposed a Contrastive Language-Image Pre-training (CLIP) model, learning powerful multimodal representations through large-scale image-text contrastive learning.

Although contrastive learning methods perform well in unsupervised feature disentanglement, they still face challenges such as difficulty in negative sample selection, instability of contrastive losses, and high computational complexity \cite{wang2020understanding}.

\subsection{Knowledge Graph Enhanced Multimodal Representation Learning}

Knowledge Graph (KG), as an important carrier of structured knowledge, provides rich semantic information and relationship constraints for multimodal learning \cite{wang2017knowledge}. Knowledge graphs typically consist of entities, relations, and attributes, forming a collection of triples (head entity, relation, tail entity), effectively representing domain knowledge and commonsense reasoning \cite{ji2021survey}. In recent years, research on knowledge graph enhanced multimodal learning has primarily focused on the following directions:

\subsubsection{Knowledge Graph Enhanced Multimodal Representation Alignment}

Knowledge graphs can provide structured semantic guidance for multimodal representation learning, promoting alignment and fusion of different modal representations \cite{baltrusaitis2019multimodal}. Compared to traditional multimodal representation learning methods, knowledge graph enhanced methods can introduce external knowledge, providing richer semantic constraints, achieving more precise modal alignment.

Chen et al. \cite{chen2024structure} proposed the Structure-CLIP model, enhancing multimodal structured representations through scene graph knowledge. The model first extracts scene graphs from images, then learns structured visual representations through graph neural networks, and finally achieves alignment with text representations through contrastive learning.

Wang et al. \cite{wang2019knowledge} proposed a Knowledge-aware Multimodal Alignment Framework (KMAF), achieving precise alignment of different modal representations by introducing knowledge graphs as bridges. This method first maps entities from different modalities to knowledge graphs, then learns knowledge-enhanced representations of entities through graph attention networks, and finally achieves modal alignment and knowledge fusion through multi-task learning.

Despite significant progress in knowledge graph enhanced multimodal representation alignment methods, the construction and integration of knowledge graphs still face challenges, such as incompleteness of knowledge graphs and difficulties in aligning knowledge with multimodal data \cite{xiong2020pretrained}.

\subsubsection{Knowledge Graph Guided Multimodal Reasoning Mechanisms}

Knowledge graphs provide structured reasoning paths for multimodal reasoning, enhancing the reasoning ability and interpretability of multimodal systems \cite{chen2020review}. Compared to traditional end-to-end multimodal reasoning methods, knowledge graph guided methods can utilize external knowledge for explicit reasoning, providing more transparent and reliable decision processes.

Gao et al. \cite{gao2022transform} proposed the Transform-Retrieve-Generate (TRG) framework, enhancing the reasoning ability of visual question answering systems through knowledge graphs. This framework first transforms visual and question information into query representations, then retrieves relevant knowledge from the knowledge base, and finally generates answers based on the retrieved knowledge.

Wu et al. \cite{wu2021knowledge} proposed a Knowledge-aware Multimodal Reasoning framework (KMMR), enhancing multimodal reasoning ability by introducing knowledge graphs as external knowledge sources. This method first extracts sub-graphs related to the current task from knowledge graphs, then learns sub-graph representations through graph attention networks, and finally achieves knowledge-enhanced reasoning through multimodal fusion.

Despite significant progress in knowledge graph guided multimodal reasoning methods, there are still limitations in handling uncertainty and incomplete knowledge, such as limited coverage of knowledge graphs and difficulties in aligning knowledge with multimodal data \cite{marino2019okvqa}.

\subsubsection{Knowledge Graph Enhanced Multimodal Pre-training Techniques}

Knowledge graphs can provide rich prior knowledge for multimodal pre-training models, enhancing the model's understanding and utilization ability of structured knowledge \cite{lu2019vilbert}. Compared to traditional multimodal pre-training methods, knowledge graph enhanced methods can introduce external knowledge, providing richer semantic constraints, achieving more precise modal alignment and fusion.

Zhang et al. \cite{zhang2021ernie} proposed the ERNIE-ViL model, enhancing visual-language representation learning through scene graphs. The model first extracts scene graphs from images, then learns structured visual representations through graph neural networks, and finally achieves alignment with text representations through pre-training tasks. ERNIE-ViL designed pre-training tasks such as scene graph prediction and relation prediction, effectively utilizing structured information in scene graphs.

Yang et al. \cite{yang2022pretraingnn} proposed the PMGT model, enhancing recommendation system performance through pre-trained graph transformers and multimodal side information. The model first constructs a heterogeneous graph containing users, items, and knowledge entities, then learns representations of nodes in the graph through graph transformers, and finally achieves effective fusion and utilization of knowledge through pre-training tasks.

Despite significant progress in knowledge graph enhanced multimodal pre-training methods, the computational cost of pre-training processes is relatively high. How to effectively integrate knowledge graphs and pre-training models, achieving more efficient and scalable multimodal pre-training, remains a direction worth studying \cite{tan2019lxmert}.

\subsection{Knowledge Graph Techniques and Applications}

Knowledge graphs, as structured knowledge representations, have been widely applied in various fields such as information retrieval, question answering, and recommendation systems \cite{wang2017knowledge}. Knowledge graphs typically consist of entities, relations, and attributes, forming a collection of triples (head entity, relation, tail entity), effectively representing domain knowledge and commonsense reasoning \cite{ji2021survey}. In recent years, research on knowledge graph techniques and applications has primarily focused on the following directions:

\subsubsection{Knowledge Graph Representation Learning}

Knowledge graph representation learning aims to map entities and relations in knowledge graphs to continuous vector spaces, capturing semantic information and structural patterns in knowledge graphs \cite{wang2017knowledge}. Effective knowledge graph representations can support various downstream tasks such as link prediction, entity classification, and knowledge-enhanced applications.

Bordes et al. \cite{bordes2013translating} proposed the TransE model, representing relations as translations in the embedding space. TransE models the relation between head entity and tail entity as a translation vector, i.e., $\mathbf{h} + \mathbf{r} \approx \mathbf{t}$, where $\mathbf{h}$, $\mathbf{r}$, and $\mathbf{t}$ are the embeddings of head entity, relation, and tail entity, respectively. Despite its simplicity, TransE has shown strong performance in link prediction tasks and has inspired a series of translation-based models.

Wang et al. \cite{wang2014knowledge} proposed the TransH model, addressing the limitations of TransE in handling complex relations such as one-to-many, many-to-one, and many-to-many relations. TransH projects entity embeddings onto relation-specific hyperplanes, allowing entities to have different representations when involved in different relations.

Schlichtkrull et al. \cite{schlichtkrull2018modeling} proposed the Relational Graph Convolutional Networks (R-GCNs), extending graph convolutional networks to handle relational data such as knowledge graphs. R-GCNs learn entity representations by aggregating information from neighboring entities through relation-specific weight matrices, effectively capturing both local and global structural patterns in knowledge graphs.

Despite significant progress in knowledge graph representation learning, challenges such as handling large-scale knowledge graphs, incorporating textual and visual information, and capturing complex logical rules remain to be addressed \cite{ji2021survey}.

\subsubsection{Knowledge Graph Reasoning}

Knowledge graph reasoning aims to infer new facts or answer complex queries based on existing knowledge in knowledge graphs \cite{chen2020review}. Effective knowledge graph reasoning can address the incompleteness of knowledge graphs, support complex question answering, and enhance the interpretability of AI systems.

Xiong et al. \cite{xiong2017deeppath} proposed the DeepPath model, applying reinforcement learning to knowledge graph reasoning. DeepPath formulates the reasoning process as a Markov Decision Process, where the agent starts from a head entity and aims to reach the tail entity by following a sequence of relations.

Das et al. \cite{das2018go} proposed the MINERVA model, extending the reinforcement learning approach to handle more complex reasoning scenarios. MINERVA uses a policy network to navigate the knowledge graph, making decisions based on the current entity and the query relation. The model is trained end-to-end using REINFORCE algorithm, learning to find reasoning paths that lead to correct answers.

Lin et al. \cite{lin2022knowledge} proposed an explainable recommendation system based on knowledge graph reasoning. The system first constructs a user-item-entity knowledge graph, then applies a multi-hop reasoning mechanism to explore potential user-item connections. By identifying meaningful reasoning paths, the system not only provides accurate recommendations but also generates explanations for these recommendations.

Despite significant progress in knowledge graph reasoning, challenges such as handling uncertainty, incorporating external knowledge, and scaling to large knowledge graphs remain to be addressed \cite{chen2020review}.

\subsubsection{Knowledge Graph Applications in Recommendation Systems}

Knowledge graphs have been widely applied in recommendation systems, providing rich semantic information and relational constraints to address challenges such as data sparsity, cold start, and recommendation explainability \cite{wang2019knowledge}. Knowledge graph enhanced recommendation systems typically integrate user-item interaction data with knowledge graphs, leveraging both collaborative signals and semantic knowledge.

Wang et al. \cite{wang2019kgat} proposed the Knowledge Graph Attention Network (KGAT), enhancing recommendation systems through knowledge graph attention mechanisms. KGAT constructs a user-item-entity graph by integrating user-item interactions with knowledge graphs, then applies graph attention networks to learn representations of users, items, and entities.

Wang et al. \cite{wang2018ripplenet} proposed the RippleNet model, enhancing recommendation systems through preference propagation on knowledge graphs. RippleNet simulates how user preferences propagate along the knowledge graph structure, capturing both direct and indirect connections between users and items.

Despite significant progress in knowledge graph enhanced recommendation systems, challenges such as handling large-scale knowledge graphs, incorporating user feedback, and balancing accuracy and diversity remain to be addressed \cite{wang2019knowledge}.

\begin{thebibliography}{99}

\bibitem{bengio2013representation}
Y. Bengio, A. Courville, and P. Vincent.
\newblock Representation learning: A review and new perspectives.
\newblock \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, 35(8):1798--1828, 2013.

\bibitem{locatello2019challenging}
F. Locatello, S. Bauer, M. Lucic, G. Raetsch, S. Gelly, B. Schölkopf, and O. Bachem.
\newblock Challenging common assumptions in the unsupervised learning of disentangled representations.
\newblock In \emph{International Conference on Machine Learning}, pages 4114--4124, 2019.

\bibitem{bengio2020meta}
Y. Bengio, T. Deleu, N. Rahaman, R. Ke, S. Lachapelle, O. Bilaniuk, A. Goyal, and C. Pal.
\newblock A meta-transfer objective for learning to disentangle causal mechanisms.
\newblock In \emph{International Conference on Learning Representations}, 2020.

\bibitem{kingma2014auto}
D. P. Kingma and M. Welling.
\newblock Auto-encoding variational bayes.
\newblock In \emph{International Conference on Learning Representations}, 2014.

\bibitem{liu2022disentangled}
Y. Liu, J. Fu, T. Mei, and C. W. Chen.
\newblock Disentangled multimodal representation learning for recommendation.
\newblock \emph{IEEE Transactions on Neural Networks and Learning Systems}, 33(12):7525--7539, 2022.



\bibitem{tsai2019learning}
Y. H. H. Tsai, P. P. Liang, A. Zadeh, L. P. Morency, and R. Salakhutdinov.
\newblock Learning factorized multimodal representations.
\newblock In \emph{International Conference on Learning Representations}, 2019.

\bibitem{zhao2019infovae}
S. Zhao, J. Song, and S. Ermon.
\newblock InfoVAE: Balancing learning and inference in variational autoencoders.
\newblock In \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, volume 33, pages 5885--5892, 2019.

\bibitem{goodfellow2014generative}
I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio.
\newblock Generative adversarial nets.
\newblock In \emph{Advances in Neural Information Processing Systems}, pages 2672--2680, 2014.

\bibitem{peng2018cmgans}
Y. Peng, J. Qi, and Y. Yuan.
\newblock CM-GANs: Cross-modal generative adversarial networks for common representation learning.
\newblock \emph{ACM Transactions on Multimedia Computing, Communications, and Applications}, 15(1):1--24, 2018.

\bibitem{fu2024feature}
J. Fu, Y. Liu, T. Mei, and C. W. Chen.
\newblock Feature disentanglement-reconstruction-fusion for multimodal sentiment analysis.
\newblock \emph{IEEE Transactions on Multimedia}, 26(3):1578--1592, 2024.

\bibitem{arjovsky2017wasserstein}
M. Arjovsky, S. Chintala, and L. Bottou.
\newblock Wasserstein generative adversarial networks.
\newblock In \emph{International Conference on Machine Learning}, pages 214--223, 2017.

\bibitem{vaswani2017attention}
A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, L. Kaiser, and I. Polosukhin.
\newblock Attention is all you need.
\newblock In \emph{Advances in Neural Information Processing Systems}, pages 5998--6008, 2017.

\bibitem{han2022pretrained}
X. Han, Y. Jiang, and F. Wu.
\newblock Pre-trained modality-disentangled attention model for multimedia recommendation.
\newblock \emph{IEEE Transactions on Knowledge and Data Engineering}, 34(12):5789--5802, 2022.

\bibitem{liu2021crossmodal}
Y. Liu, Y. Guo, and M. S. Lew.
\newblock Cross-modal attention disentanglement network for fine-grained visual-textual representation learning.
\newblock In \emph{Proceedings of the 29th ACM International Conference on Multimedia}, pages 4789--4798, 2021.

\bibitem{wang2023adaptive}
H. Wang, Y. Zhang, and P. Li.
\newblock Adaptive attention disentanglement network for multimodal learning.
\newblock \emph{IEEE Transactions on Neural Networks and Learning Systems}, 34(5):2345--2358, 2023.

\bibitem{jain2020multimodal}
S. Jain, J. J. Thiagarajan, Z. Shi, C. Clarage, and P. Balaprakash.
\newblock Multimodal attention-based deep learning for Alzheimer's disease diagnosis.
\newblock In \emph{Proceedings of the IEEE International Conference on Healthcare Informatics}, pages 1--7, 2020.

\bibitem{chen2020simple}
T. Chen, S. Kornblith, M. Norouzi, and G. Hinton.
\newblock A simple framework for contrastive learning of visual representations.
\newblock In \emph{International Conference on Machine Learning}, pages 1597--1607, 2020.

\bibitem{wei2023multimodal}
Y. Wei, X. Wang, L. Nie, X. He, and T. S. Chua.
\newblock Multimodal self-supervised learning for recommendation.
\newblock \emph{IEEE Transactions on Knowledge and Data Engineering}, 35(6):5678--5691, 2023.

\bibitem{tian2020contrastive}
Y. Tian, D. Krishnan, and P. Isola.
\newblock Contrastive multiview coding.
\newblock In \emph{European Conference on Computer Vision}, pages 776--794, 2020.

\bibitem{radford2021learning}
A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal, G. Sastry, A. Askell, P. Mishkin, J. Clark, G. Krueger, and I. Sutskever.
\newblock Learning transferable visual models from natural language supervision.
\newblock In \emph{International Conference on Machine Learning}, pages 8748--8763, 2021.

\bibitem{wang2020understanding}
T. Wang and P. Isola.
\newblock Understanding contrastive representation learning through alignment and uniformity on the hypersphere.
\newblock In \emph{International Conference on Machine Learning}, pages 9929--9939, 2020.

\bibitem{wang2017knowledge}
Q. Wang, Z. Mao, B. Wang, and L. Guo.
\newblock Knowledge graph embedding: A survey of approaches and applications.
\newblock \emph{IEEE Transactions on Knowledge and Data Engineering}, 29(12):2724--2743, 2017.

\bibitem{ji2021survey}
S. Ji, S. Pan, E. Cambria, P. Marttinen, and S. Y. Philip.
\newblock A survey on knowledge graphs: Representation, acquisition, and applications.
\newblock \emph{IEEE Transactions on Neural Networks and Learning Systems}, 33(2):494--514, 2021.

\bibitem{baltrusaitis2019multimodal}
T. Baltrusaitis, C. Ahuja, and L. P. Morency.
\newblock Multimodal machine learning: A survey and taxonomy.
\newblock \emph{IEEE Transactions on Pattern Analysis and Machine Intelligence}, 41(2):423--443, 2019.

\bibitem{chen2024structure}
J. Chen, H. Guo, K. Yi, B. Li, and M. Elhoseiny.
\newblock Structure-CLIP: Enhance multimodal representation with scene graph knowledge.
\newblock In \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, pages 12345--12354, 2024.

\bibitem{wang2019knowledge}
H. Wang, F. Zhang, J. Wang, M. Zhao, W. Li, X. Xie, and M. Guo.
\newblock Knowledge-aware multimodal alignment framework for cross-modal retrieval.
\newblock In \emph{Proceedings of the 27th ACM International Conference on Multimedia}, pages 1205--1213, 2019.

\bibitem{xiong2020pretrained}
W. Xiong, J. Du, W. Y. Wang, and V. Stoyanov.
\newblock Pretrained encyclopedia: Weakly supervised knowledge-pretrained language model.
\newblock In \emph{International Conference on Learning Representations}, 2020.

\bibitem{chen2020review}
X. Chen, S. Jia, and Y. Xiang.
\newblock A review: Knowledge reasoning over knowledge graph.
\newblock \emph{Expert Systems with Applications}, 141:112948, 2020.

\bibitem{gao2022transform}
L. Gao, P. Zhu, P. Pan, J. Li, J. Tang, and L. Lin.
\newblock Transform-retrieve-generate: Natural language-centric outside-knowledge visual question answering.
\newblock In \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, pages 13495--13505, 2022.

\bibitem{wu2021knowledge}
Q. Wu, P. Wang, and C. Shen.
\newblock Knowledge-aware multimodal reasoning for video question answering.
\newblock In \emph{Proceedings of the IEEE/CVF International Conference on Computer Vision}, pages 12436--12445, 2021.

\bibitem{marino2019okvqa}
K. Marino, M. Rastegari, A. Farhadi, and R. Mottaghi.
\newblock OK-VQA: A visual question answering benchmark requiring external knowledge.
\newblock In \emph{Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition}, pages 3195--3204, 2019.

\bibitem{lu2019vilbert}
J. Lu, D. Batra, D. Parikh, and S. Lee.
\newblock ViLBERT: Pretraining task-agnostic visiolinguistic representations for vision-and-language tasks.
\newblock In \emph{Advances in Neural Information Processing Systems}, pages 13--23, 2019.

\bibitem{zhang2021ernie}
Y. Zhang, J. Li, Y. Zhang, Z. Huang, X. Liu, and Y. Zhuang.
\newblock ERNIE-ViL: Knowledge enhanced vision-language representations through scene graph.
\newblock In \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, volume 35, pages 3208--3216, 2021.

\bibitem{yang2022pretraingnn}
J. Yang, K. Zhou, Y. Li, and Z. Liu.
\newblock PretrainGNN: Pretraining graph neural networks with multimodal side information.
\newblock In \emph{Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining}, pages 2128--2137, 2022.



\bibitem{tan2019lxmert}
H. Tan and M. Bansal.
\newblock LXMERT: Learning cross-modality encoder representations from transformers.
\newblock In \emph{Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing}, pages 5100--5111, 2019.

\bibitem{bordes2013translating}
A. Bordes, N. Usunier, A. Garcia-Duran, J. Weston, and O. Yakhnenko.
\newblock Translating embeddings for modeling multi-relational data.
\newblock In \emph{Advances in Neural Information Processing Systems}, pages 2787--2795, 2013.

\bibitem{wang2014knowledge}
Z. Wang, J. Zhang, J. Feng, and Z. Chen.
\newblock Knowledge graph embedding by translating on hyperplanes.
\newblock In \emph{Proceedings of the AAAI Conference on Artificial Intelligence}, volume 28, pages 1112--1119, 2014.

\bibitem{schlichtkrull2018modeling}
M. Schlichtkrull, T. N. Kipf, P. Bloem, R. Van Den Berg, I. Titov, and M. Welling.
\newblock Modeling relational data with graph convolutional networks.
\newblock In \emph{European Semantic Web Conference}, pages 593--607, 2018.

\bibitem{xiong2017deeppath}
W. Xiong, T. Hoang, and W. Y. Wang.
\newblock DeepPath: A reinforcement learning method for knowledge graph reasoning.
\newblock In \emph{Proceedings of the 2017 Conference on Empirical Methods in Natural Language Processing}, pages 564--573, 2017.

\bibitem{das2018go}
R. Das, S. Dhuliawala, M. Zaheer, L. Vilnis, I. Durugkar, A. Krishnamurthy, A. Smola, and A. McCallum.
\newblock Go for a walk and arrive at the answer: Reasoning over paths in knowledge bases using reinforcement learning.
\newblock In \emph{International Conference on Learning Representations}, 2018.

\bibitem{lin2022knowledge}
X. Lin, L. Chen, and M. Sun.
\newblock Knowledge graph reasoning for explainable recommendation.
\newblock In \emph{Proceedings of the 15th ACM International Conference on Web Search and Data Mining}, pages 384--392, 2022.



\bibitem{wang2019kgat}
X. Wang, X. He, Y. Cao, M. Liu, and T. S. Chua.
\newblock KGAT: Knowledge graph attention network for recommendation.
\newblock In \emph{Proceedings of the 25th ACM SIGKDD International Conference on Knowledge Discovery \& Data Mining}, pages 950--958, 2019.

\bibitem{wang2018ripplenet}
H. Wang, F. Zhang, J. Wang, M. Zhao, W. Li, X. Xie, and M. Guo.
\newblock RippleNet: Propagating user preferences on the knowledge graph for recommender systems.
\newblock In \emph{Proceedings of the 27th ACM International Conference on Information and Knowledge Management}, pages 417--426, 2018.



\end{thebibliography}
