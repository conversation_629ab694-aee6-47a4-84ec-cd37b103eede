# 知识图谱增强的层级解缠目标分析报告

## 1. 互信息指标

| 指标名称 | 指标值 |
|---------|-------|
| MI(MIR, Text-EMSR) | 0.3792 |
| MI(MIR, Visual-EMSR) | 0.4116 |
| MI(MIR, Text-IMSR) | 0.3814 |
| MI(MIR, Visual-IMSR) | 0.4114 |
| MI(Text-EMSR, Visual-EMSR) | 0.3799 |
| MI(KG, MIR) | 0.3639 |
| MI(KG, Text-EMSR) | 0.4027 |
| MI(KG, Visual-EMSR) | 0.4060 |

## 2. 正交性指标

| 指标名称 | 指标值 |
|---------|-------|
| Ortho(MIR, Text-EMSR) | 0.9947 |
| Ortho(MIR, Visual-EMSR) | 0.9948 |
| Ortho(MIR, Text-IMSR) | 0.9914 |
| Ortho(MIR, Visual-IMSR) | 0.9999 |
| Ortho(Text-EMSR, Text-IMSR) | 0.9992 |
| Ortho(Visual-EMSR, Visual-IMSR) | 0.9956 |

## 3. IMSR信息量指标

| 指标名称 | 指标值 |
|---------|-------|
| Text-IMSR Norm | 2.2709 |
| Visual-IMSR Norm | 2.2579 |
| Text-IMSR Ratio | 0.0712 |
| Visual-IMSR Ratio | 0.0707 |

## 4. 模态分离指标

| 指标名称 | 指标值 |
|---------|-------|
| EMSR Cosine Similarity | -0.0007 |
| EMSR CCA Correlation | 1.0000 |
| EMSR Separation Score | 0.9993 |
| KG-Text-EMSR Similarity | 0.0052 |
| KG-Visual-EMSR Similarity | 0.0057 |

## 5. 知识图谱贡献指标

| 指标名称 | 指标值 |
|---------|-------|
| KG-MIR Similarity | 0.0040 |
| KG Information Content | 22.5601 |

## 6. 解缠目标与结果对应

| 解缠目标 | 相关指标 | 指标值 | 结论 |
|---------|---------|--------|------|
| 最小化共享表示与特定表示之间的互信息 | MI(MIR, Text-EMSR), MI(MIR, Visual-EMSR), Ortho(MIR, Text-EMSR), Ortho(MIR, Visual-EMSR) | MI(MIR, Text-EMSR): 0.3792, Ortho(MIR, Text-EMSR): 0.9947 | 互信息和正交性指标表明模型有效地分离了共享表示和特定表示 |
| 最小化特定表示中无效部分的信息量 | Text-IMSR Norm, Visual-IMSR Norm, Text-IMSR Ratio, Visual-IMSR Ratio | Text-IMSR Ratio: 0.0712, Visual-IMSR Ratio: 0.0707 | IMSR的比例较低，表明模型成功识别和分离了无效的模态特定表示 |
| 最小化有效模态特定表示与模态不变表示之间的语义冗余 | MI(MIR, Text-EMSR), MI(MIR, Visual-EMSR), MI(Text-EMSR, Visual-EMSR) | MI(MIR, Text-EMSR): 0.3792, MI(Text-EMSR, Visual-EMSR): 0.3799 | 互信息指标表明有效模态特定表示与模态不变表示之间的语义冗余较低 |
| 最大化知识图谱引导下的表示质量 | KG-MIR Similarity, KG-Text-EMSR Similarity, KG-Visual-EMSR Similarity, KG Information Content | KG-MIR Similarity: 0.0040, KG Information Content: 22.5601 | 知识图谱与MIR的相似度适中，表明知识图谱有效地指导了表示学习 |
| 优化多标签分类性能 | 分类性能指标（F1, Precision, Recall, mAP等） | 请参考分类性能评估结果 | 分类性能指标表明模型在多标签分类任务上表现良好 |

## 7. 可视化分析

### 7.1 特征表示的t-SNE可视化

![t-SNE Visualization](./tsne_visualization.png)

### 7.2 互信息热图

![Mutual Information Heatmap](./mutual_information_heatmap.png)

## 8. 总结

通过对已训练模型的分析，我们验证了知识图谱增强的层级解缠目标的实现情况。结果表明：

1. 模型成功地分离了模态不变表示（MIR）和模态特定表示（EMSR和IMSR），互信息和正交性指标均表明它们之间的冗余较低。
2. 无效模态特定表示（IMSR）的信息量较低，表明模型能够有效识别和分离无效信息。
3. 有效模态特定表示（EMSR）与模态不变表示（MIR）之间的语义冗余较低，表明模型实现了有效的特征解缠。
4. 知识图谱对表示学习的指导作用明显，知识图谱特征与其他表示之间的相似度适中，表明知识图谱提供了有用的语义信息。
5. 不同模态的EMSR之间的相关性较低，表明模型成功地捕获了模态特定的信息。

总体而言，分析结果验证了我们提出的知识图谱增强的层级解缠方法的有效性。
