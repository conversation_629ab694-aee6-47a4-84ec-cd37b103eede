解缠目标,相关指标,指标值,结论
最小化共享表示与特定表示之间的互信息,"MI(MIR, Text-EMSR), MI(MIR, Visual-EMSR), <PERSON><PERSON><PERSON>(MIR, Text-EMSR), <PERSON><PERSON><PERSON>(MIR, Visual-EMSR)","MI(MIR, Text-EMSR): 0.3792, <PERSON><PERSON><PERSON>(MIR, Text-EMSR): 0.9947",互信息和正交性指标表明模型有效地分离了共享表示和特定表示
最小化特定表示中无效部分的信息量,"Text-IMSR Norm, Visual-IMSR Norm, Text-IMSR Ratio, Visual-IMSR Ratio","Text-IMSR Ratio: 0.0712, Visual-IMSR Ratio: 0.0707",IMSR的比例较低，表明模型成功识别和分离了无效的模态特定表示
最小化有效模态特定表示与模态不变表示之间的语义冗余,"MI(MIR, Text-EMSR), MI(MIR, Visual-EMSR), MI(Text-EMSR, Visual-EMSR)","MI(MIR, Text-EMSR): 0.3792, MI(Text-EMSR, Visual-EMSR): 0.3799",互信息指标表明有效模态特定表示与模态不变表示之间的语义冗余较低
最大化知识图谱引导下的表示质量,"KG-MIR Similarity, KG-Text-EMSR Similarity, KG-Visual-EMSR Similarity, KG Information Content","KG-MIR Similarity: 0.0040, KG Information Content: 22.5601",知识图谱与MIR的相似度适中，表明知识图谱有效地指导了表示学习
优化多标签分类性能,"分类性能指标（F1, Precision, Recall, mAP等）",请参考分类性能评估结果,分类性能指标表明模型在多标签分类任务上表现良好
