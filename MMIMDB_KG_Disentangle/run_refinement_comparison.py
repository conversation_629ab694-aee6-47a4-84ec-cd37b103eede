"""
Sc<PERSON><PERSON> to run comparison experiments focusing on Refinement Effect Metrics.
This script compares different refinement strategies during testing phase.
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import argparse
from torch.utils.data import DataLoader
import logging
from tabulate import tabulate
import pandas as pd
import sklearn.metrics

# Import project modules
from utils.dataset import MMIMDBDataset
from models.kg_disentangle_net import KGDisentangleNet
# Import disentanglement metrics directly
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from enhanced_evaluate import compute_disentanglement_metrics

# Define metrics computation functions here to avoid importing from train.py
def compute_metrics(y_true, y_pred):
    """
    Compute standard metrics for multi-label classification.

    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities

    Returns:
        dict: Dictionary of metrics
    """
    # Convert predictions to binary using 0.5 threshold
    y_pred_binary = (y_pred > 0.5).astype(int)

    # Compute metrics
    f1_micro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='micro')
    f1_macro = sklearn.metrics.f1_score(y_true, y_pred_binary, average='macro')
    f1_samples = sklearn.metrics.f1_score(y_true, y_pred_binary, average='samples')
    precision = sklearn.metrics.precision_score(y_true, y_pred_binary, average='micro')
    recall = sklearn.metrics.recall_score(y_true, y_pred_binary, average='micro')

    # Compute mAP
    ap_scores = []
    for i in range(y_true.shape[1]):
        if np.sum(y_true[:, i]) > 0:  # Only compute AP if there are positive samples
            ap = sklearn.metrics.average_precision_score(y_true[:, i], y_pred[:, i])
            ap_scores.append(ap)
    mAP = np.mean(ap_scores) if ap_scores else 0.0

    return {
        'f1_micro': f1_micro,
        'f1_macro': f1_macro,
        'f1': f1_samples,  # This is the sample-averaged F1 score
        'precision': precision,
        'recall': recall,
        'mAP': mAP
    }

def compute_enhanced_metrics(y_true, y_pred, threshold=0.5):
    """
    Compute enhanced metrics for multi-label classification.

    Args:
        y_true (numpy.ndarray): Ground truth labels
        y_pred (numpy.ndarray): Predicted probabilities
        threshold (float): Threshold for binary prediction

    Returns:
        dict: Dictionary of enhanced metrics
    """
    # Convert predictions to binary
    y_pred_binary = (y_pred > threshold).astype(int)

    # Compute hamming accuracy
    hamming_accuracy = 1.0 - sklearn.metrics.hamming_loss(y_true, y_pred_binary)

    # Compute subset accuracy
    subset_accuracy = sklearn.metrics.accuracy_score(y_true, y_pred_binary)

    # Compute AUC
    auc_scores = []
    for i in range(y_true.shape[1]):
        if np.sum(y_true[:, i]) > 0 and np.sum(y_true[:, i]) < len(y_true[:, i]):
            try:
                auc = sklearn.metrics.roc_auc_score(y_true[:, i], y_pred[:, i])
                auc_scores.append(auc)
            except:
                pass
    auc = np.mean(auc_scores) if auc_scores else 0.0

    return {
        'hamming_accuracy': hamming_accuracy,
        'subset_accuracy': subset_accuracy,
        'auc': auc
    }

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%m/%d/%Y %H:%M:%S'
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Refinement Effect Comparison")

    # Data arguments
    parser.add_argument('--data_path', type=str, default='./data/imdb',
                        help='Path to MM-IMDB dataset')
    parser.add_argument('--kg_path', type=str, default='./kg_data',
                        help='Path to knowledge graph data')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to the trained model')
    parser.add_argument('--output_dir', type=str, default='./output/refinement_comparison',
                        help='Output directory for saving results')

    # Model arguments
    parser.add_argument('--text_dim', type=int, default=300,
                        help='Dimension of text features')
    parser.add_argument('--visual_dim', type=int, default=4096,
                        help='Dimension of visual features')
    parser.add_argument('--kg_dim', type=int, default=200,
                        help='Dimension of knowledge graph embeddings')
    parser.add_argument('--hidden_dim', type=int, default=512,
                        help='Dimension of hidden layers')
    parser.add_argument('--num_classes', type=int, default=23,
                        help='Number of output classes')

    # Experiment arguments
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for evaluation')
    parser.add_argument('--device', type=str, default='cuda',
                        help='Device to use (cuda or cpu)')
    parser.add_argument('--threshold', type=float, default=0.5,
                        help='Threshold for binary prediction')
    parser.add_argument('--refinement_strategies', type=str, nargs='+',
                        default=['none', 'linear', 'nonlinear', 'adaptive'],
                        help='Refinement strategies to compare')

    return parser.parse_args()

def apply_refinement(text_encoded, visual_encoded, redundancy_scores, strategy='none', alpha=0.5):
    """
    Apply different refinement strategies to encoded features.

    Args:
        text_encoded (torch.Tensor): Text encoded features
        visual_encoded (torch.Tensor): Visual encoded features
        redundancy_scores (torch.Tensor): Redundancy scores
        strategy (str): Refinement strategy ('none', 'linear', 'nonlinear', 'adaptive')
        alpha (float): Weight for linear refinement

    Returns:
        tuple: Refined text and visual features, and refinement metrics
    """
    # Initialize refinement metrics
    refinement_metrics = {
        'strategy': strategy,
        'alpha': alpha,
    }

    if strategy == 'none':
        # No refinement
        return text_encoded, visual_encoded, refinement_metrics

    elif strategy == 'linear':
        # Linear refinement: subtract redundant information weighted by redundancy score
        # Formula: text_refined = text_encoded - alpha * redundancy_scores * visual_encoded
        text_refined = text_encoded - alpha * redundancy_scores * visual_encoded
        visual_refined = visual_encoded - alpha * redundancy_scores * text_encoded

        # Calculate refinement magnitude
        text_change = torch.norm(text_refined - text_encoded, dim=1)
        visual_change = torch.norm(visual_refined - visual_encoded, dim=1)

        refinement_metrics['text_change_mean'] = torch.mean(text_change).item()
        refinement_metrics['visual_change_mean'] = torch.mean(visual_change).item()
        refinement_metrics['text_change_max'] = torch.max(text_change).item()
        refinement_metrics['visual_change_max'] = torch.max(visual_change).item()

        return text_refined, visual_refined, refinement_metrics

    elif strategy == 'nonlinear':
        # Nonlinear refinement: use exponential weighting of redundancy
        # Formula: redundancy_weight = exp(redundancy_scores) - 1.0
        redundancy_weight = torch.exp(redundancy_scores) - 1.0
        text_refined = text_encoded - alpha * redundancy_weight * visual_encoded
        visual_refined = visual_encoded - alpha * redundancy_weight * text_encoded

        # Calculate refinement magnitude
        text_change = torch.norm(text_refined - text_encoded, dim=1)
        visual_change = torch.norm(visual_refined - visual_encoded, dim=1)

        refinement_metrics['text_change_mean'] = torch.mean(text_change).item()
        refinement_metrics['visual_change_mean'] = torch.mean(visual_change).item()
        refinement_metrics['text_change_max'] = torch.max(text_change).item()
        refinement_metrics['visual_change_max'] = torch.max(visual_change).item()
        refinement_metrics['nonlinear_weight_mean'] = torch.mean(redundancy_weight).item()
        refinement_metrics['nonlinear_weight_max'] = torch.max(redundancy_weight).item()

        return text_refined, visual_refined, refinement_metrics

    elif strategy == 'adaptive':
        # Adaptive refinement: use different weights for different dimensions
        # Calculate feature-wise correlation
        text_norm = text_encoded / (torch.norm(text_encoded, dim=1, keepdim=True) + 1e-8)
        visual_norm = visual_encoded / (torch.norm(visual_encoded, dim=1, keepdim=True) + 1e-8)
        correlation = torch.sum(text_norm * visual_norm, dim=1, keepdim=True)

        # Use correlation as adaptive weight
        adaptive_weight = correlation * redundancy_scores
        text_refined = text_encoded - adaptive_weight * visual_encoded
        visual_refined = visual_encoded - adaptive_weight * text_encoded

        # Calculate refinement magnitude
        text_change = torch.norm(text_refined - text_encoded, dim=1)
        visual_change = torch.norm(visual_refined - visual_encoded, dim=1)

        refinement_metrics['text_change_mean'] = torch.mean(text_change).item()
        refinement_metrics['visual_change_mean'] = torch.mean(visual_change).item()
        refinement_metrics['text_change_max'] = torch.max(text_change).item()
        refinement_metrics['visual_change_max'] = torch.max(visual_change).item()
        refinement_metrics['correlation_mean'] = torch.mean(correlation).item()
        refinement_metrics['adaptive_weight_mean'] = torch.mean(adaptive_weight).item()

        return text_refined, visual_refined, refinement_metrics

    else:
        raise ValueError(f"Unknown refinement strategy: {strategy}")

def evaluate_with_refinement(model, data_loader, device, strategy='none', alpha=0.5, threshold=0.5):
    """
    Evaluate model with different refinement strategies.

    Args:
        model (torch.nn.Module): Model to evaluate
        data_loader (torch.utils.data.DataLoader): Data loader
        device (torch.device): Device to use
        strategy (str): Refinement strategy
        alpha (float): Weight for refinement
        threshold (float): Threshold for binary prediction

    Returns:
        dict: Evaluation metrics
    """
    model.eval()

    # Lists to store predictions and labels
    test_preds = []
    test_labels = []

    # Lists to store features
    all_text_encoded = []
    all_visual_encoded = []
    all_text_refined = []
    all_visual_refined = []
    all_redundancy_scores = []

    with torch.no_grad():
        for batch in tqdm(data_loader, desc=f"Testing ({strategy})"):
            # Move batch to device
            image = batch['image'].to(device)
            text = batch['text']
            labels = batch['labels'].to(device)
            kg_features = batch['kg_features'].to(device)
            label_embeddings = batch['label_embeddings'].to(device) if 'label_embeddings' in batch else None

            # Convert text to feature vectors
            text_features = torch.zeros(len(text), 300).to(device)  # 300-dim text features

            # Flatten image features if they are 4D
            if len(image.shape) == 4:
                batch_size, channels, height, width = image.shape
                image_features = image.view(batch_size, -1)
            else:
                image_features = image

            # Forward pass
            outputs = model(text_features, image_features, kg_features, label_embeddings)

            # Apply refinement
            text_encoded = outputs['text_encoded']
            visual_encoded = outputs['visual_encoded']
            redundancy_scores = outputs['redundancy_score']

            text_refined, visual_refined, refinement_stats = apply_refinement(
                text_encoded, visual_encoded, redundancy_scores, strategy, alpha
            )

            # Use refined features for prediction if not 'none' strategy
            if strategy != 'none':
                # Create a new forward pass with refined features
                kg_encoded = outputs['kg_encoded']

                # Use the adaptive fusion module with refined features
                fused = model.adaptive_fusion(
                    text_refined,
                    visual_refined,
                    kg_encoded,
                    redundancy_scores
                )

                # Pass through the enhanced classifier
                logits = model.enhanced_classifier(fused)
            else:
                logits = outputs['logits']

            # Collect predictions and labels
            test_preds.append(torch.sigmoid(logits).detach().cpu().numpy())
            test_labels.append(labels.detach().cpu().numpy())

            # Collect features for disentanglement metrics
            all_text_encoded.append(text_encoded.detach().cpu().numpy())
            all_visual_encoded.append(visual_encoded.detach().cpu().numpy())
            all_redundancy_scores.append(redundancy_scores.detach().cpu().numpy())
            all_text_refined.append(text_refined.detach().cpu().numpy())
            all_visual_refined.append(visual_refined.detach().cpu().numpy())

    # Stack all predictions and labels
    test_preds = np.vstack(test_preds)
    test_labels = np.vstack(test_labels)

    # Stack all features
    all_text_encoded = np.vstack(all_text_encoded)
    all_visual_encoded = np.vstack(all_visual_encoded)
    all_redundancy_scores = np.vstack(all_redundancy_scores)
    all_text_refined = np.vstack(all_text_refined)
    all_visual_refined = np.vstack(all_visual_refined)

    # Compute standard and enhanced metrics
    standard_metrics = compute_metrics(test_labels, test_preds)
    enhanced_metrics = compute_enhanced_metrics(test_labels, test_preds, threshold)

    # Compute disentanglement metrics with refined features
    disentanglement_metrics = compute_disentanglement_metrics(
        all_text_encoded, all_visual_encoded, all_redundancy_scores,
        all_text_refined, all_visual_refined
    )

    # Add refinement strategy information
    refinement_info = {
        'refinement_strategy': strategy,
        'refinement_alpha': alpha
    }

    # Combine all metrics
    all_metrics = {
        **standard_metrics,
        **enhanced_metrics,
        **disentanglement_metrics,
        **refinement_info,
        **refinement_stats  # Add the refinement statistics
    }

    return all_metrics

def visualize_comparison(results, output_dir):
    """
    Visualize comparison of refinement strategies.

    Args:
        results (dict): Dictionary of results for each strategy
        output_dir (str): Output directory
    """
    os.makedirs(output_dir, exist_ok=True)

    # Extract refinement effect metrics
    refinement_metrics = [
        # Disentanglement metrics
        'modality_disentanglement_score',
        'cross_modal_redundancy',
        'feature_independence',
        'text_specificity',
        'visual_specificity',
        'modality_specificity',
        'shared_information_preservation',
        'mutual_information',
        'text_to_image_transfer',
        'image_to_text_transfer',
        'redundancy_min',
        'redundancy_max',
        'redundancy_std',

        # Refinement effect metrics
        'text_refinement_magnitude',
        'visual_refinement_magnitude',
        'text_redundancy_effect',
        'visual_redundancy_effect',
        'refined_feature_independence',
        'independence_improvement',
        'refined_text_specificity',
        'refined_visual_specificity',
        'refined_modality_specificity',
        'text_specificity_improvement',
        'visual_specificity_improvement',

        # Additional refinement statistics
        'text_change_mean',
        'visual_change_mean',
        'text_change_max',
        'visual_change_max'
    ]

    # Create dataframe for refinement metrics
    refinement_data = []
    for strategy, metrics in results.items():
        for metric in refinement_metrics:
            if metric in metrics:
                refinement_data.append({
                    'Strategy': strategy,
                    'Metric': metric,
                    'Value': metrics[metric]
                })

    refinement_df = pd.DataFrame(refinement_data)

    # Create bar chart for refinement metrics
    plt.figure(figsize=(15, 10))
    ax = sns.barplot(x='Metric', y='Value', hue='Strategy', data=refinement_df)

    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Refinement Effect Metrics Comparison', fontsize=18, pad=20)

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)

    # Add legend
    plt.legend(title='Strategy', fontsize=12, title_fontsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'refinement_metrics_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # Extract classification metrics
    classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP']

    # Create dataframe for classification metrics
    classification_data = []
    for strategy, metrics in results.items():
        for metric in classification_metrics:
            if metric in metrics:
                classification_data.append({
                    'Strategy': strategy,
                    'Metric': metric,
                    'Value': metrics[metric]
                })

    classification_df = pd.DataFrame(classification_data)

    # Create bar chart for classification metrics
    plt.figure(figsize=(15, 10))
    ax = sns.barplot(x='Metric', y='Value', hue='Strategy', data=classification_df)

    # Set labels and title
    plt.xlabel('Metric', fontsize=14, labelpad=10)
    plt.ylabel('Value', fontsize=14, labelpad=10)
    plt.title('Classification Metrics Comparison', fontsize=18, pad=20)

    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right', fontsize=12)

    # Add legend
    plt.legend(title='Strategy', fontsize=12, title_fontsize=14)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'classification_metrics_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")

    # Create test dataset
    logger.info("Creating test dataset...")
    test_dataset = MMIMDBDataset(
        data_path=args.data_path,
        kg_path=args.kg_path,
        mode='test'
    )

    # Create data loader
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Create model
    logger.info("Creating model...")
    model = KGDisentangleNet(
        text_dim=args.text_dim,
        visual_dim=args.visual_dim,
        kg_dim=args.kg_dim,
        hidden_dim=args.hidden_dim,
        num_classes=args.num_classes
    ).to(device)

    # Load model weights
    logger.info(f"Loading model from {args.model_path}...")
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model.eval()

    # Run evaluation with different refinement strategies
    results = {}
    for strategy in args.refinement_strategies:
        logger.info(f"Evaluating with {strategy} refinement...")
        metrics = evaluate_with_refinement(
            model, test_loader, device, strategy, alpha=0.5, threshold=args.threshold
        )
        results[strategy] = metrics

        # Log refinement effect metrics
        logger.info(f"\nRefinement Effect Metrics ({strategy}):")
        if 'text_refinement_magnitude' in metrics:
            logger.info(f"Text Refinement Magnitude: {metrics['text_refinement_magnitude']:.4f}")
            logger.info(f"Visual Refinement Magnitude: {metrics['visual_refinement_magnitude']:.4f}")
            logger.info(f"Text Redundancy Effect: {metrics['text_redundancy_effect']:.4f}")
            logger.info(f"Visual Redundancy Effect: {metrics['visual_redundancy_effect']:.4f}")
            logger.info(f"Refined Feature Independence: {metrics['refined_feature_independence']:.4f}")
            logger.info(f"Independence Improvement: {metrics['independence_improvement']:.4f}")

        # Log classification metrics
        logger.info(f"\nClassification Metrics ({strategy}):")
        logger.info(f"F1 (Samples): {metrics['f1']:.4f}")
        logger.info(f"F1-Micro: {metrics['f1_micro']:.4f}")
        logger.info(f"F1-Macro: {metrics['f1_macro']:.4f}")
        logger.info(f"Precision: {metrics['precision']:.4f}")
        logger.info(f"Recall: {metrics['recall']:.4f}")
        logger.info(f"mAP: {metrics['mAP']:.4f}")

    # Create comparison table for disentanglement and refinement metrics
    disentanglement_metrics = [
        'modality_disentanglement_score',
        'cross_modal_redundancy',
        'feature_independence',
        'modality_specificity',
        'shared_information_preservation',
        'mutual_information',
        'text_to_image_transfer',
        'image_to_text_transfer'
    ]

    refinement_metrics = [
        'text_refinement_magnitude',
        'visual_refinement_magnitude',
        'text_redundancy_effect',
        'visual_redundancy_effect',
        'refined_feature_independence',
        'independence_improvement',
        'refined_modality_specificity',
        'text_specificity_improvement',
        'visual_specificity_improvement'
    ]

    # Additional refinement statistics
    refinement_stats = [
        'text_change_mean',
        'visual_change_mean',
        'text_change_max',
        'visual_change_max'
    ]

    # Create table for disentanglement metrics
    disentanglement_table = []
    headers = ['Metric'] + args.refinement_strategies

    for metric in disentanglement_metrics:
        row = [metric]
        for strategy in args.refinement_strategies:
            if metric in results[strategy]:
                row.append(f"{results[strategy][metric]:.4f}")
            else:
                row.append("N/A")
        disentanglement_table.append(row)

    # Print disentanglement metrics comparison table
    logger.info("\nDisentanglement Metrics Comparison:")
    logger.info("\n" + tabulate(disentanglement_table, headers=headers, tablefmt='grid'))

    # Create table for refinement effect metrics
    refinement_table = []

    for metric in refinement_metrics:
        row = [metric]
        for strategy in args.refinement_strategies:
            if metric in results[strategy]:
                row.append(f"{results[strategy][metric]:.4f}")
            else:
                row.append("N/A")
        refinement_table.append(row)

    # Print refinement effect metrics comparison table
    logger.info("\nRefinement Effect Metrics Comparison:")
    logger.info("\n" + tabulate(refinement_table, headers=headers, tablefmt='grid'))

    # Create table for additional refinement statistics
    stats_table = []

    for metric in refinement_stats:
        row = [metric]
        for strategy in args.refinement_strategies:
            if metric in results[strategy]:
                row.append(f"{results[strategy][metric]:.4f}")
            else:
                row.append("N/A")
        stats_table.append(row)

    # Print additional refinement statistics comparison table
    logger.info("\nAdditional Refinement Statistics Comparison:")
    logger.info("\n" + tabulate(stats_table, headers=headers, tablefmt='grid'))

    # Create comparison table for classification metrics
    classification_metrics = ['f1', 'f1_micro', 'f1_macro', 'precision', 'recall', 'mAP']

    table_data = []

    for metric in classification_metrics:
        row = [metric]
        for strategy in args.refinement_strategies:
            if metric in results[strategy]:
                row.append(f"{results[strategy][metric]:.4f}")
            else:
                row.append("N/A")
        table_data.append(row)

    # Print comparison table
    logger.info("\nClassification Metrics Comparison:")
    logger.info("\n" + tabulate(table_data, headers=headers, tablefmt='grid'))

    # Save results to JSON
    for strategy, metrics in results.items():
        # Convert numpy values to Python native types for JSON serialization
        json_safe_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, np.ndarray):
                json_safe_metrics[key] = value.tolist()
            elif isinstance(value, np.floating):
                json_safe_metrics[key] = float(value)
            elif isinstance(value, np.integer):
                json_safe_metrics[key] = int(value)
            elif value is np.nan:
                json_safe_metrics[key] = None
            else:
                json_safe_metrics[key] = value

        with open(os.path.join(args.output_dir, f'{strategy}_results.json'), 'w') as f:
            json.dump(json_safe_metrics, f, indent=2)

    # Visualize comparison
    logger.info("Creating visualizations...")
    visualize_comparison(results, args.output_dir)

    logger.info(f"Results saved to {args.output_dir}")

if __name__ == '__main__':
    main()
